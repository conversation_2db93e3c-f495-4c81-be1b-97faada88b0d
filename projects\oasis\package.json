{"name": "@hg-tech/forgeon", "type": "module", "version": "1.0.8", "private": true, "packageManager": "pnpm@9.6.0", "homepage": "https://gitlab.hypergryph.net/tech/qaengineering/home/<USER>", "scripts": {"dev": "vite", "build": "cross-env NODE_ENV=production vite build", "build:rnd": "cross-env NODE_ENV=production vite build --mode rnd", "build:pre": "cross-env NODE_ENV=production vite build --mode pre", "report": "cross-env REPORT=true NODE_OPTIONS=--max-old-space-size=10240 npm run build", "test:type": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "dependencies": {"@ant-design/icons-vue": "catalog:", "@hg-tech/oasis-common": "workspace:*", "@hg-tech/request-api": "workspace:*", "@hg-tech/utils": "workspace:*", "@hg-tech/utils-vue": "workspace:*", "@hg/event-log": "catalog:", "@icon-park/vue-next": "catalog:", "@logicflow/core": "catalog:", "@logicflow/extension": "catalog:", "@micro-zoe/micro-app": "catalog:", "@sentry/vue": "catalog:", "@tweenjs/tween.js": "catalog:", "@vue/runtime-core": "catalog:", "@vue/shared": "catalog:", "@vueuse/components": "catalog:", "@vueuse/core": "catalog:", "@vueuse/router": "catalog:", "@vueuse/shared": "catalog:", "@zxcvbn-ts/core": "catalog:", "ant-design-vue": "catalog:", "axios": "catalog:", "bytes": "catalog:", "canvas-confetti": "catalog:", "codemirror": "catalog:", "cronstrue": "catalog:", "cropperjs": "catalog:", "crypto-js": "catalog:", "dayjs": "catalog:", "echarts": "catalog:", "exceljs": "catalog:", "filesize": "catalog:", "gsap": "catalog:", "highlight.js": "catalog:", "intro.js": "catalog:", "jmuxer": "catalog:", "js-cookie": "catalog:", "jsoneditor": "catalog:", "lodash": "catalog:", "lodash-es": "catalog:", "miao-vuefinder": "catalog:", "nprogress": "catalog:", "path-to-regexp": "catalog:", "pinia": "catalog:", "pinyin-pro": "catalog:", "print-js": "catalog:", "qrcode": "catalog:", "qs": "catalog:", "resize-observer-polyfill": "catalog:", "showdown": "catalog:", "sortablejs": "catalog:", "spark-md5": "catalog:", "splitpanes": "catalog:", "three": "catalog:", "ts-pattern": "catalog:", "uuid": "catalog:", "v-code-diff": "catalog:", "vditor": "catalog:", "version-rocket": "catalog:", "vite-svg-loader": "catalog:", "vue": "catalog:", "vue-json-pretty": "catalog:", "vue-request": "catalog:", "vue-router": "catalog:", "vue-tippy": "catalog:", "vue-types": "catalog:", "vxe-pc-ui": "catalog:", "vxe-table": "catalog:", "vxe-table-plugin-export-xlsx": "catalog:", "xe-utils": "catalog:", "xlsx": "catalog:"}, "devDependencies": {"@hg-tech/configs": "workspace:^", "@hg-tech/forgeon-style": "workspace:^", "@hg-tech/forgeon-uno-config": "workspace:^", "@iconify-icons/ant-design": "catalog:", "@iconify-icons/icon-park-outline": "catalog:", "@iconify-icons/icon-park-solid": "catalog:", "@iconify/json": "catalog:", "@iconify/vue": "catalog:", "@types/bytes": "catalog:", "@types/canvas-confetti": "catalog:", "@types/codemirror": "catalog:", "@types/crypto-js": "catalog:", "@types/inquirer": "catalog:", "@types/intro.js": "catalog:", "@types/jmuxer": "catalog:", "@types/js-cookie": "catalog:", "@types/lodash": "catalog:", "@types/lodash-es": "catalog:", "@types/nprogress": "catalog:", "@types/qrcode": "catalog:", "@types/qs": "catalog:", "@types/showdown": "catalog:", "@types/sortablejs": "catalog:", "@types/splitpanes": "catalog:", "@types/uuid": "catalog:", "@vitejs/plugin-vue": "catalog:", "@vitejs/plugin-vue-jsx": "catalog:", "@vue/compiler-sfc": "catalog:", "@vue/test-utils": "catalog:", "autoprefixer": "catalog:", "conventional-changelog-cli": "catalog:", "cross-env": "catalog:", "cspell": "catalog:", "dotenv": "catalog:", "inquirer": "catalog:", "less": "catalog:", "npm-run-all": "catalog:", "picocolors": "catalog:", "pkg-types": "catalog:", "postcss": "catalog:", "postcss-html": "catalog:", "postcss-less": "catalog:", "rimraf": "catalog:", "tippy.js": "catalog:", "ts-node": "catalog:", "typescript": "catalog:", "unocss": "catalog:", "unplugin-vue-setup-extend-plus": "catalog:", "vite": "catalog:", "vite-bundle-analyzer": "catalog:", "vite-plugin-compression": "catalog:", "vite-plugin-html": "catalog:", "vite-plugin-mkcert": "catalog:", "vite-plugin-static-copy": "catalog:", "vite-plugin-svg-icons": "catalog:", "vite-plugin-vue-devtools": "catalog:", "vite-plugin-vue-setup-extend": "catalog:", "vue-tsc": "catalog:"}}