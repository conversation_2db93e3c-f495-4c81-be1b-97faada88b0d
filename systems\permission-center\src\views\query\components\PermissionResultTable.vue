<template>
  <div class="h-full flex flex-col">
    <div v-if="!data?.length && !loading" class="h-full flex flex-col items-center justify-center c-FO-Content-Text3">
      <Empty description="请选择查询条件" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
    </div>
    <div v-else-if="loading" class="h-full flex flex-col items-center justify-center c-FO-Content-Text3">
      <Spin size="large" />
      查询中...
    </div>

    <div v-else-if="data.length === 0" class="h-full flex flex-col items-center justify-center c-FO-Content-Text3">
      <Empty description="无匹配项" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
    </div>

    <BasicVxeTable
      v-else
      ref="tableRef"
      class="flex-1"
      :options="gridOptions"
      :cssVarOverrides="{ '--vxe-ui-layout-background-color': ForgeonThemeCssVar.ContainerFill1 }"
    />
  </div>
</template>

<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { Empty, Spin, Tooltip } from 'ant-design-vue';
import type { VxeGridProps } from 'vxe-table';
import { BasicVxeTable, PlatformEnterPoint } from '@hg-tech/oasis-common';
import { useLatestPromise } from '@hg-tech/utils-vue';
import type { GetPermissionMembersRequest } from '../../../api/query.ts';
import type { PermissionCategory, QueryPermissionRow } from '../permission.ts';
import type { SysUserInfo } from '../../../api/users.ts';
import { OrgStructureType } from '../../../api/group.ts';
import { getLdapGroupDetail } from '../../../api/ldap.ts';
import { useRouter } from 'vue-router';
import { useConcatKey } from '../../../composables/useConcatKey.ts';
import ArrowDownIcon from '../../../assets/icons/SystemStrokeArrowDown.svg?component';
import DepartmentIcon from '../../../assets/icons/SystemStrokeOrganization.svg?component';
import GroupIcon from '../../../assets/icons/SystemStrokeGroup.svg?component';
import MemberCountDisplay from '../../app/components/MemberCountDisplay.vue';
import MemberPopover from '../../app/components/member/MemberPopover.vue';
import { ForgeonThemeCssVar } from '@hg-tech/forgeon-style';

const props = defineProps<{
  data: QueryPermissionRow[];
  loading: boolean;
  queryParams?: GetPermissionMembersRequest;
  categories?: PermissionCategory[];
  isMultiTenant?: boolean;
}>();

const router = useRouter();

const { getConcatKey } = useConcatKey();

// VXE Table 引用
const tableRef = ref();

// 监听数据变化，重新计算表格高度
watch(
  () => props.data,
  async () => {
    if (tableRef.value && props.data?.length) {
      tableRef.value.recalculate?.();
    }
  },
  { flush: 'post' },
);

// 成员数据缓存
const memberCache = ref<Record<string, SysUserInfo[]>>({});

// 获取自定义组成员
const { execute: fetchGroupMembers, loading: loadingGroupMembers } = useLatestPromise(getLdapGroupDetail);

// 获取成员数据的函数
async function fetchMemberData(row: QueryPermissionRow) {
  if (row.type !== 'permission' || !row.orgType || !row.resourceId) {
    return [];
  }

  try {
    const id = row.resourceId;
    const type = row.orgType;

    // 获取自定义组或部门成员
    const res = await fetchGroupMembers({
      no: id,
      include_outsource: type === OrgStructureType.OutsourceDepartment,
    }, {});
    const members = res?.data?.data?.member || [];

    // 缓存结果
    memberCache.value[getConcatKey(type, id)] = members;
    return members;
  } catch (error) {
    console.error('获取成员数据失败:', error);
    return [];
  }
}

// 获取缓存的成员数据
function getCachedMembers(row: QueryPermissionRow): SysUserInfo[] {
  if (row.type !== 'permission' || !row.orgType || !row.resourceId) {
    return [];
  }

  return memberCache.value[getConcatKey(row.orgType, row.resourceId)] || [];
}

// 判断是否是组织类型（部门或自定义组）
function isGroupType(row: QueryPermissionRow): boolean {
  if (row.type !== 'permission' || !row.orgType) {
    return false;
  }

  return [
    OrgStructureType.CustomGroup,
    OrgStructureType.Department,
    OrgStructureType.OutsourceDepartment,
  ].includes(row.orgType);
}

const gridOptions = computed((): VxeGridProps => {
  // 多租户模式：树形结构
  if (props.isMultiTenant) {
    return {
      data: props.data,
      height: 'auto',
      rowConfig: { keyField: 'uniqueKey', isHover: true },
      columnConfig: { resizable: false },
      treeConfig: {
        transform: true,
        rowField: 'uniqueKey',
        parentField: 'parentUniqueKey',
        expandAll: true,
      },
      columns: [
        { field: 'blank', title: '', width: 30 },
        {
          field: 'name',
          title: '权限下的成员',
          width: 300,
          treeNode: true,
          slots: {
            default: ({ row }) => {
              if (row.type === 'category') {
                return (
                  <div class="flex items-center gap-2">
                    <span class="FO-Font-B14 c-FO-Content-Text1">{row.name}</span>
                    <MemberCountDisplay memberCount={row.memberCountInfo} />
                  </div>
                );
              }
              return renderMemberCell(row);
            },
          },
        },
        {
          field: 'description',
          title: '类型',
          width: 120,
          filters: getMemberTypeFilters(),
          slots: {
            default: ({ row }) => (row.type === 'category'
              ? ''
              : (
                <span class="FO-Font-R14 c-FO-Content-Text2">{row.description}</span>
              )),
          },
        },
        {
          field: 'roles',
          title: '来源角色',
          minWidth: 200,
          slots: {
            default: ({ row }) => (row.type === 'category' ? '' : renderRolesCell(row)),
          },
        },
      ],
    };
  }

  // 非多租户模式：平铺结构
  return {
    data: props.data,
    height: 'auto',
    rowConfig: { keyField: 'uniqueKey', isHover: true },
    columnConfig: { resizable: false },
    columns: [
      {
        field: 'name',
        title: '成员名称',
        width: 300,
        slots: {
          default: ({ row }) => renderMemberCell(row),
        },
      },
      {
        field: 'description',
        title: '类型',
        width: 120,
        filters: getMemberTypeFilters(),
        slots: {
          default: ({ row }) => (
            <span class="FO-Font-R14 c-FO-Content-Text2">{row.description}</span>
          ),
        },
      },
      {
        field: 'roles',
        title: '来源角色',
        minWidth: 200,
        slots: {
          default: ({ row }) => renderRolesCell(row),
        },
      },
    ],
  };
});

// 获取成员类型过滤器
function getMemberTypeFilters() {
  return [
    { label: '干员', value: '干员' },
    { label: '干员(外包)', value: '干员(外包)' },
    { label: '部门', value: '部门' },
    { label: '部门(含外包)', value: '部门(含外包)' },
    { label: '自定义组', value: '自定义组' },
  ];
}

// 渲染成员单元格
function renderMemberCell(row: QueryPermissionRow) {
  return (
    <div class="flex items-center gap-8px">
      <span class="FO-Font-R14 c-FO-Content-Text1">{row.name}</span>
      {isGroupType(row) && (
        <Tooltip title="查看组内成员">
          <MemberPopover
            loading={loadingGroupMembers.value}
            members={getCachedMembers(row)}
            onClick={() => fetchMemberData(row)}
            title={`包含成员 ${getCachedMembers(row).length} 名`}
          >
            {(row.type === 'permission' && 'orgType' in row
              && (row.orgType === OrgStructureType.Department || row.orgType === OrgStructureType.OutsourceDepartment))
              ? <DepartmentIcon class="cursor-pointer c-FO-Content-Icon3" />
              : <GroupIcon class="cursor-pointer c-FO-Content-Icon3" />}
          </MemberPopover>
        </Tooltip>
      )}
    </div>
  );
}

// 渲染角色单元格
function renderRolesCell(row: QueryPermissionRow) {
  // 类型守卫，确保是 permission 类型才有 roles
  if (row.type !== 'permission' || !('roles' in row) || !row.roles || row.roles.length === 0) {
    return <span class="c-FO-Content-Text3">暂无角色</span>;
  }

  return (
    <div class="flex flex-wrap gap-1">
      {row.roles.map((role: any, index: number) => (
        <Tooltip key={index} title="前往配置角色">
          <div
            class="flex cursor-pointer items-center gap-2px border border-FO-Container-Stroke1 rounded-md bg-FO-Container-Fill4 px-2 py-1 hover:bg-FO-Container-Fill3"
            onClick={() => handleGotoRoleConfig(role)}
          >
            <span class="FO-Font-B14 c-FO-Content-Text2">
              {role.tenant ? `${role.tenant}: ${role.name}` : role.name}
            </span>
            <ArrowDownIcon class="c-FO-Content-Icon2 -rotate-90" />
          </div>
        </Tooltip>
      ))}
    </div>
  );
}

function handleGotoRoleConfig(role: any) {
  const appId = props.queryParams?.appId;
  if (!appId) {
    return;
  }

  const { fullPath } = router.resolve({
    name: PlatformEnterPoint.PermissionCenterApp,
    params: { appId },
    query: {
      tab: 'member',
      roleId: role.id,
      tenantId: role?.tenantId,
    },
  });
  window.open(location.origin + fullPath);
}
</script>
