{"name": "@hg-tech/permission-center", "type": "module", "version": "1.0.2", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "build:rnd": "cross-env NODE_ENV=production vite build --mode rnd", "build:pre": "cross-env NODE_ENV=production vite build --mode pre", "build:analyze": "vite build -- --analyze", "lint": "eslint", "lint:fix": "eslint --fix", "test": "run-p test:*", "test:type": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "dependencies": {"@ant-design/icons-vue": "catalog:", "@hg-tech/forgeon-style": "workspace:*", "@hg-tech/forgeon-uno-config": "workspace:^", "@hg-tech/oasis-common": "workspace:*", "@hg-tech/request-api": "workspace:*", "@hg-tech/utils-vue": "workspace:*", "@micro-zoe/micro-app": "catalog:", "@tanstack/vue-virtual": "catalog:", "@vueuse/core": "catalog:", "@vueuse/integrations": "catalog:", "@vueuse/router": "catalog:", "@vueuse/shared": "catalog:", "ant-design-vue": "catalog:", "dayjs": "catalog:", "lodash": "catalog:", "pinia": "catalog:", "pinyin-pro": "catalog:", "sortablejs": "catalog:", "uuid": "catalog:", "vue": "catalog:", "vue-router": "catalog:", "vxe-pc-ui": "catalog:", "vxe-table": "catalog:"}, "devDependencies": {"@hg-tech/configs": "workspace:^", "@types/lodash": "catalog:", "@types/sortablejs": "catalog:", "@types/uuid": "catalog:", "cross-env": "catalog:", "unocss": "catalog:", "unplugin-vue-inspector": "catalog:", "vite": "catalog:", "vite-svg-loader": "catalog:", "vue-tsc": "catalog:"}}