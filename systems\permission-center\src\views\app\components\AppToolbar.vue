<template>
  <div class="b-rd-[12px] bg-FO-Container-Fill1 p-[16px]">
    <div class="mb-16px flex items-center justify-between">
      <div class="flex items-center">
        <img
          v-if="appInfo?.icon" :src="appInfo?.icon"
          class="mr-12px h-32px w-32px overflow-hidden rounded-2xl object-cover"
        >
        <img v-else :src="AvatarIcon" class="mr-12px size-[32px]">
        <EllipsisText class="FO-Font-B20 mr-8px">
          {{ appInfo?.name || '-' }}
        </EllipsisText>
        <div v-if="appInfo?.isMultiTenant" class="b-rd-6px bg-FO-Datavis-Blue3 px-8px py-2px c-FO-Datavis-Blue1">
          项目
        </div>
      </div>
      <div v-if="hasPermissionToEditApp" class="flex items-center gap-12px">
        <Button class="btn-fill-secondary px-12px" @click="handleGoToPermissionQuery">
          权限查询
        </Button>
        <Dropdown placement="bottomRight">
          <Button class="btn-fill-secondary px-12px">
            应用管理
            <ArrowDownIcon />
          </Button>
          <template #overlay>
            <Menu>
              <MenuItem @click="handleEditApp">
                <div class="flex items-center gap-1">
                  <FillEditIcon />
                  编辑应用
                </div>
              </MenuItem>
              <MenuItem @click="handleGoToEditPermission">
                <div class="flex items-center gap-1">
                  <FillPermissionIcon />
                  编辑应用权限
                </div>
              </MenuItem>
              <MenuItem v-if="appInfo?.isMultiTenant" @click="handleEditAdmins">
                <div class="flex items-center gap-1">
                  <FillPermissionIcon />
                  项目管理员配置
                </div>
              </MenuItem>
              <MenuItem @click="handleDeleteApp">
                <div class="flex items-center gap-1">
                  <FillDeleteIcon />
                  删除应用
                </div>
              </MenuItem>
            </Menu>
          </template>
        </Dropdown>
      </div>
    </div>
    <div class="flex gap-16px b-rd-8px bg-FO-Container-Fill2 px-16px py-12px">
      <div class="w-0 flex flex-auto items-center">
        <span class="FO-Font-B14 whitespace-nowrap">应用 Code：</span>
        <EllipsisText class="mr-2px c-FO-Content-Text2">
          {{ appInfo?.code || '-' }}
        </EllipsisText>
        <CopyIcon
          v-if="appInfo?.code" class="cursor-pointer c-FO-Content-Text2"
          @click="() => handleCopyText(appInfo?.code)"
        />
      </div>
      <div class="w-0 flex flex-auto items-center">
        <span class="FO-Font-B14 whitespace-nowrap">应用描述：</span>
        <EllipsisText class="c-FO-Content-Text2">
          {{ appInfo?.description || '-' }}
        </EllipsisText>
      </div>
      <div v-if="hasPermissionToEditApp" class="w-0 flex flex-auto items-center">
        <span class="FO-Font-B14 whitespace-nowrap">应用凭证：</span>
        <EllipsisText class="mr-2px c-FO-Content-Text2">
          {{ appInfo?.secret || '-' }}
        </EllipsisText>
        <CopyIcon
          v-if="appInfo?.secret" class="cursor-pointer c-FO-Content-Text2"
          @click="() => handleCopyText(appInfo?.secret)"
        />
      </div>
      <div class="w-0 flex flex-auto items-center">
        <span class="FO-Font-B14 whitespace-nowrap">应用管理员：</span>
        <AvatarList :avatarList="appInfo?.member?.admin" />
      </div>
    </div>
    <AdminDrawerHolder />
    <AppFormDrawerHolder />
  </div>
</template>

<script setup lang="tsx">
import FillEditIcon from '../../../assets/icons/SystemStrokeEdit.svg?component';
import FillDeleteIcon from '../../../assets/icons/SystemStrokeDelete.svg?component';
import FillPermissionIcon from '../../../assets/icons/SystemStrokePermission.svg?component';
import WarningErrorIcon from '../../../assets/icons/SystemFillWarning.svg?component';
import CopyIcon from '../../../assets/icons/SystemStrokeCopy.svg?component';
import ArrowDownIcon from '../../../assets/icons/SystemStrokeArrowDown.svg?component';
import { Button, Dropdown, Menu, MenuItem, message, Modal } from 'ant-design-vue';
import type { PermissionAppInfo } from '../../../api/app.ts';
import { useClipboard } from '@vueuse/core';
import { EllipsisText, PlatformEnterPoint } from '@hg-tech/oasis-common';
import { useRoute, useRouter } from 'vue-router';
import { toRef } from 'vue';
import { useUserRole } from '../../../composables/useUserRole.ts';
import { useModalShow } from '@hg-tech/utils-vue';
import AppAdminDrawer from './AppAdminDrawer.vue';
import AppFormDrawer from './AppFormDrawer.vue';
import { deletePermissionApp, updatePermissionApp } from '../../../api/app.ts';
import AvatarList from './AvatarList.tsx';
import AvatarIcon from '../../../assets/icons/avatar.svg?url';
import { useRouteQuery } from '@vueuse/router';

const props = defineProps<{
  appId?: PermissionAppInfo['id'];
  appInfo?: PermissionAppInfo;
  loading?: boolean;
}>();

const emit = defineEmits<{
  updated: [];
}>();

const router = useRouter();
const route = useRoute();
const tenantId = useRouteQuery('tenantId');

const { hasPermissionToEditApp } = useUserRole(toRef(props, 'appId'));

async function handleCopyText(text?: string) {
  if (text) {
    const { copy } = useClipboard();
    await copy(text);
    message.success('复制成功');
  }
}

function handleGoToPermissionQuery() {
  router.push({
    name: PlatformEnterPoint.PermissionCenterQuery,
    query: {
      appId: props.appId,
      tenantId: tenantId.value,
    },
  });
}

function handleGoToEditPermission() {
  router.push({
    name: PlatformEnterPoint.PermissionCenterManagement,
    params: {
      appId: props.appId,
    },
    query: {
      tenantId: tenantId.value,
    },
  });
}

const [AppFormDrawerHolder, show] = useModalShow(AppFormDrawer);

async function handleEditApp() {
  await show({
    title: '编辑应用信息',
    appInfo: props.appInfo,
    async sentReq(formValue: any) {
      try {
        const res = await updatePermissionApp({ appId: props.appId }, formValue);
        if (res.data?.code === 0) {
          emit('updated');
          message.success('提交成功');
          return true;
        } else {
          return false;
        }
      } catch (error) {
        console.error(error);
        return false;
      }
    },
  });
}

const [AdminDrawerHolder, showAdminDrawer] = useModalShow(AppAdminDrawer);

async function handleEditAdmins() {
  await showAdminDrawer({
    appId: props.appId,
  });
}

async function handleDeleteApp() {
  Modal.confirm({
    icon: null,
    width: 496,
    okText: '删除',
    okButtonProps: {
      type: 'primary',
      danger: true,
    },
    cancelButtonProps: {
      // @ts-expect-error cancelButtonProps支持class但没有类型定义
      class: 'btn-fill-default',
    },
    centered: true,
    closable: true,
    title: () => {
      return (
        <div class="flex items-center">
          <WarningErrorIcon class="c-FO-Functional-Error1-Default" />
          <div class="FO-Font-B16 ml-8px">删除应用</div>
        </div>
      );
    },
    content() {
      return (
        <div class="mt-12px pb-8px c-FO-Content-Text2">
          删除应用【{ props.appInfo?.name }】，应用下的所有权限配置将被删除，此操作不可恢复，请谨慎操作。
        </div>
      );
    },
    onOk: async () => {
      try {
        const res = await deletePermissionApp({ appId: props.appId }, {});
        if (res.data?.code === 0) {
          message.success('操作成功');
          router.replace({
            name: PlatformEnterPoint.PermissionCenterDashboard,
            query: {
              tab: route.query.fromTab,
            },
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
  });
}
</script>
