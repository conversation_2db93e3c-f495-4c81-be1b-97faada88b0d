<template>
  <div class="h-full min-h-0 flex flex-col gap-12px">
    <div class="px-16px">
      <Input v-model:value="searchKw" class="w-full" placeholder="搜索角色" allowClear>
        <template #prefix>
          <SearchIcon />
        </template>
      </Input>
    </div>
    <div class="flex-auto overflow-y-auto">
      <Spin :spinning="loading">
        <div v-show="allGroups?.length" ref="sortableContainer" class="flex flex-col gap-[4px]">
          <div
            v-for="group in allGroups" :key="group.id" :data-id="group.id"
            class="group relative flex cursor-pointer items-center" @click="() => handleActiveGroup(group)"
          >
            <DragIcon
              class="drag-handle cursor-grab opacity-0 transition-300 hover:c-FO-Brand-Primary-Default group-hover:opacity-100"
              :class="{ invisible: searchKw || !hasPermissionToEditGroup }"
            />
            <div
              class="mr-[16px] min-w-0 flex flex-1 items-center gap-8px rounded-8px py-10px pl-8px pr-12px transition-300 group-hover:bg-FO-Container-Fill2"
              :class="{ '!bg-FO-Brand-Tertiary-Active': group.id === activeGroup }"
            >
              <div class="min-w-0 flex flex-auto items-center gap-8px">
                <GroupIcon
                  class="c-FO-Content-Icon3"
                  :class="{ '!c-FO-Brand-Primary-Default': group.id === activeGroup }"
                />

                <EllipsisText
                  class="c-FO-Content-Text1"
                  :class="{ '!c-FO-Brand-Primary-Default': group.id === activeGroup }"
                >
                  {{ group.name }}
                </EllipsisText>
                <div
                  v-if="group.isPublic"
                  class="FO-Font-R12 flex-shrink-0 whitespace-nowrap rounded-8px bg-FO-Datavis-Teal3 px-4px py-1px c-FO-Datavis-Teal1"
                >
                  公共
                </div>
              </div>
              <div v-if="hasPermissionToEditApp || (hasPermissionToEditGroup && !group.isPublic)" class="flex items-center gap-4px opacity-0 transition-300 group-hover:opacity-100">
                <Dropdown :trigger="['click']" placement="bottomRight" @click.stop>
                  <Button
                    :class="group.id === activeGroup ? 'btn-fill-secondary' : 'btn-fill-text'" size="small"
                    class="!px-5px" @click.stop
                  >
                    <MoreIcon />
                  </Button>
                  <template #overlay>
                    <Menu>
                      <MenuItem @click="() => handleEditGroup(group)">
                        <div class="flex items-center gap-4px">
                          <EditIcon />
                          修改名称
                        </div>
                      </MenuItem>
                      <MenuItem v-if="(appInfo?.isMultiTenant && !group.isPublic) || !appInfo?.isMultiTenant" @click="() => handleCopyGroup(group)">
                        <div class="flex items-center gap-4px">
                          <CopyIcon />
                          复制角色
                        </div>
                      </MenuItem>
                      <MenuItem v-if="appInfo?.isMultiTenant && hasPermissionToEditApp" @click="() => handleSetPublicGroup(group)">
                        <div class="flex items-center gap-4px">
                          <component :is="group.isPublic ? ComRoleOffIcon : ComRoleOnIcon" />
                          {{ group.isPublic ? '解除公共角色' : '设为公共角色' }}
                        </div>
                      </MenuItem>
                      <MenuItem v-if="(appInfo?.isMultiTenant && !group.isPublic) || !appInfo?.isMultiTenant" @click="() => handleDeleteGroup(group)">
                        <div class="flex items-center gap-4px">
                          <DeleteIcon />
                          删除角色
                        </div>
                      </MenuItem>
                    </Menu>
                  </template>
                </Dropdown>
              </div>
            </div>
          </div>
        </div>
        <Empty
          v-if="!allGroups?.length" class="!mt-[36px]" :description="searchKw ? `无匹配项` : `暂无数据`"
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
        />
      </Spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PermissionAppGroupDetail, PermissionAppGroupListItem } from '../../../api/group.ts';
import { Button, Dropdown, Empty, Input, Menu, MenuItem, Spin } from 'ant-design-vue';
import { computed, ref, watch, withDefaults } from 'vue';
import { useSortable } from '@vueuse/integrations/useSortable';
import GroupIcon from '../../../assets/icons/SystemFillUserGroup.svg?component';
import MoreIcon from '../../../assets/icons/SystemStrokeMore.svg?component';
import EditIcon from '../../../assets/icons/SystemStrokeEdit.svg?component';
import DeleteIcon from '../../../assets/icons/SystemStrokeDelete.svg?component';
import SearchIcon from '../../../assets/icons/SystemStrokeSearch.svg?component';
import CopyIcon from '../../../assets/icons/SystemStrokeCopy.svg?component';
import DragIcon from '../../../assets/icons/SystemStrokeDrag.svg?component';
import ComRoleOnIcon from '../../../assets/icons/SystemStrokeComRoleOn.svg?component';
import ComRoleOffIcon from '../../../assets/icons/SystemStrokeComRoleOff.svg?component';
import type { SortableEvent } from 'sortablejs';
import { EllipsisText } from '@hg-tech/oasis-common';
import { useUserRole } from '../../../composables/useUserRole.ts';
import type { PermissionAppInfo } from '../../../api/app.ts';

const props = withDefaults(defineProps<{
  allGroups?: PermissionAppGroupListItem[];
  loading?: boolean;
  appInfo?: PermissionAppInfo;
}>(), {
  allGroups: () => [],
  loading: false,
});

const emit = defineEmits<{
  editGroup: [group: PermissionAppGroupListItem];
  deleteGroup: [group: PermissionAppGroupListItem];
  copyGroup: [group: PermissionAppGroupListItem];
  sortGroup: [oldIndex: number, newIndex: number];
  search: [keyword: string | undefined];
  setPublicGroup: [group: PermissionAppGroupListItem];
}>();

const searchKw = ref<string>();
const activeGroup = defineModel<PermissionAppGroupDetail['id']>('activeGroup');
const sortableContainer = ref<HTMLElement>();

const sortableList = ref(props.allGroups);

const appId = computed(() => props.appInfo?.id);
const { hasPermissionToEditGroup, hasPermissionToEditApp } = useUserRole(appId);

useSortable(sortableContainer, sortableList, {
  handle: '.drag-handle',
  animation: 300,
  direction: 'vertical',
  onEnd: async (evt: SortableEvent) => {
    // 搜索时不处理拖拽
    if (searchKw.value) {
      return;
    }

    const { oldIndex, newIndex } = evt;
    if (oldIndex === newIndex || oldIndex === undefined || newIndex === undefined) {
      return;
    }
    handleSort(oldIndex, newIndex);
  },
});

// 监听搜索关键词变化，通知父组件
watch(searchKw, (keyword) => {
  emit('search', keyword);
});

watch(() => props.allGroups, (groups) => {
  sortableList.value = groups || [];
  if (!groups?.length) {
    activeGroup.value = undefined;
  } else if (!groups.some((i) => i.id === activeGroup.value)) {
    activeGroup.value = groups[0].id;
  }
}, { immediate: true });

function handleActiveGroup(group: PermissionAppGroupDetail) {
  activeGroup.value = group.id;
}

function handleEditGroup(group: PermissionAppGroupListItem) {
  emit('editGroup', group);
}

function handleDeleteGroup(group: PermissionAppGroupListItem) {
  emit('deleteGroup', group);
}

function handleCopyGroup(group: PermissionAppGroupListItem) {
  emit('copyGroup', group);
}

function handleSetPublicGroup(group: PermissionAppGroupListItem) {
  emit('setPublicGroup', group);
}

// 处理排序
function handleSort(oldIndex: number, newIndex: number) {
  // 通知父组件处理排序
  emit('sortGroup', oldIndex, newIndex);
}
</script>
