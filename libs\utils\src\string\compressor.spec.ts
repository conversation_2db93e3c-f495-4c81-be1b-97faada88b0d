import { getEncodePrefix, isSafeEncoded, safeDecode, safeEncode, tryDecompressUntilRawString } from './compressor';
import { describe, expect, it, vi } from 'vitest';

describe('safeEncode', () => {
  it('should throw warning and return empty str for no-string or empty input', () => {
    const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
    expect(safeEncode('')).toBe('');
    expect(safeEncode(123 as unknown as string)).toBe('');
    expect(safeEncode(null as unknown as string)).toBe('');
    expect(safeEncode(undefined as unknown as string)).toBe('');
    expect(safeEncode({} as unknown as string)).toBe('');
    expect(safeEncode([] as unknown as string)).toBe('');
    expect(safeEncode(true as unknown as string)).toBe('');
    expect(safeEncode(Symbol('test') as unknown as string)).toBe('');
    expect(safeEncode(BigInt(123) as unknown as string)).toBe('');
    expect(safeEncode(new Date() as unknown as string)).toBe('');
    expect(safeEncode(new Map() as unknown as string)).toBe('');
    expect(safeEncode(new Set() as unknown as string)).toBe('');
    expect(consoleWarnSpy).toHaveBeenCalledWith(expect.stringContaining('[safeEncode] input must be a no-empty string'));
    consoleWarnSpy.mockRestore();
  });

  it('should return fallback for invalid input', () => {
    expect(safeEncode(null as unknown as string, { fallback: 'default value' })).toBe('default value');
  });

  it('should return the compressed string', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded = safeEncode(input);
    expect(encoded).not.toBe(input);
  });

  it('with prefix should return the compressed string', () => {
    const input = '/68a72a78c60db1b19f7562aa?query=1&bb=cc,css';
    const encoded = safeEncode(input);
    expect(encoded).not.toBe(input);
  });

  it('with prefix should return the compressed string with prefix', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded = safeEncode(input, { prefix: 'salt' });
    expect(encoded).not.toBe(input);
  });

  it('default avoidDoubleEncode should prevent double safeEncode', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded1 = safeEncode(input);
    const encoded2 = safeEncode(encoded1);
    expect(encoded2).toBe(encoded1);
  });

  it('avoidDoubleEncode is false should double safeEncode', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded1 = safeEncode(input);
    const encoded2 = safeEncode(encoded1, { avoidDoubleEncode: false });
    expect(encoded2).not.toBe(encoded1);
  });
});

describe('safeDecode', () => {
  it('should throw warning and return empty str for no-string or empty input', () => {
    const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
    expect(safeDecode('')).toBe('');
    expect(safeDecode(123 as unknown as string)).toBe('');
    expect(safeDecode(null as unknown as string)).toBe('');
    expect(safeDecode(undefined as unknown as string)).toBe('');
    expect(safeDecode({} as unknown as string)).toBe('');
    expect(safeDecode([] as unknown as string)).toBe('');
    expect(safeDecode(true as unknown as string)).toBe('');
    expect(safeDecode(Symbol('test') as unknown as string)).toBe('');
    expect(safeDecode(BigInt(123) as unknown as string)).toBe('');
    expect(safeDecode(new Date() as unknown as string)).toBe('');
    expect(safeDecode(new Map() as unknown as string)).toBe('');
    expect(safeDecode(new Set() as unknown as string)).toBe('');
    expect(consoleWarnSpy).toHaveBeenCalledWith(expect.stringContaining('[safeDecode] type of maybeEncodedPath is no-empty string'));
    consoleWarnSpy.mockRestore();
  });

  it('should return empty str for invalid encoded strings without fallback', () => {
    const input = 'invalid-encoded-string';
    const decoded = safeDecode(input);
    expect(decoded).toBe('');
  });

  it('should return the input for invalid encoded strings with fallback', () => {
    const input = 'invalid-encoded-string';
    const decoded = safeDecode(input, { fallback: input });
    expect(decoded).toBe(input);
  });

  it('should throw error for invalid encoded strings with avoidNoEncodedError is false', () => {
    expect(() => safeDecode('invalid-encoded-string', { avoidNoEncodedError: false }))
      .toThrowError('[safeDecode] No safe encoded input: invalid-encoded-string');
  });

  it('should throw error for invalid encoded strings with avoidDifferentPrefixError is false', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded = safeEncode(input, { prefix: 'salt' });
    expect(() => safeDecode(encoded, { prefix: 'wrong-salt', avoidDifferentPrefixError: false }))
      .toThrowError(`Prefix mismatch: input string encoded with "salt", but decode prefix is "wrong-salt"`);
  });

  it('should return the original string for valid encoded strings', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded = safeEncode(input);
    const decoded = safeDecode(encoded);
    expect(decoded).toBe(input);
  });

  it('should return the original string for valid encoded strings with url included "::"', () => {
    const input = 'https://example.com/path/to/resource?query=string::with::colons';
    const encoded = safeEncode(input);
    const decoded = safeDecode(encoded);
    expect(decoded).toBe(input);
  });

  it('should return the original string for valid encoded strings with prefix', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded = safeEncode(input, { prefix: 'salt' });
    const decoded = safeDecode(encoded, { prefix: 'salt' });
    expect(decoded).toBe(input);
  });

  it('should not return the input for valid encoded strings with different prefix and fallback', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded = safeEncode(input, { prefix: 'salt' });
    const decoded = safeDecode(encoded, { prefix: 'wrong-salt', fallback: encoded });
    expect(decoded).toBe(encoded);
  });

  it('should not return the input for valid encoded strings with different prefix but no fallback', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded = safeEncode(input, { prefix: 'salt' });
    const decoded = safeDecode(encoded, { prefix: 'wrong-salt' });
    expect(decoded).toBe('');
  });

  it('avoidDoubleEncode should prevent double safeEncode', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded1 = safeEncode(input);
    const encoded2 = safeEncode(encoded1);
    expect(encoded2).toBe(encoded1);
  });

  it('avoidDoubleEncode should allow double safeEncode with avoidDoubleEncode: false', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded1 = safeEncode(input);
    const encoded2 = safeEncode(encoded1, { avoidDoubleEncode: false });
    expect(encoded2).not.toBe(encoded1);
  });

  it('debug mode should log warning message', () => {
    const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded = safeEncode(input, { prefix: 'salt' });
    const decoded = safeDecode(encoded, { prefix: 'wrong-salt' });
    expect(decoded).toBe('');
    expect(consoleWarnSpy).toHaveBeenCalledWith(
      `[safeDecode] Prefix mismatch: input string encoded with "salt", but decode prefix is "wrong-salt"`,
    );
    consoleWarnSpy.mockRestore();
  });

  it('safe decode with emoji', () => {
    const input = '😊🌟🚀';
    const encoded = safeEncode(input);
    const decoded = safeDecode(encoded);
    expect(decoded).toBe(input);
  });

  it('safe decode with regex', () => {
    const input = `/([\w-:*>]*)(?:#([\w-]+)|\.([\w-]+))?(?:\[@?(!?[\w-:]+)(?:([!*^$]?=)["']?(.*?)["']?)?\])?([/, ]+)/s`;
    const encoded = safeEncode(input);
    const decoded = safeDecode(encoded);
    expect(decoded).toBe(input);
  });

  it('safe decode with Unicode', () => {
    const input = '\uD83D\uDE00\uD83D\uDE02\uD83D\uDE05';
    const encoded = safeEncode(input);
    const decoded = safeDecode(encoded);
    expect(decoded).toBe(input);
  });

  it('safe decode with language', () => {
    const input1 = '𝒜𝓁𝓅𝒽𝒶';
    const input2 = 'こんにちは';
    const input3 = '안녕하세요';
    expect(safeDecode(safeEncode(input1))).toBe(input1);
    expect(safeDecode(safeEncode(input2))).toBe(input2);
    expect(safeDecode(safeEncode(input3))).toBe(input3);
  });
});

describe('isSafeEncoded', () => {
  it('should return false for non-string input', () => {
    const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
    expect(isSafeEncoded('')).toBe(false);
    expect(isSafeEncoded(123 as unknown as string)).toBe(false);
    expect(isSafeEncoded(null as unknown as string)).toBe(false);
    expect(isSafeEncoded(undefined as unknown as string)).toBe(false);
    expect(isSafeEncoded({} as unknown as string)).toBe(false);
    expect(isSafeEncoded([] as unknown as string)).toBe(false);
    expect(isSafeEncoded(true as unknown as string)).toBe(false);
    expect(isSafeEncoded(Symbol('test') as unknown as string)).toBe(false);
    expect(isSafeEncoded(BigInt(123) as unknown as string)).toBe(false);
    expect(isSafeEncoded(new Date() as unknown as string)).toBe(false);
    expect(consoleWarnSpy).toHaveBeenCalledWith(expect.stringContaining('[isSafeEncoded] input must be a no-empty string'));
    consoleWarnSpy.mockRestore();
  });

  it('should return false for invalid encoded strings', () => {
    expect(isSafeEncoded('invalid-encoded-string')).toBe(false);
  });

  it('should return true for valid encoded strings', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded = safeEncode(input);
    expect(isSafeEncoded(encoded)).toBe(true);
  });

  it('should return true for valid encoded strings with prefix', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded = safeEncode(input, { prefix: 'salt' });
    expect(isSafeEncoded(encoded, 'salt')).toBe(true);
  });

  it('should return false for valid encoded strings with different prefix', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded = safeEncode(input, { prefix: 'salt' });
    expect(isSafeEncoded(encoded, 'wrong-salt')).toBe(false);
  });
});

describe('getEncodePrefix', () => {
  it('should return undefined for non-string input', () => {
    const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
    expect(getEncodePrefix('')).toBe(undefined);
    expect(getEncodePrefix(123 as unknown as string)).toBe(undefined);
    expect(getEncodePrefix(null as unknown as string)).toBe(undefined);
    expect(getEncodePrefix(undefined as unknown as string)).toBe(undefined);
    expect(getEncodePrefix({} as unknown as string)).toBe(undefined);
    expect(getEncodePrefix([] as unknown as string)).toBe(undefined);
    expect(getEncodePrefix(true as unknown as string)).toBe(undefined);
    expect(getEncodePrefix(Symbol('test') as unknown as string)).toBe(undefined);
    expect(getEncodePrefix(BigInt(123) as unknown as string)).toBe(undefined);
    expect(getEncodePrefix(new Date() as unknown as string)).toBe(undefined);
    expect(consoleWarnSpy).toHaveBeenCalledWith(expect.stringContaining('[getEncodePrefix] input must be a no-empty string'));
    consoleWarnSpy.mockRestore();
  });

  it('should return undefined for invalid encoded strings', () => {
    const input = 'invalid-encoded-string';
    const prefix = getEncodePrefix(input);
    expect(prefix).toBe(undefined);
  });

  it('should return empty string for valid encoded strings without prefix', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded = safeEncode(input);
    const prefix = getEncodePrefix(encoded);
    expect(prefix).toBe('');
  });

  it('should return the correct prefix with salt', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded = safeEncode(input, { prefix: 'salt' });
    const prefix = getEncodePrefix(encoded);
    expect(prefix).toBe('salt');
  });
});

describe('tryDecompressUntilRawString', () => {
  it('should return the original string for valid encoded strings without prefix', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded = safeEncode(input);
    const encoded1 = safeEncode(encoded, { avoidDoubleEncode: false });
    const encoded2 = safeEncode(encoded1, { avoidDoubleEncode: false });
    const encoded3 = safeEncode(encoded2, { avoidDoubleEncode: false });
    const decompressed = tryDecompressUntilRawString(encoded3);
    expect(encoded).not.toBe(input);
    expect(encoded1).not.toBe(encoded);
    expect(encoded2).not.toBe(encoded1);
    expect(encoded3).not.toBe(encoded2);
    expect(decompressed).not.toBe(encoded3);
    expect(decompressed).toBe(input);
  });

  it('should return the original string for valid encoded strings with prefix', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded = safeEncode(input, { prefix: 'salt', avoidDoubleEncode: false });
    const encoded1 = safeEncode(encoded, { prefix: 'salt1', avoidDoubleEncode: false });
    const encoded2 = safeEncode(encoded1, { prefix: 'salt2', avoidDoubleEncode: false });
    const encoded3 = safeEncode(encoded2, { prefix: 'salt3', avoidDoubleEncode: false });
    const decompressed = tryDecompressUntilRawString(encoded3, ['salt', 'salt1', 'salt2', 'salt3']);
    expect(encoded).not.toBe(input);
    expect(encoded1).not.toBe(encoded);
    expect(encoded2).not.toBe(encoded1);
    expect(encoded3).not.toBe(encoded2);
    expect(decompressed).toBe(input);
  });

  it('should return the original string for valid encoded strings with different prefixes', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded = safeEncode(input, { prefix: 'salt' });
    const encoded1 = safeEncode(encoded, { prefix: 'salt2', avoidDoubleEncode: false });
    const encoded2 = safeEncode(encoded1);
    const encoded3 = safeEncode(encoded2, { avoidDoubleEncode: false });
    const decompressed = tryDecompressUntilRawString(encoded3, ['salt', 'salt2']);
    expect(encoded).not.toBe(input);
    expect(encoded1).not.toBe(encoded);
    expect(encoded2).not.toBe(encoded1);
    expect(encoded3).not.toBe(encoded2);
    expect(decompressed).toBe(input);
  });

  it('should return the original string for valid encoded strings without prefixes', () => {
    const input = 'https://example.com/path/to/resource?query=string';
    const encoded = safeEncode(input);
    const encoded1 = safeEncode(encoded, { avoidDoubleEncode: false });
    const encoded2 = safeEncode(encoded1, { avoidDoubleEncode: false });
    const encoded3 = safeEncode(encoded2, { avoidDoubleEncode: false });
    const decompressed = tryDecompressUntilRawString(encoded3);
    expect(encoded).not.toBe(input);
    expect(encoded1).not.toBe(encoded);
    expect(encoded2).not.toBe(encoded1);
    expect(encoded3).not.toBe(encoded2);
    expect(decompressed).toBe(input);
  });
});
