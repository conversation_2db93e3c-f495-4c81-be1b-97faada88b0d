{"name": "@hg-tech/forgeon-uno-config", "type": "module", "version": "2.0.2", "description": "forgeon unocss配置及预设", "repository": {"type": "git", "url": "*************************:tech/platform/hypergryph-tech-fe-mono.git"}, "exports": {".": {"types": "./es/index.d.ts", "default": "./es/index.js"}}, "main": "./es/index.js", "module": "./es/index.js", "types": "./es/index.d.ts", "files": ["./es", "CHANGELOG.md", "readme.md"], "scripts": {"build": "tsc"}, "peerDependencies": {"lodash-es": "^4", "unocss": "^0.62.4"}, "dependencies": {"@hg-tech/forgeon-style": "workspace:*"}, "devDependencies": {"@types/lodash-es": "catalog:", "@unocss/preset-mini": "catalog:", "lodash-es": "catalog:", "unocss": "catalog:"}}