<template>
  <Drawer
    :open="show"
    :width="800"
    :closable="false"
    :maskClosable="false"
    placement="right"
    :bodyStyle="{ padding: '24px' }"
    @afterOpenChange="(o) => !o && modalDestroy()"
    @close="() => modalCancel()"
  >
    <template #title>
      <span class="FO-Font-B18">已配置角色列表</span>
    </template>
    <template #extra>
      <Button class="btn-fill-text" @click="() => modalCancel()">
        <template #icon>
          <CloseIcon />
        </template>
      </Button>
    </template>

    <div class="h-full">
      <div v-if="tableData.length === 0" class="py-40px text-center">
        <Empty description="暂无角色数据" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
      </div>

      <BasicVxeTable
        v-else
        :options="gridOptions"
      />
    </div>
  </Drawer>
</template>

<script setup lang="tsx">
import { computed } from 'vue';
import { But<PERSON>, Drawer, Empty } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import type { GetUserPermissionsRequest } from '../../../api/query';
import type { ConfiguredRoleInfo } from '../permission';
import { BasicVxeTable, PlatformEnterPoint } from '@hg-tech/oasis-common';
import type { VxeGridProps } from 'vxe-table';
import CloseIcon from '../../../assets/icons/SystemStrokeClose.svg?component';
import ConfigIcon from '../../../assets/icons/SystemStrokeConfig.svg?component';

// VXE Table 行数据类型 (flat 结构)
interface ConfiguredRoleTableRow {
  uniqueKey: string;
  roleId: number;
  roleName: string;
  projectName?: string;
  members: string[];
  memberCount: number;
  originalData: ConfiguredRoleInfo;
  // 树形结构字段 (flat 结构)
  type?: 'project' | 'role';
  roleCount?: number;
  parentUniqueKey?: string;
}

interface Props extends ModalBaseProps {
  flatTableData?: ConfiguredRoleTableRow[];
  isMultiTenant?: boolean;
  queryParams?: GetUserPermissionsRequest;
}

const props = defineProps<Props>();

const router = useRouter();

// 直接使用传入的 flat 数据
const tableData = computed<ConfiguredRoleTableRow[]>(() => {
  return props.flatTableData || [];
});

// 使用传入的多租户标识
const isMultiTenant = computed(() => {
  return Boolean(props.isMultiTenant);
});

// VXE Table 列配置
const tableColumns = computed<VxeGridProps<ConfiguredRoleTableRow>['columns']>(() => [
  { field: 'blank', title: '', width: 30 },
  {
    field: 'roleName',
    title: '已配置角色',
    width: 200,
    // 多租户模式启用树形节点
    ...(isMultiTenant.value && { treeNode: true }),
    slots: {
      default({ row }: { row: ConfiguredRoleTableRow }) {
        // 项目节点的显示
        if (row.type === 'project') {
          return (
            <div class="flex items-center gap-3">
              <span class="FO-Font-B14 c-FO-Content-Text1">{row.roleName}</span>
              <span class="rounded bg-FO-Container-Fill2 px-2 py-1 text-12px c-FO-Content-Text1">
                {row.roleCount}
              </span>
            </div>
          );
        }
        // 角色节点的显示
        return <span class="FO-Font-R14 c-FO-Content-Text1">{row.roleName}</span>;
      },
    },
  },
  {
    field: 'members',
    title: '所属成员',
    slots: {
      default({ row }: { row: ConfiguredRoleTableRow }) {
        // 项目节点不显示成员
        if (row.type === 'project') {
          return <span />;
        }
        // 角色节点显示成员
        return (
          <div class="flex flex-wrap gap-1">
            {row.members.map((member, index) => (
              <span
                class="inline-flex items-center rounded bg-FO-Container-Fill4 px-2 text-sm text-FO-Content-Text2 font-semibold"
                key={index}
              >
                {member}
              </span>
            ))}
          </div>
        );
      },
    },
  },
  {
    field: 'actions',
    title: '操作',
    width: 120,
    slots: {
      default({ row }: { row: ConfiguredRoleTableRow }) {
        // 项目节点不显示操作按钮
        if (row.type === 'project') {
          return <span />;
        }
        // 角色节点显示配置按钮
        return (
          <Button
            class="btn-fill-text"
            onClick={() => handleConfigRole(row.originalData)}
            size="small"
          >
            <ConfigIcon />
            配置角色
          </Button>
        );
      },
    },
  },
]);

// VXE Table 配置
const gridOptions = computed<VxeGridProps<ConfiguredRoleTableRow>>(() => ({
  height: 'auto',
  rowConfig: {
    keyField: 'uniqueKey',
    isHover: true,
  },
  // 多租户模式启用树形结构
  ...(isMultiTenant.value && {
    treeConfig: {
      transform: true,
      rowField: 'uniqueKey',
      parentField: 'parentUniqueKey',
      expandAll: true,
    },
  }),
  columnConfig: {
    resizable: false,
  },
  columns: tableColumns.value,
  data: tableData.value,
}));

function handleConfigRole(role: ConfiguredRoleInfo) {
  const appId = props.queryParams?.appId;
  if (!appId) {
    console.warn('缺少应用ID，无法跳转到角色配置');
    return;
  }

  // 新页面打开角色配置页面
  const { fullPath } = router.resolve({
    name: PlatformEnterPoint.PermissionCenterApp,
    params: { appId: String(appId) },
    query: {
      tab: 'member',
      roleId: role.roleId,
      tenantId: role.tenantId,
    },
  });
  window.open(location.origin + fullPath);
}
</script>
