{"@focolorvariables": {"$collection_metadata": {"name": "FOColorVariables", "figmaId": "VariableCollectionId:619:3595", "modes": [{"key": "light", "name": "Light"}, {"key": "dark", "name": "Dark"}]}, "$fo": {"$brand": {"primary-default": {"$type": "color", "$value": "{@globalcolorpalette.$light.$violet.6}", "$description": "主品牌色，主题色，一级界面元素默认状态色", "$variable_metadata": {"name": "FO/Brand/Primary-Default", "figmaId": "VariableID:658:3948", "modes": {"light": "{@globalcolorpalette.$light.$violet.6}", "dark": "{@globalcolorpalette.$dark.$violet.6}"}}}, "primary-hover": {"$type": "color", "$value": "{@globalcolorpalette.$light.$violet.5}", "$description": "主品牌色，主题色，一级界面元素悬停状态色", "$variable_metadata": {"name": "FO/Brand/Primary-Hover", "figmaId": "VariableID:3049:34", "modes": {"light": "{@globalcolorpalette.$light.$violet.5}", "dark": "{@globalcolorpalette.$dark.$violet.5}"}}}, "primary-active": {"$type": "color", "$value": "{@globalcolorpalette.$light.$violet.7}", "$description": "主品牌色，主题色，一级界面元素激活状态色", "$variable_metadata": {"name": "FO/Brand/Primary-Active", "figmaId": "VariableID:3049:1197", "modes": {"light": "{@globalcolorpalette.$light.$violet.7}", "dark": "{@globalcolorpalette.$dark.$violet.4}"}}}, "primary-disabled": {"$type": "color", "$value": "{@globalcolorpalette.$light.$violet.3}", "$description": "主品牌色，主题色，一级界面元素禁用状态色", "$variable_metadata": {"name": "FO/Brand/Primary-Disabled", "figmaId": "VariableID:3049:1198", "modes": {"light": "{@globalcolorpalette.$light.$violet.3}", "dark": "{@globalcolorpalette.$dark.$violet.4}"}}}, "secondary-default": {"$type": "color", "$value": "{@globalcolorpalette.$light.$violet.1}", "$description": "主品牌色，主题色，二级界面元素默认状态色", "$variable_metadata": {"name": "FO/Brand/Secondary-Default", "figmaId": "VariableID:1101:14272", "modes": {"light": "{@globalcolorpalette.$light.$violet.1}", "dark": "{@globalcolorpalette.$dark.$violet.1}"}}}, "secondary-hover": {"$type": "color", "$value": "{@globalcolorpalette.$light.$violet.2}", "$description": "主品牌色，主题色，二级界面元素默认状态色", "$variable_metadata": {"name": "FO/Brand/Secondary-Hover", "figmaId": "VariableID:3049:1200", "modes": {"light": "{@globalcolorpalette.$light.$violet.2}", "dark": "{@globalcolorpalette.$dark.$violet.2}"}}}, "secondary-active": {"$type": "color", "$value": "{@globalcolorpalette.$light.$violet.3}", "$description": "主品牌色，主题色，二级界面元素默认状态色", "$variable_metadata": {"name": "FO/Brand/Secondary-Active", "figmaId": "VariableID:3049:1201", "modes": {"light": "{@globalcolorpalette.$light.$violet.3}", "dark": "{@globalcolorpalette.$dark.$violet.3}"}}}, "secondary-disabled": {"$type": "color", "$value": "{@globalcolorpalette.$light.$violet.1}", "$description": "主品牌色，主题色，二级界面元素默认状态色", "$variable_metadata": {"name": "FO/Brand/Secondary-Disabled", "figmaId": "VariableID:3049:1202", "modes": {"light": "{@globalcolorpalette.$light.$violet.1}", "dark": "{@globalcolorpalette.$dark.$violet.1}"}}}, "tertiary-active": {"$type": "color", "$value": "{@globalcolorpalette.$light.$violet.1}", "$description": "主品牌色，主题色，二级界面元素默认状态色", "$variable_metadata": {"name": "FO/Brand/Tertiary-Active", "figmaId": "VariableID:3137:47", "modes": {"light": "{@globalcolorpalette.$light.$violet.1}", "dark": "{@globalcolorpalette.$dark.$violet.1}"}}}}, "$functional": {"success1-default": {"$type": "color", "$value": "{@globalcolorpalette.$light.$green.6}", "$description": "成功状态标识色。同语义下可交互界面元素默认状态色", "$variable_metadata": {"name": "FO/Functional/Success1-De<PERSON>ult", "figmaId": "VariableID:3049:1203", "modes": {"light": "{@globalcolorpalette.$light.$green.6}", "dark": "{@globalcolorpalette.$dark.$green.6}"}}}, "success1-hover": {"$type": "color", "$value": "{@globalcolorpalette.$light.$green.5}", "$description": "成功状态标识色。同语义下可交互界面元素悬停状态色", "$variable_metadata": {"name": "FO/Functional/Success1-Hover", "figmaId": "VariableID:3049:1204", "modes": {"light": "{@globalcolorpalette.$light.$green.5}", "dark": "{@globalcolorpalette.$dark.$green.5}"}}}, "success1-active": {"$type": "color", "$value": "{@globalcolorpalette.$light.$green.7}", "$description": "成功状态标识色。同语义下可交互界面元素激活状态色", "$variable_metadata": {"name": "FO/Functional/Success1-Active", "figmaId": "VariableID:3049:1205", "modes": {"light": "{@globalcolorpalette.$light.$green.7}", "dark": "{@globalcolorpalette.$dark.$green.4}"}}}, "success1-disabled": {"$type": "color", "$value": "{@globalcolorpalette.$light.$green.3}", "$description": "成功状态标识色。同语义下可交互界面元素禁用状态色", "$variable_metadata": {"name": "FO/Functional/Success1-Disabled", "figmaId": "VariableID:3049:1206", "modes": {"light": "{@globalcolorpalette.$light.$green.3}", "dark": "{@globalcolorpalette.$dark.$green.2}"}}}, "success2-default": {"$type": "color", "$value": "{@globalcolorpalette.$light.$green.1}", "$description": "成功状态标识色，浅色版本。同语义下可交互界面元素默认状态色", "$variable_metadata": {"name": "FO/Functional/Success2-Default", "figmaId": "VariableID:3049:1236", "modes": {"light": "{@globalcolorpalette.$light.$green.1}", "dark": "{@globalcolorpalette.$dark.$green.2}"}}}, "success2-hover": {"$type": "color", "$value": "{@globalcolorpalette.$light.$green.2}", "$description": "成功状态标识色，浅色版本。同语义下可交互界面元素悬停状态色", "$variable_metadata": {"name": "FO/Functional/Success2-Hover", "figmaId": "VariableID:3049:1235", "modes": {"light": "{@globalcolorpalette.$light.$green.2}", "dark": "{@globalcolorpalette.$dark.$green.3}"}}}, "success2-active": {"$type": "color", "$value": "{@globalcolorpalette.$light.$green.3}", "$description": "成功状态标识色，浅色版本。同语义下可交互界面元素激活状态色", "$variable_metadata": {"name": "FO/Functional/Success2-Active", "figmaId": "VariableID:3049:1234", "modes": {"light": "{@globalcolorpalette.$light.$green.3}", "dark": "{@globalcolorpalette.$dark.$green.4}"}}}, "success2-disabled": {"$type": "color", "$value": "{@globalcolorpalette.$light.$green.1}", "$description": "成功状态标识色，浅色版本。同语义下可交互界面元素禁用状态色", "$variable_metadata": {"name": "FO/Functional/Success2-Disabled", "figmaId": "VariableID:3049:1233", "modes": {"light": "{@globalcolorpalette.$light.$green.1}", "dark": "{@globalcolorpalette.$dark.$green.2}"}}}, "warning1-default": {"$type": "color", "$value": "{@globalcolorpalette.$light.$orange.6}", "$description": "警示状态标识色。同语义下可交互界面元素默认状态色", "$variable_metadata": {"name": "FO/Functional/Warning1-Default", "figmaId": "VariableID:3049:1223", "modes": {"light": "{@globalcolorpalette.$light.$orange.6}", "dark": "{@globalcolorpalette.$dark.$orange.6}"}}}, "warning1-hover": {"$type": "color", "$value": "{@globalcolorpalette.$light.$orange.5}", "$description": "警示状态标识色。同语义下可交互界面元素悬停状态色", "$variable_metadata": {"name": "FO/Functional/Warning1-Hover", "figmaId": "VariableID:3050:2", "modes": {"light": "{@globalcolorpalette.$light.$orange.5}", "dark": "{@globalcolorpalette.$dark.$orange.5}"}}}, "warning1-active": {"$type": "color", "$value": "{@globalcolorpalette.$light.$orange.7}", "$description": "警示状态标识色。同语义下可交互界面元素激活状态色", "$variable_metadata": {"name": "FO/Functional/Warning1-Active", "figmaId": "VariableID:3050:35", "modes": {"light": "{@globalcolorpalette.$light.$orange.7}", "dark": "{@globalcolorpalette.$dark.$orange.4}"}}}, "warning1-disabled": {"$type": "color", "$value": "{@globalcolorpalette.$light.$orange.3}", "$description": "警示状态标识色。同语义下可交互界面元素禁用状态色", "$variable_metadata": {"name": "FO/Functional/Warning1-Disabled", "figmaId": "VariableID:3050:36", "modes": {"light": "{@globalcolorpalette.$light.$orange.3}", "dark": "{@globalcolorpalette.$dark.$orange.2}"}}}, "warning2-default": {"$type": "color", "$value": "{@globalcolorpalette.$light.$orange.1}", "$description": "警示状态标识色，浅色版本。同语义下可交互界面元素默认状态色", "$variable_metadata": {"name": "FO/Functional/Warning2-Default", "figmaId": "VariableID:3050:47", "modes": {"light": "{@globalcolorpalette.$light.$orange.1}", "dark": "{@globalcolorpalette.$dark.$orange.1}"}}}, "warning2-hover": {"$type": "color", "$value": "{@globalcolorpalette.$light.$orange.2}", "$description": "警示状态标识色，浅色版本。同语义下可交互界面元素悬停状态色", "$variable_metadata": {"name": "FO/Functional/Warning2-Hover", "figmaId": "VariableID:3050:46", "modes": {"light": "{@globalcolorpalette.$light.$orange.2}", "dark": "{@globalcolorpalette.$dark.$orange.3}"}}}, "warning2-active": {"$type": "color", "$value": "{@globalcolorpalette.$light.$orange.3}", "$description": "警示状态标识色，浅色版本。同语义下可交互界面元素激活状态色", "$variable_metadata": {"name": "FO/Functional/Warning2-Active", "figmaId": "VariableID:3050:49", "modes": {"light": "{@globalcolorpalette.$light.$orange.3}", "dark": "{@globalcolorpalette.$dark.$orange.4}"}}}, "warning2-disabled": {"$type": "color", "$value": "{@globalcolorpalette.$light.$orange.1}", "$description": "警示状态标识色，浅色版本。同语义下可交互界面元素禁用状态色", "$variable_metadata": {"name": "FO/Functional/Warning2-Disabled", "figmaId": "VariableID:3050:48", "modes": {"light": "{@globalcolorpalette.$light.$orange.1}", "dark": "{@globalcolorpalette.$dark.$orange.2}"}}}, "error1-default": {"$type": "color", "$value": "{@globalcolorpalette.$light.$red.6}", "$description": "错误状态标识色。同语义下可交互界面元素默认状态色", "$variable_metadata": {"name": "FO/Functional/Error1-De<PERSON>ult", "figmaId": "VariableID:3050:53", "modes": {"light": "{@globalcolorpalette.$light.$red.6}", "dark": "{@globalcolorpalette.$dark.$red.6}"}}}, "error1-hover": {"$type": "color", "$value": "{@globalcolorpalette.$light.$red.5}", "$description": "错误状态标识色。同语义下可交互界面元素悬停状态色", "$variable_metadata": {"name": "FO/Functional/Error1-Hover", "figmaId": "VariableID:3050:57", "modes": {"light": "{@globalcolorpalette.$light.$red.5}", "dark": "{@globalcolorpalette.$dark.$red.5}"}}}, "error1-active": {"$type": "color", "$value": "{@globalcolorpalette.$light.$red.7}", "$description": "错误状态标识色。同语义下可交互界面元素激活状态色", "$variable_metadata": {"name": "FO/Functional/Error1-Active", "figmaId": "VariableID:3050:61", "modes": {"light": "{@globalcolorpalette.$light.$red.7}", "dark": "{@globalcolorpalette.$dark.$red.4}"}}}, "error1-disabled": {"$type": "color", "$value": "{@globalcolorpalette.$light.$red.3}", "$description": "错误状态标识色。同语义下可交互界面元素禁用状态色", "$variable_metadata": {"name": "FO/Functional/Error1-Disabled", "figmaId": "VariableID:3050:65", "modes": {"light": "{@globalcolorpalette.$light.$red.3}", "dark": "{@globalcolorpalette.$dark.$red.2}"}}}, "error2-default": {"$type": "color", "$value": "{@globalcolorpalette.$light.$red.1}", "$description": "错误状态标识色，浅色版本。同语义下可交互界面元素默认状态色", "$variable_metadata": {"name": "FO/Functional/Error2-De<PERSON>ult", "figmaId": "VariableID:3050:78", "modes": {"light": "{@globalcolorpalette.$light.$red.1}", "dark": "{@globalcolorpalette.$dark.$red.1}"}}}, "error2-hover": {"$type": "color", "$value": "{@globalcolorpalette.$light.$red.2}", "$description": "错误状态标识色，浅色版本。同语义下可交互界面元素默认状态色", "$variable_metadata": {"name": "FO/Functional/Error2-Hover", "figmaId": "VariableID:3050:79", "modes": {"light": "{@globalcolorpalette.$light.$red.2}", "dark": "{@globalcolorpalette.$dark.$red.3}"}}}, "error2-active": {"$type": "color", "$value": "{@globalcolorpalette.$light.$red.3}", "$description": "错误状态标识色，浅色版本。同语义下可交互界面元素默认状态色", "$variable_metadata": {"name": "FO/Functional/Error2-Active", "figmaId": "VariableID:3050:80", "modes": {"light": "{@globalcolorpalette.$light.$red.3}", "dark": "{@globalcolorpalette.$dark.$red.4}"}}}, "error2-disabled": {"$type": "color", "$value": "{@globalcolorpalette.$light.$red.1}", "$description": "错误状态标识色，浅色版本。同语义下可交互界面元素默认状态色", "$variable_metadata": {"name": "FO/Functional/Error2-Disabled", "figmaId": "VariableID:3050:81", "modes": {"light": "{@globalcolorpalette.$light.$red.1}", "dark": "{@globalcolorpalette.$dark.$red.2}"}}}, "info1-default": {"$type": "color", "$value": "{@globalcolorpalette.$light.$blue.6}", "$description": "信息提示状态标识色。同语义下可交互界面元素默认状态色", "$variable_metadata": {"name": "FO/Functional/Info1-Default", "figmaId": "VariableID:3050:70", "modes": {"light": "{@globalcolorpalette.$light.$blue.6}", "dark": "{@globalcolorpalette.$dark.$blue.6}"}}}, "info1-hover": {"$type": "color", "$value": "{@globalcolorpalette.$light.$blue.5}", "$description": "信息提示状态标识色。同语义下可交互界面元素悬停状态色", "$variable_metadata": {"name": "FO/Functional/Info1-Hover", "figmaId": "VariableID:3050:71", "modes": {"light": "{@globalcolorpalette.$light.$blue.5}", "dark": "{@globalcolorpalette.$dark.$blue.5}"}}}, "info1-active": {"$type": "color", "$value": "{@globalcolorpalette.$light.$blue.7}", "$description": "信息提示状态标识色。同语义下可交互界面元素激活状态色", "$variable_metadata": {"name": "FO/Functional/Info1-Active", "figmaId": "VariableID:3050:72", "modes": {"light": "{@globalcolorpalette.$light.$blue.7}", "dark": "{@globalcolorpalette.$dark.$blue.4}"}}}, "info1-disabled": {"$type": "color", "$value": "{@globalcolorpalette.$light.$blue.3}", "$description": "信息提示状态标识色。同语义下可交互界面元素禁用状态色", "$variable_metadata": {"name": "FO/Functional/Info1-Disabled", "figmaId": "VariableID:3050:73", "modes": {"light": "{@globalcolorpalette.$light.$blue.3}", "dark": "{@globalcolorpalette.$dark.$blue.2}"}}}, "info2-default": {"$type": "color", "$value": "{@globalcolorpalette.$light.$blue.1}", "$description": "信息提示状态标识色，浅色版本。同语义下可交互界面元素默认状态色", "$variable_metadata": {"name": "FO/Functional/Info2-Default", "figmaId": "VariableID:3050:74", "modes": {"light": "{@globalcolorpalette.$light.$blue.1}", "dark": "{@globalcolorpalette.$dark.$blue.1}"}}}, "info2-hover": {"$type": "color", "$value": "{@globalcolorpalette.$light.$blue.2}", "$description": "信息提示状态标识色，浅色版本。同语义下可交互界面元素悬停状态色", "$variable_metadata": {"name": "FO/Functional/Info2-Hover", "figmaId": "VariableID:3050:75", "modes": {"light": "{@globalcolorpalette.$light.$blue.2}", "dark": "{@globalcolorpalette.$dark.$blue.3}"}}}, "info2-active": {"$type": "color", "$value": "{@globalcolorpalette.$light.$blue.3}", "$description": "信息提示状态标识色，浅色版本。同语义下可交互界面元素激活状态色", "$variable_metadata": {"name": "FO/Functional/Info2-Active", "figmaId": "VariableID:3050:76", "modes": {"light": "{@globalcolorpalette.$light.$blue.3}", "dark": "{@globalcolorpalette.$dark.$blue.4}"}}}, "info2-disabled": {"$type": "color", "$value": "{@globalcolorpalette.$light.$blue.1}", "$description": "信息提示状态标识色，浅色版本。同语义下可交互界面元素禁用状态色", "$variable_metadata": {"name": "FO/Functional/Info2-Disabled", "figmaId": "VariableID:3050:77", "modes": {"light": "{@globalcolorpalette.$light.$blue.1}", "dark": "{@globalcolorpalette.$dark.$blue.1}"}}}}, "$content": {"text0": {"$type": "color", "$value": "{@globalcolorpalette.$basic.$white.100}", "$description": "", "$variable_metadata": {"name": "FO/Content/Text0", "figmaId": "VariableID:3069:77", "modes": {"light": "{@globalcolorpalette.$basic.$white.100}", "dark": "{@globalcolorpalette.$dark.$gray.1}"}}}, "text1": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.15}", "$description": "", "$variable_metadata": {"name": "FO/Content/Text1", "figmaId": "VariableID:3069:78", "modes": {"light": "{@globalcolorpalette.$light.$gray.15}", "dark": "{@globalcolorpalette.$dark.$gray.15}"}}}, "text2": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.11}", "$description": "", "$variable_metadata": {"name": "FO/Content/Text2", "figmaId": "VariableID:3069:79", "modes": {"light": "{@globalcolorpalette.$light.$gray.11}", "dark": "{@globalcolorpalette.$dark.$gray.10}"}}}, "text3": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.8}", "$description": "", "$variable_metadata": {"name": "FO/Content/Text3", "figmaId": "VariableID:3069:80", "modes": {"light": "{@globalcolorpalette.$light.$gray.8}", "dark": "{@globalcolorpalette.$dark.$gray.7}"}}}, "text4": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.6}", "$description": "", "$variable_metadata": {"name": "FO/Content/Text4", "figmaId": "VariableID:3069:81", "modes": {"light": "{@globalcolorpalette.$light.$gray.6}", "dark": "{@globalcolorpalette.$dark.$gray.5}"}}}, "icon0": {"$type": "color", "$value": "{@globalcolorpalette.$basic.$white.100}", "$description": "", "$variable_metadata": {"name": "FO/Content/Icon0", "figmaId": "VariableID:3089:43", "modes": {"light": "{@globalcolorpalette.$basic.$white.100}", "dark": "{@globalcolorpalette.$dark.$gray.1}"}}}, "icon1": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.14}", "$description": "", "$variable_metadata": {"name": "FO/Content/Icon1", "figmaId": "VariableID:3050:85", "modes": {"light": "{@globalcolorpalette.$light.$gray.14}", "dark": "{@globalcolorpalette.$dark.$gray.14}"}}}, "icon2": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.10}", "$description": "", "$variable_metadata": {"name": "FO/Content/Icon2", "figmaId": "VariableID:3089:40", "modes": {"light": "{@globalcolorpalette.$light.$gray.10}", "dark": "{@globalcolorpalette.$dark.$gray.9}"}}}, "icon3": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.7}", "$description": "", "$variable_metadata": {"name": "FO/Content/Icon3", "figmaId": "VariableID:3089:41", "modes": {"light": "{@globalcolorpalette.$light.$gray.7}", "dark": "{@globalcolorpalette.$dark.$gray.6}"}}}, "icon4": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.5}", "$description": "", "$variable_metadata": {"name": "FO/Content/Icon4", "figmaId": "VariableID:3089:42", "modes": {"light": "{@globalcolorpalette.$light.$gray.5}", "dark": "{@globalcolorpalette.$dark.$gray.5}"}}}, "components1": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.0}", "$description": "", "$variable_metadata": {"name": "FO/Content/Components1", "figmaId": "VariableID:3293:2530", "modes": {"light": "{@globalcolorpalette.$light.$gray.0}", "dark": "{@globalcolorpalette.$dark.$gray.16}"}}}, "components2": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.0}", "$description": "", "$variable_metadata": {"name": "FO/Content/Components2", "figmaId": "VariableID:3293:2490", "modes": {"light": "{@globalcolorpalette.$light.$gray.0}", "dark": "{@globalcolorpalette.$dark.$gray.11}"}}}, "link-default": {"$type": "color", "$value": "{@globalcolorpalette.$light.$blue.6}", "$description": "信息提示状态标识色。同语义下可交互界面元素默认状态色", "$variable_metadata": {"name": "FO/Content/Link-Default", "figmaId": "VariableID:3050:121", "modes": {"light": "{@globalcolorpalette.$light.$blue.6}", "dark": "{@globalcolorpalette.$dark.$blue.6}"}}}, "link-hover": {"$type": "color", "$value": "{@globalcolorpalette.$light.$blue.5}", "$description": "信息提示状态标识色。同语义下可交互界面元素悬停状态色", "$variable_metadata": {"name": "FO/Content/Link-Hover", "figmaId": "VariableID:3050:120", "modes": {"light": "{@globalcolorpalette.$light.$blue.5}", "dark": "{@globalcolorpalette.$dark.$blue.5}"}}}, "link-active": {"$type": "color", "$value": "{@globalcolorpalette.$light.$blue.7}", "$description": "信息提示状态标识色。同语义下可交互界面元素激活状态色", "$variable_metadata": {"name": "FO/Content/Link-Active", "figmaId": "VariableID:3050:119", "modes": {"light": "{@globalcolorpalette.$light.$blue.7}", "dark": "{@globalcolorpalette.$dark.$blue.4}"}}}, "link-disabled": {"$type": "color", "$value": "{@globalcolorpalette.$light.$blue.3}", "$description": "信息提示状态标识色。同语义下可交互界面元素禁用状态色", "$variable_metadata": {"name": "FO/Content/Link-Disabled", "figmaId": "VariableID:3050:118", "modes": {"light": "{@globalcolorpalette.$light.$blue.3}", "dark": "{@globalcolorpalette.$dark.$blue.2}"}}}}, "$container": {"mask0": {"$type": "color", "$value": "{@globalcolorpalette.$basic.$white.60}", "$description": "遮罩蒙层", "$variable_metadata": {"name": "FO/Container/Mask0", "figmaId": "VariableID:3884:11262", "modes": {"light": "{@globalcolorpalette.$basic.$white.60}", "dark": "{@globalcolorpalette.$basic.$white.60}"}}}, "mask1": {"$type": "color", "$value": "{@globalcolorpalette.$basic.$black.40}", "$description": "遮罩蒙层", "$variable_metadata": {"name": "FO/Container/Mask1", "figmaId": "VariableID:1200:14292", "modes": {"light": "{@globalcolorpalette.$basic.$black.40}", "dark": "{@globalcolorpalette.$basic.$black.40}"}}}, "background": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.1}", "$description": "页面最底层大背景", "$variable_metadata": {"name": "FO/Container/Background", "figmaId": "VariableID:621:3630", "modes": {"light": "{@globalcolorpalette.$light.$gray.1}", "dark": "{@globalcolorpalette.$dark.$gray.0}"}}}, "background2": {"$type": "color", "$value": "{@globalcolorpalette.$basic.$white.80}", "$description": "页面最底层大背景", "$variable_metadata": {"name": "FO/Container/Background2", "figmaId": "VariableID:4326:12805", "modes": {"light": "{@globalcolorpalette.$basic.$white.80}", "dark": "{@globalcolorpalette.$basic.$black.50}"}}}, "fill0": {"$type": "color", "$value": "{@globalcolorpalette.$basic.$white.0}", "$description": "一级容器背景", "$variable_metadata": {"name": "FO/Container/Fill0", "figmaId": "VariableID:3089:2975", "modes": {"light": "{@globalcolorpalette.$basic.$white.0}", "dark": "{@globalcolorpalette.$basic.$black.0}"}}}, "fill1": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.0}", "$description": "一级容器背景", "$variable_metadata": {"name": "FO/Container/Fill1", "figmaId": "VariableID:621:3631", "modes": {"light": "{@globalcolorpalette.$light.$gray.0}", "dark": "{@globalcolorpalette.$dark.$gray.1}"}}}, "fill2": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.1}", "$description": "一级容器背景", "$variable_metadata": {"name": "FO/Container/Fill2", "figmaId": "VariableID:3089:814", "modes": {"light": "{@globalcolorpalette.$light.$gray.1}", "dark": "{@globalcolorpalette.$dark.$gray.2}"}}}, "fill3": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.2}", "$description": "一级容器背景", "$variable_metadata": {"name": "FO/Container/Fill3", "figmaId": "VariableID:3090:7371", "modes": {"light": "{@globalcolorpalette.$light.$gray.2}", "dark": "{@globalcolorpalette.$dark.$gray.3}"}}}, "fill4": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.3}", "$description": "一级容器背景", "$variable_metadata": {"name": "FO/Container/Fill4", "figmaId": "VariableID:3184:17852", "modes": {"light": "{@globalcolorpalette.$light.$gray.3}", "dark": "{@globalcolorpalette.$dark.$gray.4}"}}}, "fill5": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.4}", "$description": "一级容器背景", "$variable_metadata": {"name": "FO/Container/Fill5", "figmaId": "VariableID:3184:17853", "modes": {"light": "{@globalcolorpalette.$light.$gray.4}", "dark": "{@globalcolorpalette.$dark.$gray.5}"}}}, "fill6": {"$type": "color", "$value": "{@globalcolorpalette.$dark.$gray.1}", "$description": "一级容器背景", "$variable_metadata": {"name": "FO/Container/Fill6", "figmaId": "VariableID:3340:1316", "modes": {"light": "{@globalcolorpalette.$dark.$gray.1}", "dark": "{@globalcolorpalette.$dark.$gray.0}"}}}, "stroke0": {"$type": "color", "$value": "{@globalcolorpalette.$basic.$white.100}", "$description": "灰色描边反色", "$variable_metadata": {"name": "FO/Container/Stroke0", "figmaId": "VariableID:3093:37", "modes": {"light": "{@globalcolorpalette.$basic.$white.100}", "dark": "{@globalcolorpalette.$dark.$gray.2}"}}}, "stroke1": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.3}", "$description": "分割线", "$variable_metadata": {"name": "FO/Container/Stroke1", "figmaId": "VariableID:3093:36", "modes": {"light": "{@globalcolorpalette.$light.$gray.3}", "dark": "{@globalcolorpalette.$dark.$gray.3}"}}}, "stroke2": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.4}", "$description": "灰色描边", "$variable_metadata": {"name": "FO/Container/Stroke2", "figmaId": "VariableID:3093:35", "modes": {"light": "{@globalcolorpalette.$light.$gray.4}", "dark": "{@globalcolorpalette.$dark.$gray.4}"}}}, "stroke3": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.5}", "$description": "灰色描边", "$variable_metadata": {"name": "FO/Container/Stroke3", "figmaId": "VariableID:3093:34", "modes": {"light": "{@globalcolorpalette.$light.$gray.5}", "dark": "{@globalcolorpalette.$dark.$gray.6}"}}}, "stroke4": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.6}", "$description": "灰色描边", "$variable_metadata": {"name": "FO/Container/Stroke4", "figmaId": "VariableID:3093:33", "modes": {"light": "{@globalcolorpalette.$light.$gray.6}", "dark": "{@globalcolorpalette.$dark.$gray.7}"}}}, "stroke5": {"$type": "color", "$value": "{@globalcolorpalette.$light.$gray.7}", "$description": "灰色描边", "$variable_metadata": {"name": "FO/Container/Stroke5", "figmaId": "VariableID:4674:19250", "modes": {"light": "{@globalcolorpalette.$light.$gray.7}", "dark": "{@globalcolorpalette.$dark.$gray.6}"}}}}, "$datavis": {"violet1": {"$type": "color", "$value": "{@globalcolorpalette.$light.$violet.6}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Violet1", "figmaId": "VariableID:3050:99", "modes": {"light": "{@globalcolorpalette.$light.$violet.6}", "dark": "{@globalcolorpalette.$dark.$violet.7}"}}}, "violet2": {"$type": "color", "$value": "{@globalcolorpalette.$light.$violet.4}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Violet2", "figmaId": "VariableID:3137:2", "modes": {"light": "{@globalcolorpalette.$light.$violet.4}", "dark": "{@globalcolorpalette.$dark.$violet.4}"}}}, "violet3": {"$type": "color", "$value": "{@globalcolorpalette.$light.$violet.1}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Violet3", "figmaId": "VariableID:3089:44", "modes": {"light": "{@globalcolorpalette.$light.$violet.1}", "dark": "{@globalcolorpalette.$dark.$violet.1}"}}}, "blue1": {"$type": "color", "$value": "{@globalcolorpalette.$light.$blue.6}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Blue1", "figmaId": "VariableID:3089:45", "modes": {"light": "{@globalcolorpalette.$light.$blue.6}", "dark": "{@globalcolorpalette.$dark.$blue.7}"}}}, "blue2": {"$type": "color", "$value": "{@globalcolorpalette.$light.$blue.4}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Blue2", "figmaId": "VariableID:3137:35", "modes": {"light": "{@globalcolorpalette.$light.$blue.4}", "dark": "{@globalcolorpalette.$dark.$blue.4}"}}}, "blue3": {"$type": "color", "$value": "{@globalcolorpalette.$light.$blue.1}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Blue3", "figmaId": "VariableID:3089:46", "modes": {"light": "{@globalcolorpalette.$light.$blue.1}", "dark": "{@globalcolorpalette.$dark.$blue.1}"}}}, "lightblue1": {"$type": "color", "$value": "{@globalcolorpalette.$light.$lightblue.7}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/LightBlue1", "figmaId": "VariableID:3089:47", "modes": {"light": "{@globalcolorpalette.$light.$lightblue.7}", "dark": "{@globalcolorpalette.$dark.$lightblue.6}"}}}, "lightblue2": {"$type": "color", "$value": "{@globalcolorpalette.$light.$lightblue.5}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/LightBlue2", "figmaId": "VariableID:3137:36", "modes": {"light": "{@globalcolorpalette.$light.$lightblue.5}", "dark": "{@globalcolorpalette.$dark.$lightblue.3}"}}}, "lightblue3": {"$type": "color", "$value": "{@globalcolorpalette.$light.$lightblue.1}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/LightBlue3", "figmaId": "VariableID:3089:48", "modes": {"light": "{@globalcolorpalette.$light.$lightblue.1}", "dark": "{@globalcolorpalette.$dark.$lightblue.1}"}}}, "teal1": {"$type": "color", "$value": "{@globalcolorpalette.$light.$teal.7}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Teal1", "figmaId": "VariableID:3089:49", "modes": {"light": "{@globalcolorpalette.$light.$teal.7}", "dark": "{@globalcolorpalette.$dark.$teal.6}"}}}, "teal2": {"$type": "color", "$value": "{@globalcolorpalette.$light.$teal.5}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Teal2", "figmaId": "VariableID:3137:37", "modes": {"light": "{@globalcolorpalette.$light.$teal.5}", "dark": "{@globalcolorpalette.$dark.$teal.3}"}}}, "teal3": {"$type": "color", "$value": "{@globalcolorpalette.$light.$teal.1}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Teal3", "figmaId": "VariableID:3089:50", "modes": {"light": "{@globalcolorpalette.$light.$teal.1}", "dark": "{@globalcolorpalette.$dark.$teal.1}"}}}, "green1": {"$type": "color", "$value": "{@globalcolorpalette.$light.$green.7}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Green1", "figmaId": "VariableID:3089:51", "modes": {"light": "{@globalcolorpalette.$light.$green.7}", "dark": "{@globalcolorpalette.$dark.$green.6}"}}}, "green2": {"$type": "color", "$value": "{@globalcolorpalette.$light.$green.4}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Green2", "figmaId": "VariableID:3137:38", "modes": {"light": "{@globalcolorpalette.$light.$green.4}", "dark": "{@globalcolorpalette.$dark.$green.3}"}}}, "green3": {"$type": "color", "$value": "{@globalcolorpalette.$light.$green.1}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Green3", "figmaId": "VariableID:3089:52", "modes": {"light": "{@globalcolorpalette.$light.$green.1}", "dark": "{@globalcolorpalette.$dark.$green.1}"}}}, "lightgreen1": {"$type": "color", "$value": "{@globalcolorpalette.$light.$lightgreen.7}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/LightGreen1", "figmaId": "VariableID:4192:6627", "modes": {"light": "{@globalcolorpalette.$light.$lightgreen.7}", "dark": "{@globalcolorpalette.$dark.$lightgreen.6}"}}}, "lightgreen2": {"$type": "color", "$value": "{@globalcolorpalette.$light.$lightgreen.4}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/LightGreen2", "figmaId": "VariableID:4192:6628", "modes": {"light": "{@globalcolorpalette.$light.$lightgreen.4}", "dark": "{@globalcolorpalette.$dark.$lightgreen.3}"}}}, "lightgreen3": {"$type": "color", "$value": "{@globalcolorpalette.$light.$lightgreen.1}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/LightGreen3", "figmaId": "VariableID:4192:6629", "modes": {"light": "{@globalcolorpalette.$light.$lightgreen.1}", "dark": "{@globalcolorpalette.$dark.$lightgreen.1}"}}}, "yellow1": {"$type": "color", "$value": "{@globalcolorpalette.$light.$yellow.8}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Yellow1", "figmaId": "VariableID:3089:53", "modes": {"light": "{@globalcolorpalette.$light.$yellow.8}", "dark": "{@globalcolorpalette.$dark.$yellow.5}"}}}, "yellow2": {"$type": "color", "$value": "{@globalcolorpalette.$light.$yellow.7}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Yellow2", "figmaId": "VariableID:3137:39", "modes": {"light": "{@globalcolorpalette.$light.$yellow.7}", "dark": "{@globalcolorpalette.$dark.$yellow.3}"}}}, "yellow3": {"$type": "color", "$value": "{@globalcolorpalette.$light.$yellow.1}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Yellow3", "figmaId": "VariableID:3089:54", "modes": {"light": "{@globalcolorpalette.$light.$yellow.1}", "dark": "{@globalcolorpalette.$dark.$yellow.1}"}}}, "orange1": {"$type": "color", "$value": "{@globalcolorpalette.$light.$orange.7}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Orange1", "figmaId": "VariableID:3089:55", "modes": {"light": "{@globalcolorpalette.$light.$orange.7}", "dark": "{@globalcolorpalette.$dark.$orange.6}"}}}, "orange2": {"$type": "color", "$value": "{@globalcolorpalette.$light.$orange.5}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Orange2", "figmaId": "VariableID:3137:41", "modes": {"light": "{@globalcolorpalette.$light.$orange.5}", "dark": "{@globalcolorpalette.$dark.$orange.4}"}}}, "orange3": {"$type": "color", "$value": "{@globalcolorpalette.$light.$orange.1}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Orange3", "figmaId": "VariableID:3089:56", "modes": {"light": "{@globalcolorpalette.$light.$orange.1}", "dark": "{@globalcolorpalette.$dark.$orange.1}"}}}, "red1": {"$type": "color", "$value": "{@globalcolorpalette.$light.$red.6}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Red1", "figmaId": "VariableID:3089:57", "modes": {"light": "{@globalcolorpalette.$light.$red.6}", "dark": "{@globalcolorpalette.$dark.$red.7}"}}}, "red2": {"$type": "color", "$value": "{@globalcolorpalette.$light.$red.4}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Red2", "figmaId": "VariableID:3137:42", "modes": {"light": "{@globalcolorpalette.$light.$red.4}", "dark": "{@globalcolorpalette.$dark.$red.4}"}}}, "red3": {"$type": "color", "$value": "{@globalcolorpalette.$light.$red.1}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Red3", "figmaId": "VariableID:3089:58", "modes": {"light": "{@globalcolorpalette.$light.$red.1}", "dark": "{@globalcolorpalette.$dark.$red.1}"}}}, "pink1": {"$type": "color", "$value": "{@globalcolorpalette.$light.$pink.6}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Pink1", "figmaId": "VariableID:3089:59", "modes": {"light": "{@globalcolorpalette.$light.$pink.6}", "dark": "{@globalcolorpalette.$dark.$pink.7}"}}}, "pink2": {"$type": "color", "$value": "{@globalcolorpalette.$light.$pink.4}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Pink2", "figmaId": "VariableID:3137:43", "modes": {"light": "{@globalcolorpalette.$light.$pink.4}", "dark": "{@globalcolorpalette.$dark.$pink.4}"}}}, "pink3": {"$type": "color", "$value": "{@globalcolorpalette.$light.$pink.1}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Pink3", "figmaId": "VariableID:3089:60", "modes": {"light": "{@globalcolorpalette.$light.$pink.1}", "dark": "{@globalcolorpalette.$dark.$pink.1}"}}}, "purple1": {"$type": "color", "$value": "{@globalcolorpalette.$light.$purple.6}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Purple1", "figmaId": "VariableID:3089:61", "modes": {"light": "{@globalcolorpalette.$light.$purple.6}", "dark": "{@globalcolorpalette.$dark.$purple.7}"}}}, "purple2": {"$type": "color", "$value": "{@globalcolorpalette.$light.$purple.4}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Purple2", "figmaId": "VariableID:3137:44", "modes": {"light": "{@globalcolorpalette.$light.$purple.4}", "dark": "{@globalcolorpalette.$dark.$purple.4}"}}}, "purple3": {"$type": "color", "$value": "{@globalcolorpalette.$light.$purple.1}", "$description": "", "$variable_metadata": {"name": "FO/DataVIS/Purple3", "figmaId": "VariableID:3089:62", "modes": {"light": "{@globalcolorpalette.$light.$purple.1}", "dark": "{@globalcolorpalette.$dark.$purple.1}"}}}}}}, "@fonumbervariables": {"$collection_metadata": {"name": "FONumberVariables", "figmaId": "VariableCollectionId:1105:17825", "modes": [{"key": "mode_1", "name": "Mode 1"}]}, "$size": {"12": {"$type": "number", "$value": 12, "$description": "", "$variable_metadata": {"name": "Size/12", "figmaId": "VariableID:3312:966", "modes": {"mode_1": 12}}}, "16": {"$type": "number", "$value": 16, "$description": "", "$variable_metadata": {"name": "Size/16", "figmaId": "VariableID:3162:7284", "modes": {"mode_1": 16}}}, "20": {"$type": "number", "$value": 20, "$description": "", "$variable_metadata": {"name": "Size/20", "figmaId": "VariableID:3165:7578", "modes": {"mode_1": 20}}}, "24": {"$type": "number", "$value": 24, "$description": "", "$variable_metadata": {"name": "Size/24", "figmaId": "VariableID:3162:7285", "modes": {"mode_1": 24}}}, "28": {"$type": "number", "$value": 28, "$description": "", "$variable_metadata": {"name": "Size/28", "figmaId": "VariableID:3370:5672", "modes": {"mode_1": 28}}}, "32": {"$type": "number", "$value": 32, "$description": "", "$variable_metadata": {"name": "Size/32", "figmaId": "VariableID:3162:7286", "modes": {"mode_1": 32}}}, "36": {"$type": "number", "$value": 36, "$description": "", "$variable_metadata": {"name": "Size/36", "figmaId": "VariableID:3284:1298", "modes": {"mode_1": 36}}}, "40": {"$type": "number", "$value": 40, "$description": "", "$variable_metadata": {"name": "Size/40", "figmaId": "VariableID:3162:7287", "modes": {"mode_1": 40}}}, "44": {"$type": "number", "$value": 44, "$description": "", "$variable_metadata": {"name": "Size/44", "figmaId": "VariableID:4193:6571", "modes": {"mode_1": 44}}}, "48": {"$type": "number", "$value": 48, "$description": "", "$variable_metadata": {"name": "Size/48", "figmaId": "VariableID:3162:7293", "modes": {"mode_1": 48}}}, "52": {"$type": "number", "$value": 52, "$description": "", "$variable_metadata": {"name": "Size/52", "figmaId": "VariableID:4193:6572", "modes": {"mode_1": 52}}}, "56": {"$type": "number", "$value": 56, "$description": "", "$variable_metadata": {"name": "Size/56", "figmaId": "VariableID:3162:7288", "modes": {"mode_1": 56}}}, "60": {"$type": "number", "$value": 60, "$description": "", "$variable_metadata": {"name": "Size/60", "figmaId": "VariableID:4193:6573", "modes": {"mode_1": 60}}}, "64": {"$type": "number", "$value": 64, "$description": "", "$variable_metadata": {"name": "Size/64", "figmaId": "VariableID:3162:7289", "modes": {"mode_1": 64}}}, "68": {"$type": "number", "$value": 68, "$description": "", "$variable_metadata": {"name": "Size/68", "figmaId": "VariableID:4193:6574", "modes": {"mode_1": 68}}}, "72": {"$type": "number", "$value": 72, "$description": "", "$variable_metadata": {"name": "Size/72", "figmaId": "VariableID:4193:6575", "modes": {"mode_1": 72}}}, "100": {"$type": "number", "$value": 100, "$description": "", "$variable_metadata": {"name": "Size/100", "figmaId": "VariableID:3870:16103", "modes": {"mode_1": 100}}}}, "$layout": {"2": {"$type": "number", "$value": 2, "$description": "", "$variable_metadata": {"name": "Layout/2", "figmaId": "VariableID:3162:7274", "modes": {"mode_1": 2}}}, "4": {"$type": "number", "$value": 4, "$description": "", "$variable_metadata": {"name": "Layout/4", "figmaId": "VariableID:3162:7275", "modes": {"mode_1": 4}}}, "8": {"$type": "number", "$value": 8, "$description": "", "$variable_metadata": {"name": "Layout/8", "figmaId": "VariableID:3162:7277", "modes": {"mode_1": 8}}}, "12": {"$type": "number", "$value": 12, "$description": "", "$variable_metadata": {"name": "Layout/12", "figmaId": "VariableID:3162:7273", "modes": {"mode_1": 12}}}, "16": {"$type": "number", "$value": 16, "$description": "", "$variable_metadata": {"name": "Layout/16", "figmaId": "VariableID:3162:7278", "modes": {"mode_1": 16}}}, "20": {"$type": "number", "$value": 20, "$description": "", "$variable_metadata": {"name": "Layout/20", "figmaId": "VariableID:4192:6632", "modes": {"mode_1": 20}}}, "24": {"$type": "number", "$value": 24, "$description": "", "$variable_metadata": {"name": "Layout/24", "figmaId": "VariableID:3162:7279", "modes": {"mode_1": 24}}}, "28": {"$type": "number", "$value": 28, "$description": "", "$variable_metadata": {"name": "Layout/28", "figmaId": "VariableID:4193:6576", "modes": {"mode_1": 28}}}, "32": {"$type": "number", "$value": 32, "$description": "", "$variable_metadata": {"name": "Layout/32", "figmaId": "VariableID:4193:6577", "modes": {"mode_1": 32}}}}, "$radius": {"2": {"$type": "number", "$value": 2, "$description": "", "$variable_metadata": {"name": "Radius/2", "figmaId": "VariableID:1202:9323", "modes": {"mode_1": 2}}}, "4": {"$type": "number", "$value": 4, "$description": "", "$variable_metadata": {"name": "Radius/4", "figmaId": "VariableID:1202:9324", "modes": {"mode_1": 4}}}, "6": {"$type": "number", "$value": 6, "$description": "", "$variable_metadata": {"name": "Radius/6", "figmaId": "VariableID:1202:9325", "modes": {"mode_1": 6}}}, "8": {"$type": "number", "$value": 8, "$description": "", "$variable_metadata": {"name": "Radius/8", "figmaId": "VariableID:1202:9326", "modes": {"mode_1": 8}}}, "12": {"$type": "number", "$value": 12, "$description": "", "$variable_metadata": {"name": "Radius/12", "figmaId": "VariableID:1202:9327", "modes": {"mode_1": 12}}}, "16": {"$type": "number", "$value": 16, "$description": "", "$variable_metadata": {"name": "Radius/16", "figmaId": "VariableID:4193:6570", "modes": {"mode_1": 16}}}}}, "@globalcolorpalette": {"$collection_metadata": {"name": "GlobalColorPalette", "figmaId": "VariableCollectionId:3043:8", "modes": [{"key": "mode_1", "name": "Mode 1"}]}, "$light": {"$violet": {"1": {"$type": "color", "$value": "#ebebff", "$description": "", "$variable_metadata": {"name": "Light/Violet/1", "figmaId": "VariableID:3043:94", "modes": {"mode_1": "rgba(235,235,255,1.00)"}}}, "2": {"$type": "color", "$value": "#d4d1ff", "$description": "", "$variable_metadata": {"name": "Light/Violet/2", "figmaId": "VariableID:3043:91", "modes": {"mode_1": "rgba(212,209,255,1.00)"}}}, "3": {"$type": "color", "$value": "#b6b0ff", "$description": "", "$variable_metadata": {"name": "Light/Violet/3", "figmaId": "VariableID:3043:89", "modes": {"mode_1": "rgba(182,176,255,1.00)"}}}, "4": {"$type": "color", "$value": "#9b8fff", "$description": "", "$variable_metadata": {"name": "Light/Violet/4", "figmaId": "VariableID:3043:87", "modes": {"mode_1": "rgba(155,143,255,1.00)"}}}, "5": {"$type": "color", "$value": "#7c6aff", "$description": "", "$variable_metadata": {"name": "Light/Violet/5", "figmaId": "VariableID:3043:93", "modes": {"mode_1": "rgba(124,106,255,1.00)"}}}, "6": {"$type": "color", "$value": "#5e45ff", "$description": "", "$variable_metadata": {"name": "Light/Violet/6", "figmaId": "VariableID:3043:86", "modes": {"mode_1": "rgba(94,69,255,1.00)"}}}, "7": {"$type": "color", "$value": "#4e35db", "$description": "", "$variable_metadata": {"name": "Light/Violet/7", "figmaId": "VariableID:3043:88", "modes": {"mode_1": "rgba(78,53,219,1.00)"}}}, "8": {"$type": "color", "$value": "#3f27b8", "$description": "", "$variable_metadata": {"name": "Light/Violet/8", "figmaId": "VariableID:3043:90", "modes": {"mode_1": "rgba(63,39,184,1.00)"}}}, "9": {"$type": "color", "$value": "#311b94", "$description": "", "$variable_metadata": {"name": "Light/Violet/9", "figmaId": "VariableID:3043:92", "modes": {"mode_1": "rgba(49,27,148,1.00)"}}}, "10": {"$type": "color", "$value": "#241170", "$description": "", "$variable_metadata": {"name": "Light/Violet/10", "figmaId": "VariableID:3043:85", "modes": {"mode_1": "rgba(36,17,112,1.00)"}}}}, "$blue": {"1": {"$type": "color", "$value": "#e0f0ff", "$description": "", "$variable_metadata": {"name": "Light/Blue/1", "figmaId": "VariableID:3043:126", "modes": {"mode_1": "rgba(224,240,255,1.00)"}}}, "2": {"$type": "color", "$value": "#c4e0ff", "$description": "", "$variable_metadata": {"name": "Light/Blue/2", "figmaId": "VariableID:3043:122", "modes": {"mode_1": "rgba(196,224,255,1.00)"}}}, "3": {"$type": "color", "$value": "#99c8ff", "$description": "", "$variable_metadata": {"name": "Light/Blue/3", "figmaId": "VariableID:3043:121", "modes": {"mode_1": "rgba(153,200,255,1.00)"}}}, "4": {"$type": "color", "$value": "#6badff", "$description": "", "$variable_metadata": {"name": "Light/Blue/4", "figmaId": "VariableID:3043:119", "modes": {"mode_1": "rgba(107,173,255,1.00)"}}}, "5": {"$type": "color", "$value": "#3d91ff", "$description": "", "$variable_metadata": {"name": "Light/Blue/5", "figmaId": "VariableID:3043:120", "modes": {"mode_1": "rgba(61,145,255,1.00)"}}}, "6": {"$type": "color", "$value": "#0d72ff", "$description": "", "$variable_metadata": {"name": "Light/Blue/6", "figmaId": "VariableID:3043:125", "modes": {"mode_1": "rgba(13,114,255,1.00)"}}}, "7": {"$type": "color", "$value": "#055bdb", "$description": "", "$variable_metadata": {"name": "Light/Blue/7", "figmaId": "VariableID:3043:118", "modes": {"mode_1": "rgba(5,91,219,1.00)"}}}, "8": {"$type": "color", "$value": "#0047b8", "$description": "", "$variable_metadata": {"name": "Light/Blue/8", "figmaId": "VariableID:3043:124", "modes": {"mode_1": "rgba(0,71,184,1.00)"}}}, "9": {"$type": "color", "$value": "#003694", "$description": "", "$variable_metadata": {"name": "Light/Blue/9", "figmaId": "VariableID:3043:123", "modes": {"mode_1": "rgba(0,54,148,1.00)"}}}, "10": {"$type": "color", "$value": "#002770", "$description": "", "$variable_metadata": {"name": "Light/Blue/10", "figmaId": "VariableID:3043:117", "modes": {"mode_1": "rgba(0,39,112,1.00)"}}}}, "$lightblue": {"1": {"$type": "color", "$value": "#dbf6ff", "$description": "", "$variable_metadata": {"name": "Light/LightBlue/1", "figmaId": "VariableID:3043:158", "modes": {"mode_1": "rgba(219,246,255,1.00)"}}}, "2": {"$type": "color", "$value": "#bcedfe", "$description": "", "$variable_metadata": {"name": "Light/LightBlue/2", "figmaId": "VariableID:3043:157", "modes": {"mode_1": "rgba(188,237,254,1.00)"}}}, "3": {"$type": "color", "$value": "#8edffd", "$description": "", "$variable_metadata": {"name": "Light/LightBlue/3", "figmaId": "VariableID:3043:156", "modes": {"mode_1": "rgba(142,223,253,1.00)"}}}, "4": {"$type": "color", "$value": "#60d0fc", "$description": "", "$variable_metadata": {"name": "Light/LightBlue/4", "figmaId": "VariableID:3043:155", "modes": {"mode_1": "rgba(96,208,252,1.00)"}}}, "5": {"$type": "color", "$value": "#32bffb", "$description": "", "$variable_metadata": {"name": "Light/LightBlue/5", "figmaId": "VariableID:3043:154", "modes": {"mode_1": "rgba(50,191,251,1.00)"}}}, "6": {"$type": "color", "$value": "#00abfa", "$description": "", "$variable_metadata": {"name": "Light/LightBlue/6", "figmaId": "VariableID:3043:153", "modes": {"mode_1": "rgba(0,171,250,1.00)"}}}, "7": {"$type": "color", "$value": "#008bd0", "$description": "", "$variable_metadata": {"name": "Light/LightBlue/7", "figmaId": "VariableID:3043:152", "modes": {"mode_1": "rgba(0,139,208,1.00)"}}}, "8": {"$type": "color", "$value": "#006ca7", "$description": "", "$variable_metadata": {"name": "Light/LightBlue/8", "figmaId": "VariableID:3043:151", "modes": {"mode_1": "rgba(0,108,167,1.00)"}}}, "9": {"$type": "color", "$value": "#004f7d", "$description": "", "$variable_metadata": {"name": "Light/LightBlue/9", "figmaId": "VariableID:3043:150", "modes": {"mode_1": "rgba(0,79,125,1.00)"}}}, "10": {"$type": "color", "$value": "#003353", "$description": "", "$variable_metadata": {"name": "Light/LightBlue/10", "figmaId": "VariableID:3043:149", "modes": {"mode_1": "rgba(0,51,83,1.00)"}}}}, "$teal": {"1": {"$type": "color", "$value": "#d8f8ef", "$description": "", "$variable_metadata": {"name": "Light/Teal/1", "figmaId": "VariableID:3043:186", "modes": {"mode_1": "rgba(216,248,239,1.00)"}}}, "2": {"$type": "color", "$value": "#b2f0e1", "$description": "", "$variable_metadata": {"name": "Light/Teal/2", "figmaId": "VariableID:3043:184", "modes": {"mode_1": "rgba(178,240,225,1.00)"}}}, "3": {"$type": "color", "$value": "#87e1cc", "$description": "", "$variable_metadata": {"name": "Light/Teal/3", "figmaId": "VariableID:3043:183", "modes": {"mode_1": "rgba(135,225,204,1.00)"}}}, "4": {"$type": "color", "$value": "#54d3b7", "$description": "", "$variable_metadata": {"name": "Light/Teal/4", "figmaId": "VariableID:3043:189", "modes": {"mode_1": "rgba(84,211,183,1.00)"}}}, "5": {"$type": "color", "$value": "#27c4a5", "$description": "", "$variable_metadata": {"name": "Light/Teal/5", "figmaId": "VariableID:3043:187", "modes": {"mode_1": "rgba(39,196,165,1.00)"}}}, "6": {"$type": "color", "$value": "#00b594", "$description": "", "$variable_metadata": {"name": "Light/Teal/6", "figmaId": "VariableID:3043:182", "modes": {"mode_1": "rgba(0,181,148,1.00)"}}}, "7": {"$type": "color", "$value": "#00977e", "$description": "", "$variable_metadata": {"name": "Light/Teal/7", "figmaId": "VariableID:3043:181", "modes": {"mode_1": "rgba(0,151,126,1.00)"}}}, "8": {"$type": "color", "$value": "#007967", "$description": "", "$variable_metadata": {"name": "Light/Teal/8", "figmaId": "VariableID:3043:185", "modes": {"mode_1": "rgba(0,121,103,1.00)"}}}, "9": {"$type": "color", "$value": "#005b4f", "$description": "", "$variable_metadata": {"name": "Light/Teal/9", "figmaId": "VariableID:3043:180", "modes": {"mode_1": "rgba(0,91,79,1.00)"}}}, "10": {"$type": "color", "$value": "#003c35", "$description": "", "$variable_metadata": {"name": "Light/Teal/10", "figmaId": "VariableID:3043:188", "modes": {"mode_1": "rgba(0,60,53,1.00)"}}}}, "$green": {"1": {"$type": "color", "$value": "#daf8e0", "$description": "", "$variable_metadata": {"name": "Light/Green/1", "figmaId": "VariableID:3043:272", "modes": {"mode_1": "rgba(218,248,224,1.00)"}}}, "2": {"$type": "color", "$value": "#b5f2c2", "$description": "", "$variable_metadata": {"name": "Light/Green/2", "figmaId": "VariableID:3043:271", "modes": {"mode_1": "rgba(181,242,194,1.00)"}}}, "3": {"$type": "color", "$value": "#8ee5a1", "$description": "", "$variable_metadata": {"name": "Light/Green/3", "figmaId": "VariableID:3043:269", "modes": {"mode_1": "rgba(142,229,161,1.00)"}}}, "4": {"$type": "color", "$value": "#65d780", "$description": "", "$variable_metadata": {"name": "Light/Green/4", "figmaId": "VariableID:3043:268", "modes": {"mode_1": "rgba(101,215,128,1.00)"}}}, "5": {"$type": "color", "$value": "#41ca62", "$description": "", "$variable_metadata": {"name": "Light/Green/5", "figmaId": "VariableID:3043:267", "modes": {"mode_1": "rgba(65,202,98,1.00)"}}}, "6": {"$type": "color", "$value": "#22bd4b", "$description": "", "$variable_metadata": {"name": "Light/Green/6", "figmaId": "VariableID:3043:270", "modes": {"mode_1": "rgba(34,189,75,1.00)"}}}, "7": {"$type": "color", "$value": "#1b9e40", "$description": "", "$variable_metadata": {"name": "Light/Green/7", "figmaId": "VariableID:3043:265", "modes": {"mode_1": "rgba(27,158,64,1.00)"}}}, "8": {"$type": "color", "$value": "#147e34", "$description": "", "$variable_metadata": {"name": "Light/Green/8", "figmaId": "VariableID:3043:266", "modes": {"mode_1": "rgba(20,126,52,1.00)"}}}, "9": {"$type": "color", "$value": "#0e5f27", "$description": "", "$variable_metadata": {"name": "Light/Green/9", "figmaId": "VariableID:3043:264", "modes": {"mode_1": "rgba(14,95,39,1.00)"}}}, "10": {"$type": "color", "$value": "#093f1b", "$description": "", "$variable_metadata": {"name": "Light/Green/10", "figmaId": "VariableID:3043:263", "modes": {"mode_1": "rgba(9,63,27,1.00)"}}}}, "$lightgreen": {"1": {"$type": "color", "$value": "#e9fad2", "$description": "", "$variable_metadata": {"name": "Light/LightGreen/1", "figmaId": "VariableID:3043:303", "modes": {"mode_1": "rgba(233,250,210,1.00)"}}}, "2": {"$type": "color", "$value": "#cff5ab", "$description": "", "$variable_metadata": {"name": "Light/LightGreen/2", "figmaId": "VariableID:3043:298", "modes": {"mode_1": "rgba(207,245,171,1.00)"}}}, "3": {"$type": "color", "$value": "#b6ec84", "$description": "", "$variable_metadata": {"name": "Light/LightGreen/3", "figmaId": "VariableID:3043:300", "modes": {"mode_1": "rgba(182,236,132,1.00)"}}}, "4": {"$type": "color", "$value": "#9ce25f", "$description": "", "$variable_metadata": {"name": "Light/LightGreen/4", "figmaId": "VariableID:3043:302", "modes": {"mode_1": "rgba(156,226,95,1.00)"}}}, "5": {"$type": "color", "$value": "#83d93d", "$description": "", "$variable_metadata": {"name": "Light/LightGreen/5", "figmaId": "VariableID:3043:299", "modes": {"mode_1": "rgba(131,217,61,1.00)"}}}, "6": {"$type": "color", "$value": "#6acf1d", "$description": "", "$variable_metadata": {"name": "Light/LightGreen/6", "figmaId": "VariableID:3043:297", "modes": {"mode_1": "rgba(106,207,29,1.00)"}}}, "7": {"$type": "color", "$value": "#56ad18", "$description": "", "$variable_metadata": {"name": "Light/LightGreen/7", "figmaId": "VariableID:3043:301", "modes": {"mode_1": "rgba(86,173,24,1.00)"}}}, "8": {"$type": "color", "$value": "#428a12", "$description": "", "$variable_metadata": {"name": "Light/LightGreen/8", "figmaId": "VariableID:3043:296", "modes": {"mode_1": "rgba(66,138,18,1.00)"}}}, "9": {"$type": "color", "$value": "#2f670c", "$description": "", "$variable_metadata": {"name": "Light/LightGreen/9", "figmaId": "VariableID:3043:295", "modes": {"mode_1": "rgba(47,103,12,1.00)"}}}, "10": {"$type": "color", "$value": "#1e4508", "$description": "", "$variable_metadata": {"name": "Light/LightGreen/10", "figmaId": "VariableID:3043:294", "modes": {"mode_1": "rgba(30,69,8,1.00)"}}}}, "$yellow": {"1": {"$type": "color", "$value": "#fffdcc", "$description": "", "$variable_metadata": {"name": "Light/Yellow/1", "figmaId": "VariableID:3043:334", "modes": {"mode_1": "rgba(255,253,204,1.00)"}}}, "2": {"$type": "color", "$value": "#fff9b0", "$description": "", "$variable_metadata": {"name": "Light/Yellow/2", "figmaId": "VariableID:3043:329", "modes": {"mode_1": "rgba(255,249,176,1.00)"}}}, "3": {"$type": "color", "$value": "#fff187", "$description": "", "$variable_metadata": {"name": "Light/Yellow/3", "figmaId": "VariableID:3043:332", "modes": {"mode_1": "rgba(255,241,135,1.00)"}}}, "4": {"$type": "color", "$value": "#fce55d", "$description": "", "$variable_metadata": {"name": "Light/Yellow/4", "figmaId": "VariableID:3043:330", "modes": {"mode_1": "rgba(252,229,93,1.00)"}}}, "5": {"$type": "color", "$value": "#fcd732", "$description": "", "$variable_metadata": {"name": "Light/Yellow/5", "figmaId": "VariableID:3043:328", "modes": {"mode_1": "rgba(252,215,50,1.00)"}}}, "6": {"$type": "color", "$value": "#fcc500", "$description": "", "$variable_metadata": {"name": "Light/Yellow/6", "figmaId": "VariableID:3043:327", "modes": {"mode_1": "rgba(252,197,0,1.00)"}}}, "7": {"$type": "color", "$value": "#d59b00", "$description": "", "$variable_metadata": {"name": "Light/Yellow/7", "figmaId": "VariableID:3043:326", "modes": {"mode_1": "rgba(213,155,0,1.00)"}}}, "8": {"$type": "color", "$value": "#aa7400", "$description": "", "$variable_metadata": {"name": "Light/Yellow/8", "figmaId": "VariableID:3043:333", "modes": {"mode_1": "rgba(170,116,0,1.00)"}}}, "9": {"$type": "color", "$value": "#805000", "$description": "", "$variable_metadata": {"name": "Light/Yellow/9", "figmaId": "VariableID:3043:331", "modes": {"mode_1": "rgba(128,80,0,1.00)"}}}, "10": {"$type": "color", "$value": "#553100", "$description": "", "$variable_metadata": {"name": "Light/Yellow/10", "figmaId": "VariableID:3043:325", "modes": {"mode_1": "rgba(85,49,0,1.00)"}}}}, "$orange": {"1": {"$type": "color", "$value": "#fff0d4", "$description": "", "$variable_metadata": {"name": "Light/Orange/1", "figmaId": "VariableID:3043:371", "modes": {"mode_1": "rgba(255,240,212,1.00)"}}}, "2": {"$type": "color", "$value": "#fee3b4", "$description": "", "$variable_metadata": {"name": "Light/Orange/2", "figmaId": "VariableID:3043:374", "modes": {"mode_1": "rgba(254,227,180,1.00)"}}}, "3": {"$type": "color", "$value": "#fdcf8b", "$description": "", "$variable_metadata": {"name": "Light/Orange/3", "figmaId": "VariableID:3043:372", "modes": {"mode_1": "rgba(253,207,139,1.00)"}}}, "4": {"$type": "color", "$value": "#fcb962", "$description": "", "$variable_metadata": {"name": "Light/Orange/4", "figmaId": "VariableID:3043:370", "modes": {"mode_1": "rgba(252,185,98,1.00)"}}}, "5": {"$type": "color", "$value": "#fba23c", "$description": "", "$variable_metadata": {"name": "Light/Orange/5", "figmaId": "VariableID:3043:375", "modes": {"mode_1": "rgba(251,162,60,1.00)"}}}, "6": {"$type": "color", "$value": "#fa8611", "$description": "", "$variable_metadata": {"name": "Light/Orange/6", "figmaId": "VariableID:3043:369", "modes": {"mode_1": "rgba(250,134,17,1.00)"}}}, "7": {"$type": "color", "$value": "#d0650c", "$description": "", "$variable_metadata": {"name": "Light/Orange/7", "figmaId": "VariableID:3043:367", "modes": {"mode_1": "rgba(208,101,12,1.00)"}}}, "8": {"$type": "color", "$value": "#a74808", "$description": "", "$variable_metadata": {"name": "Light/Orange/8", "figmaId": "VariableID:3043:373", "modes": {"mode_1": "rgba(167,72,8,1.00)"}}}, "9": {"$type": "color", "$value": "#7d2f05", "$description": "", "$variable_metadata": {"name": "Light/Orange/9", "figmaId": "VariableID:3043:366", "modes": {"mode_1": "rgba(125,47,5,1.00)"}}}, "10": {"$type": "color", "$value": "#531b02", "$description": "", "$variable_metadata": {"name": "Light/Orange/10", "figmaId": "VariableID:3043:368", "modes": {"mode_1": "rgba(83,27,2,1.00)"}}}}, "$red": {"1": {"$type": "color", "$value": "#fee8e2", "$description": "", "$variable_metadata": {"name": "Light/Red/1", "figmaId": "VariableID:3043:403", "modes": {"mode_1": "rgba(254,232,226,1.00)"}}}, "2": {"$type": "color", "$value": "#fdd0c5", "$description": "", "$variable_metadata": {"name": "Light/Red/2", "figmaId": "VariableID:3043:405", "modes": {"mode_1": "rgba(253,208,197,1.00)"}}}, "3": {"$type": "color", "$value": "#fbafa1", "$description": "", "$variable_metadata": {"name": "Light/Red/3", "figmaId": "VariableID:3043:404", "modes": {"mode_1": "rgba(251,175,161,1.00)"}}}, "4": {"$type": "color", "$value": "#f99181", "$description": "", "$variable_metadata": {"name": "Light/Red/4", "figmaId": "VariableID:3043:406", "modes": {"mode_1": "rgba(249,145,129,1.00)"}}}, "5": {"$type": "color", "$value": "#f77063", "$description": "", "$variable_metadata": {"name": "Light/Red/5", "figmaId": "VariableID:3043:400", "modes": {"mode_1": "rgba(247,112,99,1.00)"}}}, "6": {"$type": "color", "$value": "#f24f44", "$description": "", "$variable_metadata": {"name": "Light/Red/6", "figmaId": "VariableID:3043:399", "modes": {"mode_1": "rgba(242,79,68,1.00)"}}}, "7": {"$type": "color", "$value": "#d12a25", "$description": "", "$variable_metadata": {"name": "Light/Red/7", "figmaId": "VariableID:3043:402", "modes": {"mode_1": "rgba(209,42,37,1.00)"}}}, "8": {"$type": "color", "$value": "#ae1a1a", "$description": "", "$variable_metadata": {"name": "Light/Red/8", "figmaId": "VariableID:3043:398", "modes": {"mode_1": "rgba(174,26,26,1.00)"}}}, "9": {"$type": "color", "$value": "#8a1015", "$description": "", "$variable_metadata": {"name": "Light/Red/9", "figmaId": "VariableID:3043:397", "modes": {"mode_1": "rgba(138,16,21,1.00)"}}}, "10": {"$type": "color", "$value": "#660910", "$description": "", "$variable_metadata": {"name": "Light/Red/10", "figmaId": "VariableID:3043:401", "modes": {"mode_1": "rgba(102,9,16,1.00)"}}}}, "$pink": {"1": {"$type": "color", "$value": "#fde4ef", "$description": "", "$variable_metadata": {"name": "Light/Pink/1", "figmaId": "VariableID:3043:437", "modes": {"mode_1": "rgba(253,228,239,1.00)"}}}, "2": {"$type": "color", "$value": "#fad3e5", "$description": "", "$variable_metadata": {"name": "Light/Pink/2", "figmaId": "VariableID:3043:432", "modes": {"mode_1": "rgba(250,211,229,1.00)"}}}, "3": {"$type": "color", "$value": "#f6a8cf", "$description": "", "$variable_metadata": {"name": "Light/Pink/3", "figmaId": "VariableID:3043:433", "modes": {"mode_1": "rgba(246,168,207,1.00)"}}}, "4": {"$type": "color", "$value": "#f17fbc", "$description": "", "$variable_metadata": {"name": "Light/Pink/4", "figmaId": "VariableID:3043:431", "modes": {"mode_1": "rgba(241,127,188,1.00)"}}}, "5": {"$type": "color", "$value": "#ed57ac", "$description": "", "$variable_metadata": {"name": "Light/Pink/5", "figmaId": "VariableID:3043:435", "modes": {"mode_1": "rgba(237,87,172,1.00)"}}}, "6": {"$type": "color", "$value": "#e540a3", "$description": "", "$variable_metadata": {"name": "Light/Pink/6", "figmaId": "VariableID:3043:436", "modes": {"mode_1": "rgba(229,64,163,1.00)"}}}, "7": {"$type": "color", "$value": "#c42489", "$description": "", "$variable_metadata": {"name": "Light/Pink/7", "figmaId": "VariableID:3043:430", "modes": {"mode_1": "rgba(196,36,137,1.00)"}}}, "8": {"$type": "color", "$value": "#a11878", "$description": "", "$variable_metadata": {"name": "Light/Pink/8", "figmaId": "VariableID:3043:434", "modes": {"mode_1": "rgba(161,24,120,1.00)"}}}, "9": {"$type": "color", "$value": "#7d0f5c", "$description": "", "$variable_metadata": {"name": "Light/Pink/9", "figmaId": "VariableID:3043:429", "modes": {"mode_1": "rgba(125,15,92,1.00)"}}}, "10": {"$type": "color", "$value": "#590842", "$description": "", "$variable_metadata": {"name": "Light/Pink/10", "figmaId": "VariableID:3043:428", "modes": {"mode_1": "rgba(89,8,66,1.00)"}}}}, "$purple": {"1": {"$type": "color", "$value": "#fae7fb", "$description": "", "$variable_metadata": {"name": "Light/Purple/1", "figmaId": "VariableID:3043:489", "modes": {"mode_1": "rgba(250,231,251,1.00)"}}}, "2": {"$type": "color", "$value": "#f5d2fa", "$description": "", "$variable_metadata": {"name": "Light/Purple/2", "figmaId": "VariableID:3043:484", "modes": {"mode_1": "rgba(245,210,250,1.00)"}}}, "3": {"$type": "color", "$value": "#eba7f7", "$description": "", "$variable_metadata": {"name": "Light/Purple/3", "figmaId": "VariableID:3043:487", "modes": {"mode_1": "rgba(235,167,247,1.00)"}}}, "4": {"$type": "color", "$value": "#db7bf0", "$description": "", "$variable_metadata": {"name": "Light/Purple/4", "figmaId": "VariableID:3043:485", "modes": {"mode_1": "rgba(219,123,240,1.00)"}}}, "5": {"$type": "color", "$value": "#ca53eb", "$description": "", "$variable_metadata": {"name": "Light/Purple/5", "figmaId": "VariableID:3043:488", "modes": {"mode_1": "rgba(202,83,235,1.00)"}}}, "6": {"$type": "color", "$value": "#bc40e5", "$description": "", "$variable_metadata": {"name": "Light/Purple/6", "figmaId": "VariableID:3043:483", "modes": {"mode_1": "rgba(188,64,229,1.00)"}}}, "7": {"$type": "color", "$value": "#8c1eb7", "$description": "", "$variable_metadata": {"name": "Light/Purple/7", "figmaId": "VariableID:3043:482", "modes": {"mode_1": "rgba(140,30,183,1.00)"}}}, "8": {"$type": "color", "$value": "#6b1394", "$description": "", "$variable_metadata": {"name": "Light/Purple/8", "figmaId": "VariableID:3043:486", "modes": {"mode_1": "rgba(107,19,148,1.00)"}}}, "9": {"$type": "color", "$value": "#4d0b70", "$description": "", "$variable_metadata": {"name": "Light/Purple/9", "figmaId": "VariableID:3043:481", "modes": {"mode_1": "rgba(77,11,112,1.00)"}}}, "10": {"$type": "color", "$value": "#3a0659", "$description": "", "$variable_metadata": {"name": "Light/Purple/10", "figmaId": "VariableID:3043:480", "modes": {"mode_1": "rgba(58,6,89,1.00)"}}}}, "$gray": {"0": {"$type": "color", "$value": "#ffffff", "$description": "", "$variable_metadata": {"name": "Light/Gray/0", "figmaId": "VariableID:3043:61", "modes": {"mode_1": "rgba(255,255,255,1.00)"}}}, "1": {"$type": "color", "$value": "#f5f6f7", "$description": "", "$variable_metadata": {"name": "Light/Gray/1", "figmaId": "VariableID:3043:60", "modes": {"mode_1": "rgba(245,246,247,1.00)"}}}, "2": {"$type": "color", "$value": "#edeff2", "$description": "", "$variable_metadata": {"name": "Light/Gray/2", "figmaId": "VariableID:3043:59", "modes": {"mode_1": "rgba(237,239,242,1.00)"}}}, "3": {"$type": "color", "$value": "#e3e6ed", "$description": "", "$variable_metadata": {"name": "Light/Gray/3", "figmaId": "VariableID:3043:51", "modes": {"mode_1": "rgba(227,230,237,1.00)"}}}, "4": {"$type": "color", "$value": "#d3d8e0", "$description": "", "$variable_metadata": {"name": "Light/Gray/4", "figmaId": "VariableID:3043:50", "modes": {"mode_1": "rgba(211,216,224,1.00)"}}}, "5": {"$type": "color", "$value": "#c2c8d4", "$description": "", "$variable_metadata": {"name": "Light/Gray/5", "figmaId": "VariableID:3043:49", "modes": {"mode_1": "rgba(194,200,212,1.00)"}}}, "6": {"$type": "color", "$value": "#b1b7c4", "$description": "", "$variable_metadata": {"name": "Light/Gray/6", "figmaId": "VariableID:3043:47", "modes": {"mode_1": "rgba(177,183,196,1.00)"}}}, "7": {"$type": "color", "$value": "#9da4b2", "$description": "", "$variable_metadata": {"name": "Light/Gray/7", "figmaId": "VariableID:3043:46", "modes": {"mode_1": "rgba(157,164,178,1.00)"}}}, "8": {"$type": "color", "$value": "#8a92a1", "$description": "", "$variable_metadata": {"name": "Light/Gray/8", "figmaId": "VariableID:3043:45", "modes": {"mode_1": "rgba(138,146,161,1.00)"}}}, "9": {"$type": "color", "$value": "#7a8291", "$description": "", "$variable_metadata": {"name": "Light/Gray/9", "figmaId": "VariableID:3043:48", "modes": {"mode_1": "rgba(122,130,145,1.00)"}}}, "10": {"$type": "color", "$value": "#697080", "$description": "", "$variable_metadata": {"name": "Light/Gray/10", "figmaId": "VariableID:3043:53", "modes": {"mode_1": "rgba(105,112,128,1.00)"}}}, "11": {"$type": "color", "$value": "#585f6e", "$description": "", "$variable_metadata": {"name": "Light/Gray/11", "figmaId": "VariableID:3043:58", "modes": {"mode_1": "rgba(88,95,110,1.00)"}}}, "12": {"$type": "color", "$value": "#484e5c", "$description": "", "$variable_metadata": {"name": "Light/Gray/12", "figmaId": "VariableID:3043:57", "modes": {"mode_1": "rgba(72,78,92,1.00)"}}}, "13": {"$type": "color", "$value": "#383d4a", "$description": "", "$variable_metadata": {"name": "Light/Gray/13", "figmaId": "VariableID:3043:55", "modes": {"mode_1": "rgba(56,61,74,1.00)"}}}, "14": {"$type": "color", "$value": "#292e38", "$description": "", "$variable_metadata": {"name": "Light/Gray/14", "figmaId": "VariableID:3043:54", "modes": {"mode_1": "rgba(41,46,56,1.00)"}}}, "15": {"$type": "color", "$value": "#1c1f26", "$description": "", "$variable_metadata": {"name": "Light/Gray/15", "figmaId": "VariableID:3043:52", "modes": {"mode_1": "rgba(28,31,38,1.00)"}}}, "16": {"$type": "color", "$value": "#000000", "$description": "", "$variable_metadata": {"name": "Light/Gray/16", "figmaId": "VariableID:3043:56", "modes": {"mode_1": "rgba(0,0,0,1.00)"}}}}}, "$dark": {"$violet": {"1": {"$type": "color", "$value": "#333863", "$description": "", "$variable_metadata": {"name": "Dark/Violet/1", "figmaId": "VariableID:3043:573", "modes": {"mode_1": "rgba(51,56,99,1.00)"}}}, "2": {"$type": "color", "$value": "#3c427e", "$description": "", "$variable_metadata": {"name": "Dark/Violet/2", "figmaId": "VariableID:3043:571", "modes": {"mode_1": "rgba(60,66,126,1.00)"}}}, "3": {"$type": "color", "$value": "#464d9b", "$description": "", "$variable_metadata": {"name": "Dark/Violet/3", "figmaId": "VariableID:3043:567", "modes": {"mode_1": "rgba(70,77,155,1.00)"}}}, "4": {"$type": "color", "$value": "#5157b9", "$description": "", "$variable_metadata": {"name": "Dark/Violet/4", "figmaId": "VariableID:3043:570", "modes": {"mode_1": "rgba(81,87,185,1.00)"}}}, "5": {"$type": "color", "$value": "#5f63d8", "$description": "", "$variable_metadata": {"name": "Dark/Violet/5", "figmaId": "VariableID:3043:568", "modes": {"mode_1": "rgba(95,99,216,1.00)"}}}, "6": {"$type": "color", "$value": "#6f6ff7", "$description": "", "$variable_metadata": {"name": "Dark/Violet/6", "figmaId": "VariableID:3043:572", "modes": {"mode_1": "rgba(111,111,247,1.00)"}}}, "7": {"$type": "color", "$value": "#8886f7", "$description": "", "$variable_metadata": {"name": "Dark/Violet/7", "figmaId": "VariableID:3043:566", "modes": {"mode_1": "rgba(136,134,247,1.00)"}}}, "8": {"$type": "color", "$value": "#a6a4fb", "$description": "", "$variable_metadata": {"name": "Dark/Violet/8", "figmaId": "VariableID:3043:565", "modes": {"mode_1": "rgba(166,164,251,1.00)"}}}, "9": {"$type": "color", "$value": "#c3c2fd", "$description": "", "$variable_metadata": {"name": "Dark/Violet/9", "figmaId": "VariableID:3043:564", "modes": {"mode_1": "rgba(195,194,253,1.00)"}}}, "10": {"$type": "color", "$value": "#dedeff", "$description": "", "$variable_metadata": {"name": "Dark/Violet/10", "figmaId": "VariableID:3043:569", "modes": {"mode_1": "rgba(222,222,255,1.00)"}}}}, "$blue": {"1": {"$type": "color", "$value": "#253c5e", "$description": "", "$variable_metadata": {"name": "Dark/Blue/1", "figmaId": "VariableID:3043:614", "modes": {"mode_1": "rgba(37,60,94,1.00)"}}}, "2": {"$type": "color", "$value": "#2c4b7a", "$description": "", "$variable_metadata": {"name": "Dark/Blue/2", "figmaId": "VariableID:3043:612", "modes": {"mode_1": "rgba(44,75,122,1.00)"}}}, "3": {"$type": "color", "$value": "#325a96", "$description": "", "$variable_metadata": {"name": "Dark/Blue/3", "figmaId": "VariableID:3043:613", "modes": {"mode_1": "rgba(50,90,150,1.00)"}}}, "4": {"$type": "color", "$value": "#3668b4", "$description": "", "$variable_metadata": {"name": "Dark/Blue/4", "figmaId": "VariableID:3043:611", "modes": {"mode_1": "rgba(54,104,180,1.00)"}}}, "5": {"$type": "color", "$value": "#3976d2", "$description": "", "$variable_metadata": {"name": "Dark/Blue/5", "figmaId": "VariableID:3043:609", "modes": {"mode_1": "rgba(57,118,210,1.00)"}}}, "6": {"$type": "color", "$value": "#3b84f1", "$description": "", "$variable_metadata": {"name": "Dark/Blue/6", "figmaId": "VariableID:3043:608", "modes": {"mode_1": "rgba(59,132,241,1.00)"}}}, "7": {"$type": "color", "$value": "#619ef6", "$description": "", "$variable_metadata": {"name": "Dark/Blue/7", "figmaId": "VariableID:3043:607", "modes": {"mode_1": "rgba(97,158,246,1.00)"}}}, "8": {"$type": "color", "$value": "#85b7f9", "$description": "", "$variable_metadata": {"name": "Dark/Blue/8", "figmaId": "VariableID:3043:606", "modes": {"mode_1": "rgba(133,183,249,1.00)"}}}, "9": {"$type": "color", "$value": "#aad0fc", "$description": "", "$variable_metadata": {"name": "Dark/Blue/9", "figmaId": "VariableID:3043:605", "modes": {"mode_1": "rgba(170,208,252,1.00)"}}}, "10": {"$type": "color", "$value": "#d1e8ff", "$description": "", "$variable_metadata": {"name": "Dark/Blue/10", "figmaId": "VariableID:3043:610", "modes": {"mode_1": "rgba(209,232,255,1.00)"}}}}, "$lightblue": {"1": {"$type": "color", "$value": "#1f4159", "$description": "", "$variable_metadata": {"name": "Dark/LightBlue/1", "figmaId": "VariableID:3043:642", "modes": {"mode_1": "rgba(31,65,89,1.00)"}}}, "2": {"$type": "color", "$value": "#255373", "$description": "", "$variable_metadata": {"name": "Dark/LightBlue/2", "figmaId": "VariableID:3043:645", "modes": {"mode_1": "rgba(37,83,115,1.00)"}}}, "3": {"$type": "color", "$value": "#2a6a91", "$description": "", "$variable_metadata": {"name": "Dark/LightBlue/3", "figmaId": "VariableID:3043:639", "modes": {"mode_1": "rgba(42,106,145,1.00)"}}}, "4": {"$type": "color", "$value": "#2f7dad", "$description": "", "$variable_metadata": {"name": "Dark/LightBlue/4", "figmaId": "VariableID:3043:643", "modes": {"mode_1": "rgba(47,125,173,1.00)"}}}, "5": {"$type": "color", "$value": "#3290c9", "$description": "", "$variable_metadata": {"name": "Dark/LightBlue/5", "figmaId": "VariableID:3043:640", "modes": {"mode_1": "rgba(50,144,201,1.00)"}}}, "6": {"$type": "color", "$value": "#35a2e5", "$description": "", "$variable_metadata": {"name": "Dark/LightBlue/6", "figmaId": "VariableID:3043:641", "modes": {"mode_1": "rgba(53,162,229,1.00)"}}}, "7": {"$type": "color", "$value": "#5ab5ed", "$description": "", "$variable_metadata": {"name": "Dark/LightBlue/7", "figmaId": "VariableID:3043:638", "modes": {"mode_1": "rgba(90,181,237,1.00)"}}}, "8": {"$type": "color", "$value": "#80c6f2", "$description": "", "$variable_metadata": {"name": "Dark/LightBlue/8", "figmaId": "VariableID:3043:637", "modes": {"mode_1": "rgba(128,198,242,1.00)"}}}, "9": {"$type": "color", "$value": "#a6d8fa", "$description": "", "$variable_metadata": {"name": "Dark/LightBlue/9", "figmaId": "VariableID:3043:644", "modes": {"mode_1": "rgba(166,216,250,1.00)"}}}, "10": {"$type": "color", "$value": "#cceeff", "$description": "", "$variable_metadata": {"name": "Dark/LightBlue/10", "figmaId": "VariableID:3043:636", "modes": {"mode_1": "rgba(204,238,255,1.00)"}}}}, "$teal": {"1": {"$type": "color", "$value": "#1d4746", "$description": "", "$variable_metadata": {"name": "Dark/Teal/1", "figmaId": "VariableID:3043:686", "modes": {"mode_1": "rgba(29,71,70,1.00)"}}}, "2": {"$type": "color", "$value": "#205957", "$description": "", "$variable_metadata": {"name": "Dark/Teal/2", "figmaId": "VariableID:3043:684", "modes": {"mode_1": "rgba(32,89,87,1.00)"}}}, "3": {"$type": "color", "$value": "#216b67", "$description": "", "$variable_metadata": {"name": "Dark/Teal/3", "figmaId": "VariableID:3043:683", "modes": {"mode_1": "rgba(33,107,103,1.00)"}}}, "4": {"$type": "color", "$value": "#218079", "$description": "", "$variable_metadata": {"name": "Dark/Teal/4", "figmaId": "VariableID:3043:682", "modes": {"mode_1": "rgba(33,128,121,1.00)"}}}, "5": {"$type": "color", "$value": "#22948a", "$description": "", "$variable_metadata": {"name": "Dark/Teal/5", "figmaId": "VariableID:3043:681", "modes": {"mode_1": "rgba(34,148,138,1.00)"}}}, "6": {"$type": "color", "$value": "#21a698", "$description": "", "$variable_metadata": {"name": "Dark/Teal/6", "figmaId": "VariableID:3043:680", "modes": {"mode_1": "rgba(33,166,152,1.00)"}}}, "7": {"$type": "color", "$value": "#59baac", "$description": "", "$variable_metadata": {"name": "Dark/Teal/7", "figmaId": "VariableID:3043:679", "modes": {"mode_1": "rgba(89,186,172,1.00)"}}}, "8": {"$type": "color", "$value": "#81cfc0", "$description": "", "$variable_metadata": {"name": "Dark/Teal/8", "figmaId": "VariableID:3043:678", "modes": {"mode_1": "rgba(129,207,192,1.00)"}}}, "9": {"$type": "color", "$value": "#a6e3d5", "$description": "", "$variable_metadata": {"name": "Dark/Teal/9", "figmaId": "VariableID:3043:677", "modes": {"mode_1": "rgba(166,227,213,1.00)"}}}, "10": {"$type": "color", "$value": "#c9f8eb", "$description": "", "$variable_metadata": {"name": "Dark/Teal/10", "figmaId": "VariableID:3043:685", "modes": {"mode_1": "rgba(201,248,235,1.00)"}}}}, "$green": {"1": {"$type": "color", "$value": "#264a2d", "$description": "", "$variable_metadata": {"name": "Dark/Green/1", "figmaId": "VariableID:3043:717", "modes": {"mode_1": "rgba(38,74,45,1.00)"}}}, "2": {"$type": "color", "$value": "#2a5a35", "$description": "", "$variable_metadata": {"name": "Dark/Green/2", "figmaId": "VariableID:3043:715", "modes": {"mode_1": "rgba(42,90,53,1.00)"}}}, "3": {"$type": "color", "$value": "#306c3e", "$description": "", "$variable_metadata": {"name": "Dark/Green/3", "figmaId": "VariableID:3043:714", "modes": {"mode_1": "rgba(48,108,62,1.00)"}}}, "4": {"$type": "color", "$value": "#327e47", "$description": "", "$variable_metadata": {"name": "Dark/Green/4", "figmaId": "VariableID:3043:712", "modes": {"mode_1": "rgba(50,126,71,1.00)"}}}, "5": {"$type": "color", "$value": "#369150", "$description": "", "$variable_metadata": {"name": "Dark/Green/5", "figmaId": "VariableID:3043:711", "modes": {"mode_1": "rgba(54,145,80,1.00)"}}}, "6": {"$type": "color", "$value": "#36a358", "$description": "", "$variable_metadata": {"name": "Dark/Green/6", "figmaId": "VariableID:3043:710", "modes": {"mode_1": "rgba(54,163,88,1.00)"}}}, "7": {"$type": "color", "$value": "#60b776", "$description": "", "$variable_metadata": {"name": "Dark/Green/7", "figmaId": "VariableID:3043:709", "modes": {"mode_1": "rgba(96,183,118,1.00)"}}}, "8": {"$type": "color", "$value": "#83ca93", "$description": "", "$variable_metadata": {"name": "Dark/Green/8", "figmaId": "VariableID:3043:716", "modes": {"mode_1": "rgba(131,202,147,1.00)"}}}, "9": {"$type": "color", "$value": "#a5deb0", "$description": "", "$variable_metadata": {"name": "Dark/Green/9", "figmaId": "VariableID:3043:708", "modes": {"mode_1": "rgba(165,222,176,1.00)"}}}, "10": {"$type": "color", "$value": "#d0f0c2", "$description": "", "$variable_metadata": {"name": "Dark/Green/10", "figmaId": "VariableID:3043:713", "modes": {"mode_1": "rgba(208,240,194,1.00)"}}}}, "$lightgreen": {"1": {"$type": "color", "$value": "#3b4f28", "$description": "", "$variable_metadata": {"name": "Dark/LightGreen/1", "figmaId": "VariableID:3043:748", "modes": {"mode_1": "rgba(59,79,40,1.00)"}}}, "2": {"$type": "color", "$value": "#45622d", "$description": "", "$variable_metadata": {"name": "Dark/LightGreen/2", "figmaId": "VariableID:3043:746", "modes": {"mode_1": "rgba(69,98,45,1.00)"}}}, "3": {"$type": "color", "$value": "#4e7530", "$description": "", "$variable_metadata": {"name": "Dark/LightGreen/3", "figmaId": "VariableID:3043:743", "modes": {"mode_1": "rgba(78,117,48,1.00)"}}}, "4": {"$type": "color", "$value": "#568835", "$description": "", "$variable_metadata": {"name": "Dark/LightGreen/4", "figmaId": "VariableID:3043:742", "modes": {"mode_1": "rgba(86,136,53,1.00)"}}}, "5": {"$type": "color", "$value": "#5c9c38", "$description": "", "$variable_metadata": {"name": "Dark/LightGreen/5", "figmaId": "VariableID:3043:744", "modes": {"mode_1": "rgba(92,156,56,1.00)"}}}, "6": {"$type": "color", "$value": "#61b03a", "$description": "", "$variable_metadata": {"name": "Dark/LightGreen/6", "figmaId": "VariableID:3043:745", "modes": {"mode_1": "rgba(97,176,58,1.00)"}}}, "7": {"$type": "color", "$value": "#7cbf5c", "$description": "", "$variable_metadata": {"name": "Dark/LightGreen/7", "figmaId": "VariableID:3043:741", "modes": {"mode_1": "rgba(124,191,92,1.00)"}}}, "8": {"$type": "color", "$value": "#98cf7e", "$description": "", "$variable_metadata": {"name": "Dark/LightGreen/8", "figmaId": "VariableID:3043:740", "modes": {"mode_1": "rgba(152,207,126,1.00)"}}}, "9": {"$type": "color", "$value": "#b3e09f", "$description": "", "$variable_metadata": {"name": "Dark/LightGreen/9", "figmaId": "VariableID:3043:739", "modes": {"mode_1": "rgba(179,224,159,1.00)"}}}, "10": {"$type": "color", "$value": "#d0f0c2", "$description": "", "$variable_metadata": {"name": "Dark/LightGreen/10", "figmaId": "VariableID:3043:747", "modes": {"mode_1": "rgba(208,240,194,1.00)"}}}}, "$yellow": {"1": {"$type": "color", "$value": "#544223", "$description": "", "$variable_metadata": {"name": "Dark/Yellow/1", "figmaId": "VariableID:3043:775", "modes": {"mode_1": "rgba(84,66,35,1.00)"}}}, "2": {"$type": "color", "$value": "#6f5728", "$description": "", "$variable_metadata": {"name": "Dark/Yellow/2", "figmaId": "VariableID:3043:778", "modes": {"mode_1": "rgba(111,87,40,1.00)"}}}, "3": {"$type": "color", "$value": "#896a2a", "$description": "", "$variable_metadata": {"name": "Dark/Yellow/3", "figmaId": "VariableID:3043:776", "modes": {"mode_1": "rgba(137,106,42,1.00)"}}}, "4": {"$type": "color", "$value": "#a37e2a", "$description": "", "$variable_metadata": {"name": "Dark/Yellow/4", "figmaId": "VariableID:3043:779", "modes": {"mode_1": "rgba(163,126,42,1.00)"}}}, "5": {"$type": "color", "$value": "#be942a", "$description": "", "$variable_metadata": {"name": "Dark/Yellow/5", "figmaId": "VariableID:3043:774", "modes": {"mode_1": "rgba(190,148,42,1.00)"}}}, "6": {"$type": "color", "$value": "#d9a927", "$description": "", "$variable_metadata": {"name": "Dark/Yellow/6", "figmaId": "VariableID:3043:777", "modes": {"mode_1": "rgba(217,169,39,1.00)"}}}, "7": {"$type": "color", "$value": "#dfbe52", "$description": "", "$variable_metadata": {"name": "Dark/Yellow/7", "figmaId": "VariableID:3043:773", "modes": {"mode_1": "rgba(223,190,82,1.00)"}}}, "8": {"$type": "color", "$value": "#e6d279", "$description": "", "$variable_metadata": {"name": "Dark/Yellow/8", "figmaId": "VariableID:3043:772", "modes": {"mode_1": "rgba(230,210,121,1.00)"}}}, "9": {"$type": "color", "$value": "#efe6a0", "$description": "", "$variable_metadata": {"name": "Dark/Yellow/9", "figmaId": "VariableID:3043:771", "modes": {"mode_1": "rgba(239,230,160,1.00)"}}}, "10": {"$type": "color", "$value": "#faf8c8", "$description": "", "$variable_metadata": {"name": "Dark/Yellow/10", "figmaId": "VariableID:3043:770", "modes": {"mode_1": "rgba(250,248,200,1.00)"}}}}, "$orange": {"1": {"$type": "color", "$value": "#573928", "$description": "", "$variable_metadata": {"name": "Dark/Orange/1", "figmaId": "VariableID:3043:810", "modes": {"mode_1": "rgba(87,57,40,1.00)"}}}, "2": {"$type": "color", "$value": "#70482f", "$description": "", "$variable_metadata": {"name": "Dark/Orange/2", "figmaId": "VariableID:3043:809", "modes": {"mode_1": "rgba(112,72,47,1.00)"}}}, "3": {"$type": "color", "$value": "#8c5835", "$description": "", "$variable_metadata": {"name": "Dark/Orange/3", "figmaId": "VariableID:3043:807", "modes": {"mode_1": "rgba(140,88,53,1.00)"}}}, "4": {"$type": "color", "$value": "#a86839", "$description": "", "$variable_metadata": {"name": "Dark/Orange/4", "figmaId": "VariableID:3043:808", "modes": {"mode_1": "rgba(168,104,57,1.00)"}}}, "5": {"$type": "color", "$value": "#c4763b", "$description": "", "$variable_metadata": {"name": "Dark/Orange/5", "figmaId": "VariableID:3043:805", "modes": {"mode_1": "rgba(196,118,59,1.00)"}}}, "6": {"$type": "color", "$value": "#de873c", "$description": "", "$variable_metadata": {"name": "Dark/Orange/6", "figmaId": "VariableID:3043:804", "modes": {"mode_1": "rgba(222,135,60,1.00)"}}}, "7": {"$type": "color", "$value": "#e5a25a", "$description": "", "$variable_metadata": {"name": "Dark/Orange/7", "figmaId": "VariableID:3043:803", "modes": {"mode_1": "rgba(229,162,90,1.00)"}}}, "8": {"$type": "color", "$value": "#edbc7b", "$description": "", "$variable_metadata": {"name": "Dark/Orange/8", "figmaId": "VariableID:3043:802", "modes": {"mode_1": "rgba(237,188,123,1.00)"}}}, "9": {"$type": "color", "$value": "#f5d49f", "$description": "", "$variable_metadata": {"name": "Dark/Orange/9", "figmaId": "VariableID:3043:801", "modes": {"mode_1": "rgba(245,212,159,1.00)"}}}, "10": {"$type": "color", "$value": "#ffecc9", "$description": "", "$variable_metadata": {"name": "Dark/Orange/10", "figmaId": "VariableID:3043:806", "modes": {"mode_1": "rgba(255,236,201,1.00)"}}}}, "$red": {"1": {"$type": "color", "$value": "#592e2e", "$description": "", "$variable_metadata": {"name": "Dark/Red/1", "figmaId": "VariableID:3043:837", "modes": {"mode_1": "rgba(89,46,46,1.00)"}}}, "2": {"$type": "color", "$value": "#703a38", "$description": "", "$variable_metadata": {"name": "Dark/Red/2", "figmaId": "VariableID:3043:841", "modes": {"mode_1": "rgba(112,58,56,1.00)"}}}, "3": {"$type": "color", "$value": "#8a4541", "$description": "", "$variable_metadata": {"name": "Dark/Red/3", "figmaId": "VariableID:3043:840", "modes": {"mode_1": "rgba(138,69,65,1.00)"}}}, "4": {"$type": "color", "$value": "#a4514b", "$description": "", "$variable_metadata": {"name": "Dark/Red/4", "figmaId": "VariableID:3043:839", "modes": {"mode_1": "rgba(164,81,75,1.00)"}}}, "5": {"$type": "color", "$value": "#bf5d54", "$description": "", "$variable_metadata": {"name": "Dark/Red/5", "figmaId": "VariableID:3043:838", "modes": {"mode_1": "rgba(191,93,84,1.00)"}}}, "6": {"$type": "color", "$value": "#db665a", "$description": "", "$variable_metadata": {"name": "Dark/Red/6", "figmaId": "VariableID:3043:836", "modes": {"mode_1": "rgba(219,102,90,1.00)"}}}, "7": {"$type": "color", "$value": "#e68176", "$description": "", "$variable_metadata": {"name": "Dark/Red/7", "figmaId": "VariableID:3043:833", "modes": {"mode_1": "rgba(230,129,118,1.00)"}}}, "8": {"$type": "color", "$value": "#ef9b92", "$description": "", "$variable_metadata": {"name": "Dark/Red/8", "figmaId": "VariableID:3043:835", "modes": {"mode_1": "rgba(239,155,146,1.00)"}}}, "9": {"$type": "color", "$value": "#f7b5af", "$description": "", "$variable_metadata": {"name": "Dark/Red/9", "figmaId": "VariableID:3043:832", "modes": {"mode_1": "rgba(247,181,175,1.00)"}}}, "10": {"$type": "color", "$value": "#fed3ce", "$description": "", "$variable_metadata": {"name": "Dark/Red/10", "figmaId": "VariableID:3043:834", "modes": {"mode_1": "rgba(254,211,206,1.00)"}}}}, "$pink": {"1": {"$type": "color", "$value": "#5c3253", "$description": "", "$variable_metadata": {"name": "Dark/Pink/1", "figmaId": "VariableID:3043:872", "modes": {"mode_1": "rgba(92,50,83,1.00)"}}}, "2": {"$type": "color", "$value": "#743b67", "$description": "", "$variable_metadata": {"name": "Dark/Pink/2", "figmaId": "VariableID:3043:869", "modes": {"mode_1": "rgba(116,59,103,1.00)"}}}, "3": {"$type": "color", "$value": "#8c4278", "$description": "", "$variable_metadata": {"name": "Dark/Pink/3", "figmaId": "VariableID:3043:870", "modes": {"mode_1": "rgba(140,66,120,1.00)"}}}, "4": {"$type": "color", "$value": "#a44a8a", "$description": "", "$variable_metadata": {"name": "Dark/Pink/4", "figmaId": "VariableID:3043:868", "modes": {"mode_1": "rgba(164,74,138,1.00)"}}}, "5": {"$type": "color", "$value": "#be509c", "$description": "", "$variable_metadata": {"name": "Dark/Pink/5", "figmaId": "VariableID:3043:867", "modes": {"mode_1": "rgba(190,80,156,1.00)"}}}, "6": {"$type": "color", "$value": "#d955ad", "$description": "", "$variable_metadata": {"name": "Dark/Pink/6", "figmaId": "VariableID:3043:866", "modes": {"mode_1": "rgba(217,85,173,1.00)"}}}, "7": {"$type": "color", "$value": "#e379bd", "$description": "", "$variable_metadata": {"name": "Dark/Pink/7", "figmaId": "VariableID:3043:865", "modes": {"mode_1": "rgba(227,121,189,1.00)"}}}, "8": {"$type": "color", "$value": "#ed99cd", "$description": "", "$variable_metadata": {"name": "Dark/Pink/8", "figmaId": "VariableID:3043:864", "modes": {"mode_1": "rgba(237,153,205,1.00)"}}}, "9": {"$type": "color", "$value": "#f5b9de", "$description": "", "$variable_metadata": {"name": "Dark/Pink/9", "figmaId": "VariableID:3043:863", "modes": {"mode_1": "rgba(245,185,222,1.00)"}}}, "10": {"$type": "color", "$value": "#fdd7ee", "$description": "", "$variable_metadata": {"name": "Dark/Pink/10", "figmaId": "VariableID:3043:871", "modes": {"mode_1": "rgba(253,215,238,1.00)"}}}}, "$purple": {"1": {"$type": "color", "$value": "#513561", "$description": "", "$variable_metadata": {"name": "Dark/Purple/1", "figmaId": "VariableID:3043:903", "modes": {"mode_1": "rgba(81,53,97,1.00)"}}}, "2": {"$type": "color", "$value": "#633b79", "$description": "", "$variable_metadata": {"name": "Dark/Purple/2", "figmaId": "VariableID:3043:901", "modes": {"mode_1": "rgba(99,59,121,1.00)"}}}, "3": {"$type": "color", "$value": "#754391", "$description": "", "$variable_metadata": {"name": "Dark/Purple/3", "figmaId": "VariableID:3043:898", "modes": {"mode_1": "rgba(117,67,145,1.00)"}}}, "4": {"$type": "color", "$value": "#894baa", "$description": "", "$variable_metadata": {"name": "Dark/Purple/4", "figmaId": "VariableID:3043:899", "modes": {"mode_1": "rgba(137,75,170,1.00)"}}}, "5": {"$type": "color", "$value": "#9e52c4", "$description": "", "$variable_metadata": {"name": "Dark/Purple/5", "figmaId": "VariableID:3043:902", "modes": {"mode_1": "rgba(158,82,196,1.00)"}}}, "6": {"$type": "color", "$value": "#b159de", "$description": "", "$variable_metadata": {"name": "Dark/Purple/6", "figmaId": "VariableID:3043:897", "modes": {"mode_1": "rgba(177,89,222,1.00)"}}}, "7": {"$type": "color", "$value": "#c27be6", "$description": "", "$variable_metadata": {"name": "Dark/Purple/7", "figmaId": "VariableID:3043:900", "modes": {"mode_1": "rgba(194,123,230,1.00)"}}}, "8": {"$type": "color", "$value": "#d29bee", "$description": "", "$variable_metadata": {"name": "Dark/Purple/8", "figmaId": "VariableID:3043:896", "modes": {"mode_1": "rgba(210,155,238,1.00)"}}}, "9": {"$type": "color", "$value": "#e1baf5", "$description": "", "$variable_metadata": {"name": "Dark/Purple/9", "figmaId": "VariableID:3043:895", "modes": {"mode_1": "rgba(225,186,245,1.00)"}}}, "10": {"$type": "color", "$value": "#efd8fb", "$description": "", "$variable_metadata": {"name": "Dark/Purple/10", "figmaId": "VariableID:3043:894", "modes": {"mode_1": "rgba(239,216,251,1.00)"}}}}, "$gray": {"0": {"$type": "color", "$value": "#1a1e24", "$description": "", "$variable_metadata": {"name": "Dark/Gray/0", "figmaId": "VariableID:3043:542", "modes": {"mode_1": "rgba(26,30,36,1.00)"}}}, "1": {"$type": "color", "$value": "#222730", "$description": "", "$variable_metadata": {"name": "Dark/Gray/1", "figmaId": "VariableID:3043:534", "modes": {"mode_1": "rgba(34,39,48,1.00)"}}}, "2": {"$type": "color", "$value": "#2b303b", "$description": "", "$variable_metadata": {"name": "Dark/Gray/2", "figmaId": "VariableID:3043:530", "modes": {"mode_1": "rgba(43,48,59,1.00)"}}}, "3": {"$type": "color", "$value": "#353a47", "$description": "", "$variable_metadata": {"name": "Dark/Gray/3", "figmaId": "VariableID:3043:539", "modes": {"mode_1": "rgba(53,58,71,1.00)"}}}, "4": {"$type": "color", "$value": "#3f4654", "$description": "", "$variable_metadata": {"name": "Dark/Gray/4", "figmaId": "VariableID:3043:529", "modes": {"mode_1": "rgba(63,70,84,1.00)"}}}, "5": {"$type": "color", "$value": "#4f5663", "$description": "", "$variable_metadata": {"name": "Dark/Gray/5", "figmaId": "VariableID:3043:540", "modes": {"mode_1": "rgba(79,86,99,1.00)"}}}, "6": {"$type": "color", "$value": "#5f6675", "$description": "", "$variable_metadata": {"name": "Dark/Gray/6", "figmaId": "VariableID:3043:528", "modes": {"mode_1": "rgba(95,102,117,1.00)"}}}, "7": {"$type": "color", "$value": "#747887", "$description": "", "$variable_metadata": {"name": "Dark/Gray/7", "figmaId": "VariableID:3043:536", "modes": {"mode_1": "rgba(116,120,135,1.00)"}}}, "8": {"$type": "color", "$value": "#848a99", "$description": "", "$variable_metadata": {"name": "Dark/Gray/8", "figmaId": "VariableID:3043:527", "modes": {"mode_1": "rgba(132,138,153,1.00)"}}}, "9": {"$type": "color", "$value": "#9499a8", "$description": "", "$variable_metadata": {"name": "Dark/Gray/9", "figmaId": "VariableID:3043:526", "modes": {"mode_1": "rgba(148,153,168,1.00)"}}}, "10": {"$type": "color", "$value": "#a8acb8", "$description": "", "$variable_metadata": {"name": "Dark/Gray/10", "figmaId": "VariableID:3043:537", "modes": {"mode_1": "rgba(168,172,184,1.00)"}}}, "11": {"$type": "color", "$value": "#bbbec7", "$description": "", "$variable_metadata": {"name": "Dark/Gray/11", "figmaId": "VariableID:3043:535", "modes": {"mode_1": "rgba(187,190,199,1.00)"}}}, "12": {"$type": "color", "$value": "#c8c9d1", "$description": "", "$variable_metadata": {"name": "Dark/Gray/12", "figmaId": "VariableID:3043:538", "modes": {"mode_1": "rgba(200,201,209,1.00)"}}}, "13": {"$type": "color", "$value": "#d6d7db", "$description": "", "$variable_metadata": {"name": "Dark/Gray/13", "figmaId": "VariableID:3043:533", "modes": {"mode_1": "rgba(214,215,219,1.00)"}}}, "14": {"$type": "color", "$value": "#e0e1e5", "$description": "", "$variable_metadata": {"name": "Dark/Gray/14", "figmaId": "VariableID:3043:532", "modes": {"mode_1": "rgba(224,225,229,1.00)"}}}, "15": {"$type": "color", "$value": "#ebebf0", "$description": "", "$variable_metadata": {"name": "Dark/Gray/15", "figmaId": "VariableID:3043:531", "modes": {"mode_1": "rgba(235,235,240,1.00)"}}}, "16": {"$type": "color", "$value": "#f2f3f7", "$description": "", "$variable_metadata": {"name": "Dark/Gray/16", "figmaId": "VariableID:3043:541", "modes": {"mode_1": "rgba(242,243,247,1.00)"}}}}}, "$basic": {"$black": {"0": {"$type": "color", "$value": "#00000000", "$description": "", "$variable_metadata": {"name": "Basic/Black/0", "figmaId": "VariableID:3046:9", "modes": {"mode_1": "rgba(0,0,0,0.00)"}}}, "5": {"$type": "color", "$value": "#0000000d", "$description": "", "$variable_metadata": {"name": "Basic/Black/5", "figmaId": "VariableID:3043:11144", "modes": {"mode_1": "rgba(0,0,0,0.05)"}}}, "10": {"$type": "color", "$value": "#0000001a", "$description": "", "$variable_metadata": {"name": "Basic/Black/10", "figmaId": "VariableID:3043:11136", "modes": {"mode_1": "rgba(0,0,0,0.10)"}}}, "20": {"$type": "color", "$value": "#00000033", "$description": "", "$variable_metadata": {"name": "Basic/Black/20", "figmaId": "VariableID:3043:11137", "modes": {"mode_1": "rgba(0,0,0,0.20)"}}}, "30": {"$type": "color", "$value": "#0000004d", "$description": "", "$variable_metadata": {"name": "Basic/Black/30", "figmaId": "VariableID:3043:11139", "modes": {"mode_1": "rgba(0,0,0,0.30)"}}}, "40": {"$type": "color", "$value": "#00000066", "$description": "", "$variable_metadata": {"name": "Basic/Black/40", "figmaId": "VariableID:3043:11138", "modes": {"mode_1": "rgba(0,0,0,0.40)"}}}, "50": {"$type": "color", "$value": "#00000080", "$description": "", "$variable_metadata": {"name": "Basic/Black/50", "figmaId": "VariableID:3043:11141", "modes": {"mode_1": "rgba(0,0,0,0.50)"}}}, "60": {"$type": "color", "$value": "#00000099", "$description": "", "$variable_metadata": {"name": "Basic/Black/60", "figmaId": "VariableID:3043:11140", "modes": {"mode_1": "rgba(0,0,0,0.60)"}}}, "70": {"$type": "color", "$value": "#000000b2", "$description": "", "$variable_metadata": {"name": "Basic/Black/70", "figmaId": "VariableID:3043:11146", "modes": {"mode_1": "rgba(0,0,0,0.70)"}}}, "80": {"$type": "color", "$value": "#000000cc", "$description": "", "$variable_metadata": {"name": "Basic/Black/80", "figmaId": "VariableID:3043:11142", "modes": {"mode_1": "rgba(0,0,0,0.80)"}}}, "90": {"$type": "color", "$value": "#000000e5", "$description": "", "$variable_metadata": {"name": "Basic/Black/90", "figmaId": "VariableID:3043:11145", "modes": {"mode_1": "rgba(0,0,0,0.90)"}}}, "100": {"$type": "color", "$value": "#000000", "$description": "", "$variable_metadata": {"name": "Basic/Black/100", "figmaId": "VariableID:3043:11143", "modes": {"mode_1": "rgba(0,0,0,1.00)"}}}}, "$white": {"0": {"$type": "color", "$value": "#ffffff00", "$description": "", "$variable_metadata": {"name": "Basic/White/0", "figmaId": "VariableID:3046:8", "modes": {"mode_1": "rgba(255,255,255,0.00)"}}}, "5": {"$type": "color", "$value": "#ffffff0d", "$description": "", "$variable_metadata": {"name": "Basic/White/5", "figmaId": "VariableID:3043:11174", "modes": {"mode_1": "rgba(255,255,255,0.05)"}}}, "10": {"$type": "color", "$value": "#ffffff1a", "$description": "", "$variable_metadata": {"name": "Basic/White/10", "figmaId": "VariableID:3043:11170", "modes": {"mode_1": "rgba(255,255,255,0.10)"}}}, "20": {"$type": "color", "$value": "#ffffff33", "$description": "", "$variable_metadata": {"name": "Basic/White/20", "figmaId": "VariableID:3043:11172", "modes": {"mode_1": "rgba(255,255,255,0.20)"}}}, "30": {"$type": "color", "$value": "#ffffff4d", "$description": "", "$variable_metadata": {"name": "Basic/White/30", "figmaId": "VariableID:3043:11176", "modes": {"mode_1": "rgba(255,255,255,0.30)"}}}, "40": {"$type": "color", "$value": "#ffffff66", "$description": "", "$variable_metadata": {"name": "Basic/White/40", "figmaId": "VariableID:3043:11171", "modes": {"mode_1": "rgba(255,255,255,0.40)"}}}, "50": {"$type": "color", "$value": "#ffffff80", "$description": "", "$variable_metadata": {"name": "Basic/White/50", "figmaId": "VariableID:3043:11177", "modes": {"mode_1": "rgba(255,255,255,0.50)"}}}, "60": {"$type": "color", "$value": "#ffffff99", "$description": "", "$variable_metadata": {"name": "Basic/White/60", "figmaId": "VariableID:3043:11173", "modes": {"mode_1": "rgba(255,255,255,0.60)"}}}, "70": {"$type": "color", "$value": "#ffffffb2", "$description": "", "$variable_metadata": {"name": "Basic/White/70", "figmaId": "VariableID:3043:11179", "modes": {"mode_1": "rgba(255,255,255,0.70)"}}}, "80": {"$type": "color", "$value": "#ffffffcc", "$description": "", "$variable_metadata": {"name": "Basic/White/80", "figmaId": "VariableID:3043:11175", "modes": {"mode_1": "rgba(255,255,255,0.80)"}}}, "90": {"$type": "color", "$value": "#ffffffe5", "$description": "", "$variable_metadata": {"name": "Basic/White/90", "figmaId": "VariableID:3043:11178", "modes": {"mode_1": "rgba(255,255,255,0.90)"}}}, "100": {"$type": "color", "$value": "#ffffff", "$description": "", "$variable_metadata": {"name": "Basic/White/100", "figmaId": "VariableID:3043:11180", "modes": {"mode_1": "rgba(255,255,255,1.00)"}}}}}}, "@focontentvariables": {"$collection_metadata": {"name": "FOContentVariables", "figmaId": "VariableCollectionId:3162:7290", "modes": [{"key": "mode_1", "name": "Mode 1"}]}, "$content": {"$action": {"default": {"$type": "unknown", "$value": "按钮", "$description": "", "$variable_metadata": {"name": "Content/Action/Default", "figmaId": "VariableID:3293:2473", "modes": {"mode_1": "按钮"}}}, "search": {"$type": "unknown", "$value": "搜索", "$description": "", "$variable_metadata": {"name": "Content/Action/Search", "figmaId": "VariableID:3293:2476", "modes": {"mode_1": "搜索"}}}, "add": {"$type": "unknown", "$value": "新建", "$description": "", "$variable_metadata": {"name": "Content/Action/Add", "figmaId": "VariableID:3293:2474", "modes": {"mode_1": "新建"}}}, "view": {"$type": "unknown", "$value": "查看", "$description": "", "$variable_metadata": {"name": "Content/Action/View", "figmaId": "VariableID:3911:11544", "modes": {"mode_1": "查看"}}}, "cancel": {"$type": "unknown", "$value": "取消", "$description": "", "$variable_metadata": {"name": "Content/Action/Cancel", "figmaId": "VariableID:3293:2475", "modes": {"mode_1": "取消"}}}, "import": {"$type": "unknown", "$value": "导入", "$description": "", "$variable_metadata": {"name": "Content/Action/Import", "figmaId": "VariableID:4239:6679", "modes": {"mode_1": "导入"}}}, "export": {"$type": "unknown", "$value": "导出", "$description": "", "$variable_metadata": {"name": "Content/Action/Export", "figmaId": "VariableID:4239:6680", "modes": {"mode_1": "导出"}}}, "delete": {"$type": "unknown", "$value": "删除", "$description": "", "$variable_metadata": {"name": "Content/Action/Delete", "figmaId": "VariableID:4239:6681", "modes": {"mode_1": "删除"}}}}, "$navigation": {"home": {"$type": "unknown", "$value": "首页", "$description": "", "$variable_metadata": {"name": "Content/Navigation/Home", "figmaId": "VariableID:3165:17784", "modes": {"mode_1": "首页"}}}, "ai": {"$type": "unknown", "$value": "AI", "$description": "", "$variable_metadata": {"name": "Content/Navigation/AI", "figmaId": "VariableID:3165:16874", "modes": {"mode_1": "AI"}}}, "development": {"$type": "unknown", "$value": "研发", "$description": "", "$variable_metadata": {"name": "Content/Navigation/Development", "figmaId": "VariableID:3165:16876", "modes": {"mode_1": "研发"}}}, "statistics": {"$type": "unknown", "$value": "统计", "$description": "", "$variable_metadata": {"name": "Content/Navigation/Statistics", "figmaId": "VariableID:3165:17785", "modes": {"mode_1": "统计"}}}, "testing": {"$type": "unknown", "$value": "测试", "$description": "", "$variable_metadata": {"name": "Content/Navigation/Testing", "figmaId": "VariableID:3165:16875", "modes": {"mode_1": "测试"}}}, "devguard": {"$type": "unknown", "$value": "<PERSON><PERSON><PERSON>", "$description": "", "$variable_metadata": {"name": "Content/Navigation/DevGuard", "figmaId": "VariableID:3165:15466", "modes": {"mode_1": "<PERSON><PERSON><PERSON>"}}}, "gamepackage": {"$type": "unknown", "$value": "游戏包体中心", "$description": "", "$variable_metadata": {"name": "Content/Navigation/GamePackage", "figmaId": "VariableID:3165:15467", "modes": {"mode_1": "游戏包体中心"}}}}, "$datafield": {"label_default": {"$type": "unknown", "$value": "数据字段", "$description": "", "$variable_metadata": {"name": "Content/DataField/Label_Default", "figmaId": "VariableID:3432:3384", "modes": {"mode_1": "数据字段"}}}, "label_id": {"$type": "unknown", "$value": "ID", "$description": "", "$variable_metadata": {"name": "Content/DataField/Label_ID", "figmaId": "VariableID:3432:3430", "modes": {"mode_1": "ID"}}}, "label_number": {"$type": "unknown", "$value": "序号", "$description": "", "$variable_metadata": {"name": "Content/DataField/Label_Number", "figmaId": "VariableID:3521:2866", "modes": {"mode_1": "序号"}}}, "label_name": {"$type": "unknown", "$value": "名称", "$description": "", "$variable_metadata": {"name": "Content/DataField/Label_Name", "figmaId": "VariableID:3432:3423", "modes": {"mode_1": "名称"}}}, "label_describe": {"$type": "unknown", "$value": "简介", "$description": "", "$variable_metadata": {"name": "Content/DataField/Label_Describe", "figmaId": "VariableID:3432:3424", "modes": {"mode_1": "简介"}}}, "label_time": {"$type": "unknown", "$value": "时间", "$description": "", "$variable_metadata": {"name": "Content/DataField/Label_Time", "figmaId": "VariableID:3432:3425", "modes": {"mode_1": "时间"}}}, "label_timerange": {"$type": "unknown", "$value": "时间段", "$description": "", "$variable_metadata": {"name": "Content/DataField/Label_TimeRange", "figmaId": "VariableID:3432:3426", "modes": {"mode_1": "时间段"}}}, "label_state": {"$type": "unknown", "$value": "状态", "$description": "", "$variable_metadata": {"name": "Content/DataField/Label_State", "figmaId": "VariableID:3432:3427", "modes": {"mode_1": "状态"}}}, "label_member": {"$type": "unknown", "$value": "负责人", "$description": "", "$variable_metadata": {"name": "Content/DataField/Label_Member", "figmaId": "VariableID:3432:3429", "modes": {"mode_1": "负责人"}}}, "label_action": {"$type": "unknown", "$value": "操作", "$description": "", "$variable_metadata": {"name": "Content/DataField/Label_Action", "figmaId": "VariableID:3432:3428", "modes": {"mode_1": "操作"}}}, "value_default": {"$type": "unknown", "$value": "数据值", "$description": "", "$variable_metadata": {"name": "Content/DataField/Value_Default", "figmaId": "VariableID:3432:3385", "modes": {"mode_1": "数据值"}}}, "value_id": {"$type": "unknown", "$value": "210022929", "$description": "", "$variable_metadata": {"name": "Content/DataField/Value_ID", "figmaId": "VariableID:3432:3431", "modes": {"mode_1": "210022929"}}}, "value_number": {"$type": "unknown", "$value": "1", "$description": "", "$variable_metadata": {"name": "Content/DataField/Value_Number", "figmaId": "VariableID:3521:2867", "modes": {"mode_1": "1"}}}, "value_name": {"$type": "unknown", "$value": "Full Name (nickname)", "$description": "", "$variable_metadata": {"name": "Content/DataField/Value_Name", "figmaId": "VariableID:3432:3418", "modes": {"mode_1": "Full Name (nickname)"}}}, "value_describe": {"$type": "unknown", "$value": "介绍文字", "$description": "", "$variable_metadata": {"name": "Content/DataField/Value_Describe", "figmaId": "VariableID:3432:3419", "modes": {"mode_1": "介绍文字"}}}, "value_time": {"$type": "unknown", "$value": "2025-04-12  13:34", "$description": "", "$variable_metadata": {"name": "Content/DataField/Value_Time", "figmaId": "VariableID:3432:3420", "modes": {"mode_1": "2025-04-12  13:34"}}}, "value_timerange": {"$type": "unknown", "$value": "2025-04-12  13:34 ～ 2025-04-12  13:34", "$description": "", "$variable_metadata": {"name": "Content/DataField/Value_TimeRange", "figmaId": "VariableID:3432:3421", "modes": {"mode_1": "2025-04-12  13:34 ～ 2025-04-12  13:34"}}}}}, "$state": {"processing": {"$type": "unknown", "$value": "提交中...", "$description": "", "$variable_metadata": {"name": "State/Processing", "figmaId": "VariableID:3293:1248", "modes": {"mode_1": "提交中..."}}}, "processing2": {"$type": "unknown", "$value": "处理中...", "$description": "", "$variable_metadata": {"name": "State/Processing2", "figmaId": "VariableID:3293:1249", "modes": {"mode_1": "处理中..."}}}, "processing3": {"$type": "unknown", "$value": "查询中...", "$description": "", "$variable_metadata": {"name": "State/Processing3", "figmaId": "VariableID:3293:1250", "modes": {"mode_1": "查询中..."}}}}, "$notice": {"welcome": {"$type": "unknown", "$value": "欢迎使用 ForgeOn", "$description": "", "$variable_metadata": {"name": "Notice/Welcome", "figmaId": "VariableID:3162:7291", "modes": {"mode_1": "欢迎使用 ForgeOn"}}}}}}