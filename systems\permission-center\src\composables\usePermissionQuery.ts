import type {
  GetPermissionGroupsResponse,
  GetPermissionMembersResponse,
  MultiTenantPermissionGroupsData,
  MultiTenantPermissionMembersData,
  NonMultiTenantPermissionGroupsData,
  NonMultiTenantPermissionMembersData,
  PermissionGroupItem,
  PermissionMemberItem,
} from '../api/query.ts';
import type { PermissionCategory, QueryPermissionRow } from '../views/query/permission.ts';
import { OrgStructureType } from '../api/group.ts';

/**
 * 按权限查询数据处理
 */
export function usePermissionQuery() {
  /**
   * 将API返回的权限成员数据转换为表格显示的行数据
   * @param apiData API返回的权限成员数据
   * @param isMultiTenant 是否为多租户模式
   * @returns 转换后的表格数据、分类信息
   */
  function transformMembersToTableData(
    apiData: GetPermissionMembersResponse['data'],
    isMultiTenant: boolean,
  ) {
    if (isMultiTenant) {
      // 多租户模式：生成带租户分组的树形数据
      return generateMultiTenantData(apiData as MultiTenantPermissionMembersData);
    } else {
      // 非多租户模式：生成平铺的成员列表
      return generateNonMultiTenantData(apiData as NonMultiTenantPermissionMembersData);
    }
  }

  /**
   * 将V2 API返回的角色数据转换为表格显示的行数据
   * @param apiData V2 API返回的角色数据
   * @param isMultiTenant 是否为多租户模式
   * @returns 转换后的表格数据、分类信息
   */
  function transformGroupsToTableData(
    apiData: GetPermissionGroupsResponse['data'],
    isMultiTenant: boolean,
  ) {
    if (isMultiTenant) {
      // 多租户模式：生成带租户分组的树形数据
      return generateMultiTenantGroupData(apiData as MultiTenantPermissionGroupsData);
    } else {
      // 非多租户模式：生成平铺的角色列表
      return generateNonMultiTenantGroupData(apiData as NonMultiTenantPermissionGroupsData);
    }
  }

  return {
    transformMembersToTableData,
    transformGroupsToTableData,
  };
}

/**
 * 生成多租户数据（树形结构）
 */
function generateMultiTenantData(data: MultiTenantPermissionMembersData) {
  const tableData: QueryPermissionRow[] = [];
  const categories: PermissionCategory[] = [];

  if (!data?.tenants) {
    return { tableData, categories };
  }

  Object.entries(data.tenants).forEach(([tenantId, members]) => {
    if (!Array.isArray(members) || members.length === 0) {
      return;
    }

    const tenantName = members[0]?.groups?.[0]?.tenant || `项目 ${tenantId}`;
    const uniqueMembers = new Map<string, PermissionMemberItem>();

    // 去重
    members.forEach((member) => uniqueMembers.set(member.resourceId, member));

    // 统计成员类型
    const memberCountInfo = { users: 0, departments: 0, groups: 0 };
    members.forEach((member) => {
      switch (member.type) {
        case OrgStructureType.Member:
          memberCountInfo.users++;
          break;
        case OrgStructureType.Department:
        case OrgStructureType.OutsourceDepartment:
          memberCountInfo.departments++;
          break;
        case OrgStructureType.CustomGroup:
          memberCountInfo.groups++;
          break;
      }
    });

    // 添加租户分组节点
    tableData.push({
      uniqueKey: `tenant-${tenantId}`,
      id: `tenant-${tenantId}`,
      name: tenantName,
      type: 'category',
      childCount: uniqueMembers.size,
      memberCountInfo,
    });

    // 添加成员节点
    Array.from(uniqueMembers.values()).forEach((member, index) => {
      tableData.push(createMemberRow(member, {
        uniqueKey: `member-${tenantId}-${member.resourceId}-${index}`,
        parentUniqueKey: `tenant-${tenantId}`,
        categoryId: tenantId,
      }));
    });

    // 添加分类信息
    categories.push({
      label: tenantName,
      value: tenantId,
      count: uniqueMembers.size,
    });
  });

  return { tableData, categories };
}

/**
 * 生成非多租户数据（平铺结构）
 */
function generateNonMultiTenantData(data: NonMultiTenantPermissionMembersData) {
  const tableData: QueryPermissionRow[] = [];
  const categories: PermissionCategory[] = []; // 非多租户不需要分类

  if (!data?.members || !Array.isArray(data.members)) {
    return { tableData, categories };
  }

  // 去重
  const uniqueMembers = new Map<string, PermissionMemberItem>();
  data.members.forEach((member) => uniqueMembers.set(member.resourceId, member));

  // 生成平铺的成员行
  Array.from(uniqueMembers.values()).forEach((member, index) => {
    tableData.push(createMemberRow(member, {
      uniqueKey: `member-${member.resourceId}-${index}`,
      categoryId: 0,
    }));
  });

  return { tableData, categories };
}

/**
 * 创建成员行数据
 */
function createMemberRow(
  member: PermissionMemberItem,
  options: { uniqueKey: string; categoryId?: string | number; parentUniqueKey?: string },
): QueryPermissionRow {
  const roles = member.groups?.map((group) => ({
    id: group.id,
    name: group.name,
    tenant: group.tenant,
    tenantId: group.tenantId,
    isPublic: false,
  })) || [];

  const categoryId = options.categoryId || 0;
  const tenant = typeof categoryId === 'string' ? categoryId : '';

  const baseRow = {
    uniqueKey: options.uniqueKey,
    parentUniqueKey: options.parentUniqueKey,
    id: member.resourceId,
    name: member.name,
    type: 'permission' as const,
    categoryId,
    code: member.resourceId,
    description: getMemberTypeLabel(member.type, member.outsourceFlag),
    isEnabled: true,
    roles,
    resourceId: member.resourceId,
    orgType: member.type as OrgStructureType,
    outsourceFlag: member.outsourceFlag,
    rawData: {
      id: 0,
      appId: 0,
      tenant,
      name: member.name,
      code: member.resourceId,
      description: getMemberTypeLabel(member.type, member.outsourceFlag),
      category: tenant,
      isEnabled: true,
    },
  };

  return baseRow;
}

/**
 * 获取成员类型标签
 * @param type 成员类型
 * @param outsourceFlag 外包标识，仅对 Member 类型有效
 */
function getMemberTypeLabel(type: string, outsourceFlag?: boolean): string {
  switch (type) {
    case OrgStructureType.Member:
      return outsourceFlag ? '干员(外包)' : '干员';
    case OrgStructureType.Department:
      return '部门';
    case OrgStructureType.OutsourceDepartment:
      return '部门(含外包)';
    case OrgStructureType.CustomGroup:
      return '自定义组';
    default:
      return type;
  }
}

/**
 * 生成多租户角色数据（树形结构） - V2 接口专用
 */
function generateMultiTenantGroupData(data: MultiTenantPermissionGroupsData) {
  const tableData: QueryPermissionRow[] = [];
  const categories: PermissionCategory[] = [];

  if (!data?.tenants) {
    return { tableData, categories };
  }

  Object.entries(data.tenants).forEach(([tenantId, groups]) => {
    if (!Array.isArray(groups) || groups.length === 0) {
      return;
    }

    const tenantName = groups[0]?.tenant || `项目 ${tenantId}`;

    // 添加租户分组节点
    tableData.push({
      uniqueKey: `tenant-${tenantId}`,
      id: `tenant-${tenantId}`,
      name: tenantName,
      type: 'category',
      childCount: groups.length,
      memberCountInfo: { users: 0, departments: 0, groups: groups.length },
    });

    // 添加角色节点
    groups.forEach((group, index) => {
      tableData.push(createGroupRow(group, {
        uniqueKey: `group-${tenantId}-${group.id}-${index}`,
        parentUniqueKey: `tenant-${tenantId}`,
        categoryId: tenantId,
      }));
    });

    // 添加分类信息
    categories.push({
      label: tenantName,
      value: tenantId,
      count: groups.length,
    });
  });

  return { tableData, categories };
}

/**
 * 生成非多租户角色数据（平铺结构） - V2 接口专用
 */
function generateNonMultiTenantGroupData(data: NonMultiTenantPermissionGroupsData) {
  const tableData: QueryPermissionRow[] = [];
  const categories: PermissionCategory[] = []; // 非多租户不需要分类

  if (!data?.groups || !Array.isArray(data.groups)) {
    return { tableData, categories };
  }

  // 生成平铺的角色行
  data.groups.forEach((group, index) => {
    tableData.push(createGroupRow(group, {
      uniqueKey: `group-${group.id}-${index}`,
      categoryId: 0,
    }));
  });

  return { tableData, categories };
}

/**
 * 创建角色行数据 - V2 接口专用
 */
function createGroupRow(
  group: PermissionGroupItem,
  options: { uniqueKey: string; categoryId?: string | number; parentUniqueKey?: string },
): QueryPermissionRow {
  const categoryId = options.categoryId || 0;
  const tenant = typeof categoryId === 'string' ? categoryId : group.tenant || '';

  // 将角色转换为角色信息数组
  const roles = [{
    id: group.id,
    name: group.name,
    tenant: group.tenant,
    tenantId: group.tenantId,
    isPublic: false,
  }];

  const baseRow = {
    uniqueKey: options.uniqueKey,
    parentUniqueKey: options.parentUniqueKey,
    id: group.id.toString(),
    name: group.name,
    type: 'permission' as const,
    categoryId,
    code: group.id.toString(),
    description: '角色',
    isEnabled: true,
    roles,
    resourceId: group.id.toString(),
    orgType: OrgStructureType.CustomGroup,
    rawData: {
      id: group.id,
      appId: group.appId,
      tenant: group.tenant,
      name: group.name,
      code: group.id.toString(),
      description: '角色',
      category: tenant,
      isEnabled: true,
    },
  };

  return baseRow;
}
