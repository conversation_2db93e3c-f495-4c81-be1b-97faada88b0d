import type { Router } from 'vue-router';
import {
  getMicroAppData,
  usePermissionCtx,
  useRouteNavigationCtx,
} from '@hg-tech/oasis-common';
import { checkDevGuardPermission } from '../../services/permission.ts';

/**
 * 权限检查相关
 */
export function withPermission(router: Router) {
  router.beforeEach(async (to, _, next) => {
    const routeNavigation = getMicroAppData(useRouteNavigationCtx);
    const permissionData = getMicroAppData(usePermissionCtx);

    if (to.meta.permissionDeclare == null) {
      // 没有声明权限的路由直接放行
      return next();
    }
    try {
      if (checkDevGuardPermission(to.meta.permissionDeclare, permissionData?.permissionInfo)) {
        // 权限检查通过
        return next();
      } else if (routeNavigation?.onForbidden) {
        routeNavigation.onForbidden();
        return next(false);
      }
    } catch (e) {
      console.error(e);
    }

    console.warn('未实现 onForbidden 方法，使用默认处理');
    return next(false);
  });
}
