<template>
  <Drawer
    :open="show"
    :width="700"
    :closable="false"
    :maskClosable="false"
    placement="right"
    :bodyStyle="{ padding: '24px' }"
    @afterOpenChange="(o) => !o && modalDestroy()"
    @close="() => modalCancel()"
  >
    <template #title>
      <div class="FO-Font-B18 flex items-center">
        权限配置
        <EllipsisText v-if="permission?.name">
          ({{ permission?.name }})
        </EllipsisText>
      </div>
    </template>
    <template #extra>
      <Button class="btn-fill-text" @click="() => modalCancel()">
        <template #icon>
          <CloseIcon />
        </template>
      </Button>
    </template>

    <div class="h-full flex flex-col">
      <Alert
        class="mb-24px w-fit b-none px-12px py-4px c-FO-Functional-Info1-Default"
        message="以下角色拥有当前权限，可将用户添加至合适的角色配置权限"
        showIcon
      />
      <div :class="{ 'flex-auto overflow-hidden': flatTableRows?.length }">
        <div v-if="loading" class="py-40px text-center">
          <Spin size="large" />
          <div class="mt-2 text-FO-Content-Text3">
            加载中...
          </div>
        </div>

        <div v-else-if="error" class="py-40px text-center">
          <Empty description="数据加载失败" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
          <div class="text-FO-Functional-Error-Default mt-2">
            {{ error }}
          </div>
        </div>

        <div v-else-if="filteredAvailableRoles.length === 0" class="py-40px text-center">
          <Empty description="无匹配项" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
        </div>

        <BasicVxeTable
          v-else
          :options="gridOptions"
        />
      </div>
      <div class="mt-4 w-full flex items-center justify-center">
        <span class="FO-Font-R14 c-FO-Content-Text2">
          没有合适的角色？
          <Button type="link" class="FO-Font-B14 h-auto p-0 text-FO-Functional-Info1-Default" @click="handleCreateRole">
            前往创建
          </Button>
        </span>
      </div>
    </div>
  </Drawer>
</template>

<script setup lang="tsx">
import { computed } from 'vue';
import { Alert, Button, Drawer, Empty, Spin } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import type { GetUserPermissionsRequest } from '../../../api/query';
import type { QueryPermissionRow } from '../permission';
import { BasicVxeTable, EllipsisText, PlatformEnterPoint } from '@hg-tech/oasis-common';
import type { VxeGridProps } from 'vxe-table';
import CloseIcon from '../../../assets/icons/SystemStrokeClose.svg?component';
import ConfigIcon from '../../../assets/icons/SystemStrokeConfig.svg?component';

// VXE Table 行数据类型 (flat 结构)
interface AvailableRoleTableRow {
  uniqueKey: string;
  roleId: number;
  roleName: string;
  projectName?: string;
  originalData: AvailableRole;
  // 树形结构字段 (flat 结构)
  type?: 'project' | 'role';
  roleCount?: number;
  parentUniqueKey?: string;
}

interface AvailableRole {
  roleId: number;
  roleName: string;
  projectName?: string;
  tenantId?: number;
  memberCount: number;
}

interface GroupedProject {
  name: string;
  roleCount: number;
  roles: AvailableRole[];
}

interface Props extends ModalBaseProps {
  permission?: QueryPermissionRow;
  queryParams?: GetUserPermissionsRequest;
  availableRoles?: AvailableRole[];
  isMultiTenant?: boolean;
  loading?: boolean;
  error?: string;
}

const props = defineProps<Props>();

const router = useRouter();

// 从父组件传入的多租户标识
const isMultiTenant = computed(() => {
  return Boolean(props.isMultiTenant);
});

// 使用父组件传入的角色数据
const availableRoles = computed<AvailableRole[]>(() => {
  return props.availableRoles || [];
});

// 可选角色列表
const filteredAvailableRoles = computed<AvailableRole[]>(() => {
  return availableRoles.value;
});

// 按项目分组的角色数据
const groupedAvailableProjects = computed<GroupedProject[]>(() => {
  if (!isMultiTenant.value) {
    return [];
  }

  const projectMap = new Map<string, AvailableRole[]>();

  filteredAvailableRoles.value.forEach((role) => {
    const projectName = role.projectName || '默认项目';
    if (!projectMap.has(projectName)) {
      projectMap.set(projectName, []);
    }
    projectMap.get(projectName)!.push(role);
  });

  return Array.from(projectMap.entries()).map(([name, projectRoles]) => ({
    name,
    roleCount: projectRoles.length,
    roles: projectRoles,
  }));
});

// VXE Table 数据转换（普通模式）
const tableRows = computed<AvailableRoleTableRow[]>(() => {
  return filteredAvailableRoles.value.map((role) => ({
    uniqueKey: `role-${role.roleId}${role.tenantId ? `-${role.tenantId}` : ''}`,
    roleId: role.roleId,
    roleName: role.roleName,
    projectName: role.projectName,
    originalData: role,
    type: 'role',
  }));
});

// VXE Table flat 数据转换（多租户模式）
const flatTableRows = computed<AvailableRoleTableRow[]>(() => {
  if (!isMultiTenant.value || groupedAvailableProjects.value.length === 0) {
    return tableRows.value;
  }

  const result: AvailableRoleTableRow[] = [];

  groupedAvailableProjects.value.forEach((project) => {
    // 项目节点 - 使用第一个角色的tenantId作为项目的tenantId
    const firstRole = project.roles[0];
    const projectTenantId = firstRole?.tenantId || 'default';
    const projectRow: AvailableRoleTableRow = {
      uniqueKey: `tenant-${projectTenantId}`,
      roleId: 0,
      roleName: project.name,
      projectName: project.name,
      originalData: {} as AvailableRole,
      type: 'project',
      roleCount: project.roleCount,
      parentUniqueKey: undefined, // 顶级节点
    };
    result.push(projectRow);

    // 角色节点
    project.roles.forEach((role) => {
      const roleRow: AvailableRoleTableRow = {
        uniqueKey: `role-${role.roleId}${role.tenantId ? `-${role.tenantId}` : ''}`,
        roleId: role.roleId,
        roleName: role.roleName,
        projectName: role.projectName,
        originalData: role,
        type: 'role',
        parentUniqueKey: projectRow.uniqueKey, // 指向父节点
      };
      result.push(roleRow);
    });
  });

  return result;
});

// VXE Table 动态列配置
const tableColumns = computed<VxeGridProps<AvailableRoleTableRow>['columns']>(() => {
  const baseColumns: VxeGridProps<AvailableRoleTableRow>['columns'] = [
    { field: 'blank', title: '', width: 30 },
    {
      field: 'roleName',
      title: '可选角色',
      // 多租户模式启用树形节点
      ...(isMultiTenant.value && { treeNode: true }),
      slots: {
        default({ row }: { row: AvailableRoleTableRow }) {
          // 项目节点的显示
          if (row.type === 'project') {
            return (
              <div class="flex items-center gap-3">
                <span class="FO-Font-B14 c-FO-Content-Text1">{row.roleName}</span>
                <span class="rounded bg-FO-Container-Fill2 px-2 py-1 text-12px c-FO-Content-Text1">
                  {row.roleCount}
                </span>
              </div>
            );
          }
          // 角色节点的显示
          return <span class="FO-Font-R14 c-FO-Content-Text1">{row.roleName}</span>;
        },
      },
    },
  ];

  // 操作列
  baseColumns.push({
    field: 'actions',
    title: '操作',
    width: 120,
    slots: {
      default({ row }: { row: AvailableRoleTableRow }) {
        // 项目节点不显示操作按钮
        if (row.type === 'project') {
          return <span />;
        }
        // 角色节点显示配置按钮
        return (
          <Button
            class="btn-fill-text"
            onClick={() => handleConfigRole(row.originalData)}
            size="small"
          >
            <ConfigIcon />
            配置角色
          </Button>
        );
      },
    },
  });

  return baseColumns;
});

// VXE Table 配置与行样式
const gridOptions = computed<VxeGridProps<AvailableRoleTableRow>>(() => ({
  height: 'auto',
  rowConfig: {
    keyField: 'uniqueKey',
    isHover: true,
  },
  // 多租户模式启用树形结构
  ...(isMultiTenant.value && {
    treeConfig: {
      transform: true,
      rowField: 'uniqueKey',
      parentField: 'parentUniqueKey',
      expandAll: true,
    },
  }),
  columnConfig: {
    resizable: false,
  },
  columns: tableColumns.value,
  data: flatTableRows.value,
}));

function handleConfigRole(role: AvailableRole) {
  const appId = props.queryParams?.appId;
  if (!appId) {
    console.warn('缺少应用ID，无法跳转到角色配置');
    return;
  }

  const { fullPath } = router.resolve({
    name: PlatformEnterPoint.PermissionCenterApp,
    params: { appId: String(appId) },
    query: {
      tab: 'member',
      roleId: role.roleId,
    },
  });
  window.open(location.origin + fullPath);
}

function handleCreateRole() {
  const appId = props.queryParams?.appId;
  if (!appId) {
    console.warn('缺少应用ID，无法跳转到创建角色');
    return;
  }

  // 新页面打开角色创建页面
  const { fullPath } = router.resolve({
    name: PlatformEnterPoint.PermissionCenterApp,
    params: { appId: String(appId) },
    query: {
      tab: 'member',
      createRole: '1',
    },
  });
  window.open(location.origin + fullPath);
}
</script>
