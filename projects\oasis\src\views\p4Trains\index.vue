<template>
  <div :class="prefixCls" class="h-full flex flex-col p-[20px]" @contextmenu.prevent>
    <div class="mb-[16px] flex flex-none items-center rounded-md bg-FO-Container-Fill1 p-[16px]">
      <router-link class="flex px-2 c-FO-Content-Text1" :to="{ name: 'P4Training' }">
        <Icon icon="icon-park-outline:left" :size="18" title="返回" />
        <span class="font-size-[16px] font-bold">返回P4学习首页</span>
      </router-link>
    </div>
    <div :class="`${prefixCls}__all`">
      <div :class="`${prefixCls}__left w-[350px] mr-[16px] rounded-md`">
        <div
          :class="`${prefixCls}__title font-size-[20px] font-bold flex rounded-t-md justify-center py-6`"
        >
          技术中心P4V操作手册
        </div>
        <div ref="tabItemRef" :class="`${prefixCls}__tab`">
          <div
            v-for="item in showP4WikiList"
            :key="item.ID"
            :class="`${prefixCls}__tab-item`"
            :active="item.ID === activeP4WikiID"
            :data-id="item.ID"
            @click="handleGroupChange(item.ID!)"
          >
            <div class="w-[250px]">
              <ATypographyText
                :class="`${prefixCls}__tab-text ml-2`"
                :ellipsis="{ tooltip: true }"
                :content="item.title"
              />
            </div>
          </div>
        </div>
        <a-button
          v-if="examProgressInfo && examProgressInfo.statusCode !== 2"
          shape="round"
          :class="`${prefixCls}__btn `"
          @click="goToExam"
        >
          进入考试
        </a-button>
      </div>
      <div :class="`${prefixCls}__right rounded-md p-[16px] overflow-auto flex-1`">
        <MarkdownViewer v-if="activeP4WikiContent" :value="activeP4WikiContent" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup title="P4Trains">
import { TypographyText as ATypographyText } from 'ant-design-vue';
import { sortBy } from 'lodash-es';
import { nextTick, onBeforeMount, ref } from 'vue';
import { useRouter } from 'vue-router';
import type { P4WikiItem } from '/@/api/page/model/tcp4tModel';
import { getP4WikiListByPage } from '/@/api/page/tcp4t';
import { MarkdownViewer } from '/@/components/Markdown';
import { useDesign } from '/@/hooks/web/useDesign';
import { useGo } from '/@/hooks/web/usePage';
import { openWindow } from '/@/utils';
import { useP4Exam } from '/@/views/tcp4t/hook';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import { Icon } from '/@/components/Icon/index';
import { sendEvent } from '/@/service/tracker';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';

const { prefixCls } = useDesign('p4-trains');
const { resolve, currentRoute } = useRouter();
const { getExamProgress, examProgressInfo } = useP4Exam();
const go = useGo();

const tabItemRef = ref<HTMLDivElement>();
const showP4WikiList = ref<P4WikiItem[]>([]);
const activeP4WikiID = ref<number | undefined>(Number(currentRoute.value.query?.ID));

const activeP4WikiContent = ref<string>('');

// 获取培训教程列表
async function getP4WikiList() {
  const { list } = await getAllPaginationList((p) => getP4WikiListByPage(p));

  if (list?.length) {
    const findItem = list.find((item) => item.ID === activeP4WikiID.value);

    activeP4WikiID.value = findItem ? activeP4WikiID.value : list[0].ID;
    showP4WikiList.value = sortBy(list, 'readOrder');
    activeP4WikiContent.value
        = showP4WikiList.value.find((item) => item.ID === activeP4WikiID.value)?.content || '';
    nextTick(() => {
      const index = showP4WikiList.value.findIndex((item) => item.ID === activeP4WikiID.value);
      tabItemRef.value?.children[index]?.scrollIntoView({ block: 'start', behavior: 'smooth' });
    });
  } else {
    showP4WikiList.value = [];
    activeP4WikiID.value = undefined;
    activeP4WikiContent.value = '';
  }
}

// 切换培训列表
function handleGroupChange(ID: number) {
  if (ID === activeP4WikiID.value) {
    return;
  }

  go({ query: { ID } }, true);
}

function goToExam() {
  sendEvent('p4_study_p4_exam_click_enter_exam');
  const { href } = resolve({
    name: PlatformEnterPoint.TCP4T,
    query: {
      fs: 1,
      from: 'P4Trains',
    },
  });

  openWindow(href);
}

onBeforeMount(async () => {
  await getP4WikiList();
  await getExamProgress();
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-p4-trains';
.@{prefix-cls} {
  &__all {
    display: flex;
    flex: auto;
    overflow: auto;
  }

  &__left {
    display: flex;
    flex-direction: column;
    border: 1px solid @table-head-border;
    background-color: @FO-Container-Fill1;
  }

  &__right {
    border: 1px solid @table-head-border;
    background-color: @FO-Container-Fill1;
  }

  &__title {
    flex: none;
    background-color: #424242;
    color: #fff;
  }

  &__tab {
    flex: auto;
    margin: 30px 5px;
    overflow: auto;

    &-text {
      color: #424242;
    }

    &-item {
      display: flex;
      justify-content: space-between;
      margin: 8px 20px;
      padding: 8px;
      border: 1px solid transparent;
      border-radius: 6px;
      cursor: pointer;

      &:hover {
        background-color: @FO-Container-Stroke1;
      }

      &[active='true'] {
        background-color: #424242;
        font-weight: bold;

        .@{prefix-cls}__tab-text {
          color: #fff;
        }
      }
    }
  }

  &__btn {
    flex: none;
    border: 0 !important;
    background-color: #424242 !important;
    color: #fff !important;
    margin: 8px auto;
    width: max-content;
  }
}

html[data-theme='dark'] {
  .@{prefix-cls} {
    &__title {
      background-color: #d1d5da;
      color: #424242;
    }

    &__tab {
      &-text {
        color: #d1d5da;
      }

      &-item {
        &:hover {
          background-color: @FO-Container-Stroke1;
        }

        &[active='true'] {
          background-color: #d1d5da;
          font-weight: bold;

          .@{prefix-cls}__tab-text {
            color: #424242;
          }
        }
      }
    }

    &__btn {
      border: 0 !important;
      background-color: #d1d5da !important;
      color: #424242 !important;
    }
  }
}
</style>
