<template>
  <div :class="prefixCls">
    <div class="top relative mb mt">
      <div class="absolute left-30px top-50% translate-y--50% font-size-6 font-bold">
        新人P4接触流程
      </div>
      <div class="flex flex-justify-center">
        <div
          v-for="(item, index) in processList"
          :key="item.ID"
          class="ml-4px flex flex-items-center c-#8d8d8d"
          :class="{
            'c-#0B8600!': isPass(item, index),
          }"
          @click="clickStep(index + 1, item, true)"
        >
          <div
            class="flex cursor-pointer flex-items-center border-rd-10px p-4"
            :class="{ 'bg-#f2f2f2': showStep === index + 1 }"
          >
            <div
              class="h-30px w-30px border-rd-100% text-center font-size-6 c-#fff font-bold line-height-30px"
              :class="isPass(item, index) ? 'bg-#0B8600' : 'bg-#8d8d8d'"
            >
              {{ index + 1 }}
            </div>
            <div class="relative mx-1 font-size-5 font-bold">
              {{ item.title }}
              <div
                class="absolute top-25px w-100% flex flex-wrap gap-x-3px gap-y-3px flex-justify-start flex-items-center"
              >
                <div
                  v-for="(_, dotIndex) in dotNum"
                  v-show="showStep === index + 1"
                  :key="dotIndex"
                  class="h-4px w-4px border-rd-100%"
                  :class="
                    index + 2 <= step || (index + 1 === step && dot > dotIndex)
                      ? 'bg-#0B8600!'
                      : 'bg-#8d8d8d!'
                  "
                />
              </div>
            </div>
          </div>

          <Icon
            v-if="index !== processList.length - 1"
            icon="fluent:arrow-right-12-filled"
            class="ml-4px"
            :size="20"
          />
        </div>
      </div>
    </div>
    <!-- 第一种(主框架) -->
    <div
      v-if="processType === 1"
      ref="universalBottomRef"
      :class="`${prefixCls}__universal-bottom`"
      class="mx b-2 b-#8d8d8d border-rd-10px p-4 pb-1"
      style="height: calc(100vh - 181px)"
      @mousewheel="mousewheel"
      @scroll="scroll"
    >
      <div v-for="(item, detailListIndex) in processDetailList" :key="item.ID">
        <div class="flex items-center text-16px font-bold">
          <div
            class="mr-2 h-20px w-20px flex break-inside-avoid items-start justify-center border-rd-100% text-#fff"
            :class="
              showStep < step || (showStep === step && dot > detailListIndex)
                ? 'bg-#0B8600!'
                : 'bg-#8d8d8d!'
            "
          >
            {{ String.fromCharCode(detailListIndex + 97) }}
          </div>
          <div>{{ item.title }}</div>
        </div>
        <div class="mark-down mt border-rd-10px bg-#f2f2f2 p-4 font-size-4">
          <MarkdownViewer :value="item.content" style="transform: scale(0.95)" @click="handleLinkClick" />
        </div>
        <div class="relative mx-auto h-64px w-full inline-flex items-center justify-center">
          <a-button
            :class="`${prefixCls}__btn`"
            shape="round"
            :gray="!(showStep + 1 <= step || (showStep === step && dot > detailListIndex))"
            @click="buttonClick(item, detailListIndex)"
          >
            我已了解
          </a-button>

          <Icon
            v-if="
              (showStep !== step && dotNum === detailListIndex + 1)
                || (showStep === step && dotNum === detailListIndex + 1 && dotNum === dot)
            "
            icon="icon-park-outline:arrow-circle-right"
            color="#0B8600"
            :size="24"
            class="absolute left-50% ml-4 translate-x-150% cursor-pointer"
            @click="clickNext"
          />
        </div>
      </div>

      <div
        v-if="leftArrow"
        class="[transform:translateY(-50%)] absolute left-16px top-50vh h-70px w-35px flex cursor-pointer justify-center rounded-[0_70px_70px_0] bg-#4242421A hover:!bg-#4242424d"
        @click="getClickScrollPosition(1)"
      >
        <Icon icon="fluent:arrow-left-12-filled" />
      </div>
      <div
        v-if="rightArrow"
        class="[left:calc(100vw_-_16px)] [transform:translateX(-100%)translateY(-50%)] absolute top-50vh h-70px w-35px flex cursor-pointer justify-center rounded-[70px_0_0_70px] bg-#4242421A hover:!bg-#4242424d"
        @click="getClickScrollPosition(2)"
      >
        <Icon icon="fluent:arrow-right-12-filled" />
      </div>
    </div>

    <!-- 第二种框架(前往考试) -->
    <div
      v-if="processType === 2"
      class="mx flex flex-justify-center flex-items-center b-1 border-rd-10px p-4"
      style="height: calc(100vh - 181px)"
    >
      <div v-if="examProgressInfo?.statusCode !== 2" class="flex flex-col items-center">
        <a-button
          :class="`${prefixCls}__btn !pr-6px`"
          black
          shape="round"
          @click="jumpTcp4tPage "
        >
          前往参加P4考试
          <Icon icon="mingcute:right-fill" class="!ml-0" :size="16" />
        </a-button>
        <a-button
          :class="`${prefixCls}__btn mt-4`"
          size="small"
          shape="round"
          reload
          @click="reloadTestStatus"
        >
          刷新考试通过状态
        </a-button>
      </div>
      <div v-else class="flex items-center">
        <a-button :class="`${prefixCls}__btn`" shape="round" class="cursor-pointer">
          已通过P4考试
        </a-button>
        <Icon
          icon="icon-park-outline:arrow-circle-right"
          color="#0B8600"
          :size="24"
          class="ml-2 cursor-pointer"
          @click="clickNext"
        />
      </div>
    </div>
    <!-- 第三种框架(获取权限) -->
    <div
      v-if="processType === 3"
      class="relative mx flex flex-justify-center flex-items-center b-1 border-rd-10px p-4"
      style="height: calc(100vh - 181px)"
    >
      <div v-if="!isAuthority">
        <a-button :class="`${prefixCls}__btn`" shape="round" black @click="clickGetAuthority">
          点击获得P4提交权限
        </a-button>
      </div>
      <div v-else class="flex flex-col flex-items-center">
        <div class="flex flex-items-center">
          <a-button :class="`${prefixCls}__btn`" shape="round">
            你已获得P4提交权限
          </a-button>
          <Icon
            icon="icon-park-outline:check-one"
            color="#0B8600"
            :size="24"
            class="ml-2 cursor-pointer"
          />
        </div>

        <div class="mt flex flex-col flex-items-center font-size-6 font-bold line-height-16">
          <div>你已完成P4接触流程，并获取了提交权限</div>
          <div>欢迎你为鹰角网络P4仓库添砖加瓦！</div>
        </div>
        <div class="absolute bottom-10px left-50% translate-x--50% font-size-3 font-bold">
          在之后的工作中，如遇到P4提交问题，可飞书搜索并联系 技术中心服务台
        </div>
      </div>
    </div>
    <div class="mt-24px text-center">
      <Icon icon="tcp4t-footer-logo|svg" class="!text-color !h-40px" :size="200" />
    </div>
    <Teleport v-if="curPreviewImg" to="body">
      <div :class="`${prefixCls}__img-preview`">
        <div :class="`${prefixCls}__img-preview-mask`" @click="handlePreviewClose()">
          <Icon
            icon="icon-park-outline:close-one"
            class="absolute right-16px top-16px cursor-pointer"
            color="#fff"
            :size="40"
          />
          <div class="relative" @click.stop>
            <img :src="curPreviewImg" alt="预览图">
          </div>
        </div>
      </div>
    </Teleport>
    <LinkToChromeModal @register="registerModal" />
  </div>
</template>

<script setup lang="ts">
import { nextTick, onBeforeMount, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { useRequest } from 'vue-request';
import { useRouter } from 'vue-router';
import LinkToChromeModal from './LinkToChromeModal.vue';
import type { OrientationListItem } from '/@/api/page/model/systemModel';
import {
  getAuthority,
  getOrientationByPage,
  getUserSchedule,
  updateUserSchedule,
} from '/@/api/page/system';
import { Icon } from '/@/components/Icon';
import { MarkdownViewer } from '/@/components/Markdown';
import { useModal } from '/@/components/Modal';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useGo } from '/@/hooks/web/usePage';
import { useP4Exam } from '/@/views/tcp4t/hook';
import { sendEvent } from '/@/service/tracker';
import { processItemType } from './data.data';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';

const { prefixCls } = useDesign('touch-process');
const { getExamProgress, examProgressInfo } = useP4Exam();
const { createMessage } = useMessage();
const [registerModal, { openModal }] = useModal();
const router = useRouter();
const fs = router.currentRoute.value.query.fs;
const go = useGo();
const showStep = ref(1); // 当前显示步骤
const step = ref(1); // 实际步骤
const dot = ref(0); // 实际子步骤
const dotNum = ref(0); // 子步骤的数量
const lastDotNum = ref(0); // 实际子步骤的数量
const processType = ref(1);
const processList = ref<OrientationListItem[]>([]);
const processDetailList = ref<OrientationListItem[]>([]);
const isAuthority = ref(false);
const curPreviewImg = ref('');
const universalBottomRef = ref<HTMLElement | null>();
const leftArrow = ref(false);
const rightArrow = ref(false);
const { run } = useRequest(getExamProgress, {
  pollingInterval: 3000,
  manual: true,
  ready: () => !examProgressInfo.value || examProgressInfo.value.statusCode !== 2,
});

function isPass(item, index) {
  return index + 1 < step.value
    || (index + 1 === step.value
      && dot.value === lastDotNum.value
      && item.ID !== processItemType.exam
      && item.ID !== processItemType.getAuthority)
    || (isAuthority.value && item.ID === processItemType.getAuthority)
    || (examProgressInfo.value?.statusCode === 2 && item.ID === processItemType.exam);
}

function jumpTcp4tPage() {
  sendEvent('p4_study_p4_contact_click_next_exam');
  const path = router.resolve({
    name: PlatformEnterPoint.TCP4T,
    query: { fs: 1 },
  }).href;
  window.open(path, '_blank');
}

function handleLinkClick(e: MouseEvent) {
  const target = e.target as HTMLElement;
  if (target.tagName === 'A') {
    e.preventDefault();
    const url: string = target.getAttribute('href') as string;
    if (url) {
      sendEvent('p4_study_p4_contact_click_doc_link', {
        p4_study_doc_link: url,
      });
      window.open(url, '_blank');
    }
  }
}

async function clickNext() {
  if (showStep.value === step.value) {
    const res = await updateUserSchedule({
      bizId: 1,
      orientationId: processList.value[step.value].ID,
    });
    if (res?.code !== 7) {
      step.value++;
      dot.value = 0;
      await clickStep(-1, processList.value[step.value - 1]);
      showStep.value = step.value;
      lastDotNum.value = dotNum.value;
    }
  } else {
    clickStep(showStep.value + 1, processList.value[showStep.value]);
  }
}

async function clickStep(index, item, isViewClick = false) {
  if (index >= step.value && processType.value === 2 && examProgressInfo.value?.statusCode !== 2) {
    createMessage.error('请先完成考试');
    return;
  }
  if (isViewClick && index === showStep.value) {
    return;
  }
  if (index === step.value + 1 && dot.value === lastDotNum.value) {
    clickNext();
    return;
  }
  if (index > step.value) {
    createMessage.error('请先完成前序流程');
    return;
  }

  if (item.title === 'P4考试') {
    processType.value = 2;
    dotNum.value = 0;
    run();
  } else if (item.title === '获得提交权限') {
    processType.value = 3;
    dotNum.value = 0;
    const res2 = await getUserSchedule({ bizId: 1 });

    if (res2?.orientation?.status === 1) {
      step.value++;
      dot.value = 0;
      isAuthority.value = true;
    }
  } else {
    processType.value = 1;
    // 获取详细内容
    const { list } = await getOrientationByPage({ page: 1, pageSize: 100, parentId: item.ID });
    list?.sort((a, b) => {
      return a.order - b.order;
    });
    processDetailList.value = list || [];

    dotNum.value = processDetailList.value.length;
    if (universalBottomRef.value) {
      universalBottomRef.value.scrollLeft = 0;
    }

    universalBottomRef.value?.addEventListener('click', (e: any) => {
      if (e.target?.tagName === 'IMG') {
        curPreviewImg.value = e.target.src;
        document.body.style.overflow = 'hidden';
      }
    });
  }

  showStep.value = index;
}

function getClickScrollPosition(type) {
  const universalBottomRefValue = universalBottomRef?.value;

  if (universalBottomRefValue) {
    // 获取一栏的列数
    const columnWidth = Math.floor(universalBottomRefValue.clientWidth / 2) || 0;
    // 获取当前滚动位置
    const currentScrollPosition = universalBottomRefValue.scrollLeft || 0;

    // 计算下一个滚动位置在增加到 columnWidth 最近的倍数值
    let targetScrollPosition;

    if (type === 1) {
      targetScrollPosition
          = columnWidth
          * Math.floor(
            Number.isInteger(currentScrollPosition / columnWidth)
              ? currentScrollPosition / columnWidth - 1
              : currentScrollPosition / columnWidth,
          );
    } else {
      targetScrollPosition = columnWidth * Math.ceil(currentScrollPosition / columnWidth + 1);
    }

    // 使用 scrollTo 方法添加动画
    universalBottomRefValue.scrollTo({
      left: targetScrollPosition,
      behavior: 'smooth',
    });
  }
}

function handlePreviewClose() {
  curPreviewImg.value = '';
  document.body.style.overflow = '';
}

async function buttonClick(item, dotIndex) {
  if (showStep.value === step.value && dot.value === dotIndex) {
    await updateUserSchedule({ bizId: 1, orientationId: item.ID });
    dot.value = dotIndex + 1;
  } else {
    if (showStep.value === step.value && dot.value < dotIndex) {
      createMessage.error('你需要先了解左边一节的内容');
    }
  }
}

async function clickGetAuthority() {
  sendEvent('p4_study_p4_contact_click_get_submit_permission');
  const res = await updateUserSchedule({
    bizId: 1,
    orientationId: processList.value[step.value - 1].ID,
  });
  if (res?.code !== 7) {
    const res2 = await getAuthority();
    if (res2?.code !== 7) {
      step.value++;
      dot.value = 0;
      isAuthority.value = true;
    }
  }
}

async function mousewheel(event) {
  if (!universalBottomRef.value) {
    return;
  }
  universalBottomRef.value.scrollLeft += event.deltaY || event.deltaX;
}
function scroll() {
  setTimeout(() => {
    if (!universalBottomRef.value) {
      return;
    }
    if (universalBottomRef?.value?.scrollLeft === 0) {
      leftArrow.value = false;
    } else {
      leftArrow.value = true;
    }
    // 判断是否滚动底部

    if (
      universalBottomRef.value?.scrollLeft + universalBottomRef.value?.clientWidth
      >= universalBottomRef.value?.scrollWidth - 1
    ) {
      rightArrow.value = false;
    } else {
      rightArrow.value = true;
    }
  }, 100);
}
async function reloadTestStatus() {
  await getExamProgress();
}

function getStepById(type, parentId) {
  if (type === 'step') {
    let index: number;
    if (!parentId?.orientationId) {
      return 1;
    } else if (parentId.orientationId && !parentId.orientationParentId) {
      index = processList.value.findIndex((item) => parentId.orientationId === item.ID);
    } else {
      index = processList.value.findIndex((item) => parentId.orientationParentId === item.ID);
    }

    return index + 1 || 1;
  } else {
    if (!parentId) {
      return 0;
    }
    const index = processDetailList.value.findIndex((item) => parentId.orientationId === item.ID);
    return index + 1;
  }
}

onBeforeMount(async () => {
  if (fs !== '1') {
    go({ query: { fs: '1' } }, true);
  } else {
    // 获取数据
    const { list } = await getOrientationByPage({ page: 1, pageSize: 100 });
    list?.sort((a, b) => {
      return a.order - b.order;
    });
    processList.value = list || [];

    const { orientation } = await getUserSchedule({ bizId: 1 });
    // 获取步骤条位置
    step.value = getStepById('step', orientation);
    // 步骤条赋值
    showStep.value = step.value;
    // 模拟点击获取详细内容
    await clickStep(step.value, processList.value[step.value - 1]);
    // 获取子节点的位置
    dot.value = getStepById('button', orientation);
    lastDotNum.value = dotNum.value;
  }
});

onMounted(() => {
  if (/webkit\W(?!.*chrome).*safari\W/i.test((window.navigator && navigator.userAgent) || '')) {
    nextTick(() => {
      openModal(true, {});
    });
  }
});
onBeforeUnmount(() => {
  if (fs !== '0') {
    go({ query: { fs: '0' } }, true);
  }
});
watch(
  () => processDetailList.value,
  () => {
    scroll();
  },
);
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-touch-process';

.@{prefix-cls} {
  @apply bg-FO-Container-Fill1 h-full relative min-w-1475px;

  .vditor-reset {
    overflow: visible !important;
  }

  &__universal-bottom {
    overflow-x: auto;
    column-gap: 20px;
    column-fill: auto;
    column-rule: 2px solid #8d8d8d;
    column-count: 2;

    img {
      max-width: 85%;
      max-height: calc(-250px + 100vh);
    }
  }

  .bigImg {
    background-repeat: no-repeat;
    background-position: center;
  }

  &__btn {
    @apply font-bold font-size-16px !text-#fff !bg-#0B8600 flex items-center;

    &[gray='true'] {
      @apply !bg-#8d8d8d;
    }

    &[black] {
      @apply !bg-#515151;
    }

    &[reload] {
      animation: reload-test-status-animation 1ms 2s forwards;
      opacity: 0;
    }

    &[size='small'] {
      @apply font-size-3;
    }
  }

  &__img-preview {
    position: sticky;
    inset: 0;
    z-index: 9999;
    width: 100vw;
    height: 100vh;
    overflow: hidden;

    & .@{prefix-cls}__img-preview-mask {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100vw;
      height: 100vh;
      overflow: hidden;
      background-color: #00000090;

      & img {
        z-index: 1;
        max-width: 90vw;
        max-height: 90vh;
        background: #fff;
      }
    }
  }
}

@keyframes reload-test-status-animation {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
</style>
