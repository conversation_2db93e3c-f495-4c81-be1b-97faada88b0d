<template>
  <div class="flex items-stretch gap-10px">
    <div
      v-for="option in normalizedOptions"
      :key="getFieldValue(option, 'value')"
      class="flex-1 cursor-pointer border-2 rounded-8px p-12px transition-all"
      :class="getCardClass(option)"
      tabindex="0"
      role="radio"
      :aria-checked="isSelected(option)"
      :aria-disabled="disabled || option.disabled"
      @click="handleOptionClick(option)"
      @keydown.enter="handleOptionClick(option)"
      @keydown.space.prevent="handleOptionClick(option)"
    >
      <div class="flex items-start gap-2">
        <div class="flex-shrink-0">
          <slot name="icon" :option="option" :selected="isSelected(option)">
            <OptionCheckIcon
              v-if="isSelected(option)"
              class="text-18px c-FO-Brand-Primary-Default"
            />
            <div
              v-else
              class="box-border size-18px b-2 b-FO-Container-Fill3 rounded-full bg-FO-Container-Fill1"
            />
          </slot>
        </div>
        <div class="flex-1">
          <div class="FO-Font-B14">
            <slot name="title" :option="option">
              {{ getFieldValue(option, 'title') }}
            </slot>
          </div>
          <div class="FO-Font-R12 text-FO-Content-Text3">
            <slot name="description" :option="option">
              {{ getFieldValue(option, 'description') }}
            </slot>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import OptionCheckIcon from '../assets/icons/SystemFillCheck2.svg?component';

export interface CardSelectorOption {
  value: any;
  title?: string;
  description?: string;
  disabled?: boolean;
  [key: string]: any;
}

export interface CardSelectorFieldNames {
  value?: string;
  title?: string;
  description?: string;
}

const props = withDefaults(defineProps<{
  options?: CardSelectorOption[];
  multiple?: boolean;
  disabled?: boolean;
  fieldNames?: CardSelectorFieldNames;
}>(), {
  options: () => [],
  multiple: false,
  disabled: false,
  fieldNames: () => ({
    value: 'value',
    title: 'title',
    description: 'description',
  }),
});

const emit = defineEmits<{
  change: [value: any, option: CardSelectorOption | CardSelectorOption[]];
}>();

const modelValue = defineModel<any>();

// 标准化选项数据
const normalizedOptions = computed(() => {
  return props.options.map((option) => ({
    ...option,
    disabled: option.disabled || props.disabled,
  }));
});

// 获取字段值的辅助函数
function getFieldValue(option: CardSelectorOption, field: keyof CardSelectorFieldNames): any {
  const fieldName = props.fieldNames[field] || field;
  return option[fieldName];
}

// 检查选项是否被选中
function isSelected(option: CardSelectorOption): boolean {
  const optionValue = getFieldValue(option, 'value');

  if (props.multiple) {
    return Array.isArray(modelValue.value) && modelValue.value.includes(optionValue);
  }

  return modelValue.value === optionValue;
}

// 获取卡片样式类
function getCardClass(option: CardSelectorOption): string {
  const selected = isSelected(option);
  const disabled = option.disabled || props.disabled;

  let classes = '';

  if (disabled) {
    classes += 'opacity-60 cursor-not-allowed ';
  }

  if (selected) {
    classes += 'bg-FO-Container-Fill1 border-FO-Brand-Primary-Disabled ';
  } else {
    classes += 'border-FO-Container-Fill2 bg-FO-Container-Fill2 ';
  }

  return classes.trim();
}

// 处理选项点击
function handleOptionClick(option: CardSelectorOption): void {
  if (option.disabled || props.disabled) {
    return;
  }

  const optionValue = getFieldValue(option, 'value');
  let newValue: any;
  let changeOption: CardSelectorOption | CardSelectorOption[];

  if (props.multiple) {
    const currentValues = Array.isArray(modelValue.value) ? [...modelValue.value] : [];
    const index = currentValues.indexOf(optionValue);

    if (index > -1) {
      currentValues.splice(index, 1);
    } else {
      currentValues.push(optionValue);
    }

    newValue = currentValues;
    changeOption = props.options.filter((opt) =>
      currentValues.includes(getFieldValue(opt, 'value')),
    );
  } else {
    newValue = optionValue;
    changeOption = option;
  }

  modelValue.value = newValue;
  emit('change', newValue, changeOption);
}
</script>
