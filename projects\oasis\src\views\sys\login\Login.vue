<template>
  <div v-track:v="'syyviauq9u'" class="hypergryph-login">
    <div class="hypergryph-login__content">
      <div class="hypergryph-login__form">
        <LoginForm />
      </div>
    </div>
    <div class="hypergryph-login__footer">
      <Icon icon="login-footer-logo|svg" class="hypergryph-login__footer-icon" :size="200" />
    </div>
    <ThemeSwitcher
      class="pos-absolute right-0 top-50% z-100 font-size-[20px] c-FO-Content-Components1"
      placement="left"
      :theme="getDarkMode"
      :onToggle="setDarkMode"
    />
  </div>
</template>

<script lang="ts" setup>
import { ThemeSwitcher } from '@hg-tech/oasis-common';
import { useRootSetting } from '../../../hooks/setting/useRootSetting.ts';
import Icon from '../../../components/Icon';
import LoginForm from './LoginForm.vue';

const { getDarkMode, setDarkMode } = useRootSetting();
</script>

<style lang="less">
.hypergryph-login {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: @FO-Container-Background;

  &__content {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 100%;
    height: 100%;

    @media (max-width: @screen-md-max) {
      flex-direction: column;
      justify-content: center;
      background-position: bottom 18% right 9%;
      background-size: auto 190%;
    }
  }

  &__logo {
    width: 300px;
    min-width: 100px;
    margin: 0 30px 0 200px;

    @media (max-width: @screen-md-max) {
      width: 50vw;
      max-width: 300px;
      margin: 0;
    }
  }

  &__form {
    width: 484px;
    min-width: 484px;
    height: 620px;

    @media (max-width: @screen-md-max) {
      width: 80vw;
      max-width: 410px;
      margin: 0;
    }
  }

  &__footer {
    display: flex;
    position: absolute;
    bottom: 24px;
    align-items: center;
    justify-content: center;
    width: 100vw;

    &-icon {
      width: 200px;
      height: 40px !important;
    }
  }
}
</style>
