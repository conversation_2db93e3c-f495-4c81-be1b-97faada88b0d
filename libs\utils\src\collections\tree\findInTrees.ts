import { isNotEmptyArray } from '../array';

export interface FindInTreeResult<T, Key> {
  path: Key[];
  target?: T;
}
export interface FindInTreeOptions<KeyField extends string, ChildrenField extends string > {
  keyField?: KeyField;
  childrenField?: ChildrenField;
  order?: 'pre' | 'post' ;
}

export function findInTrees<T extends { key: NonNullable<any>; children?: T[] }>(
  trees: T[],
  judgeFn: (node: T) => boolean,
): FindInTreeResult<T, T['key']>;

export function findInTrees<
  T extends { [K in KeyField]: NonNullable<any> } & { [C in ChildrenField]?: T[] },
  KeyField extends string = 'key',
  ChildrenField extends string = 'children',
>(
  trees: T[],
  judgeFn: (node: T) => boolean,
  options?: FindInTreeOptions<KeyField, ChildrenField>,
): FindInTreeResult<T, T[KeyField]>;

/**
 * 查到树中的某个节点，返回节点及其路径
 * @return path 包含找到的节点本身，如果没有找到则返回空数组，target 为找到的节点
 */
export function findInTrees<
  T extends { [K in KeyField]: NonNullable<any> } & { [C in ChildrenField]?: T[] },
  KeyField extends string = 'key',
  ChildrenField extends string = 'children',
>(
  trees: T[],
  judgeFn: (node: T) => boolean,
  options?: FindInTreeOptions<KeyField, ChildrenField>,
): FindInTreeResult<T, T[KeyField]> {
  const {
    keyField = 'key' as KeyField,
    childrenField = 'children' as ChildrenField,
    order = 'pre',
  } = options || {};

  const existingNodes = new WeakSet<T>();

  return (function dfsTraverse(
    nodes: T[],
    parentPath: T[KeyField][] = [],
  ): FindInTreeResult<T, T[KeyField]> {
    for (const node of nodes) {
      if (existingNodes.has(node)) {
        // 自引用
        continue;
      } else if (node) {
        existingNodes.add(node);
      }

      const currentKey = node?.[keyField];
      if (currentKey == null) {
        console.warn(`[findInTrees] 节点缺少 ${keyField} 字段作为唯一值`, node);
      }

      const currentPath = [...parentPath, currentKey];

      switch (order) {
        case 'post':
          // 后序遍历
          if (isNotEmptyArray(node?.[childrenField])) {
            const childFoundItem = dfsTraverse(node[childrenField] || [], currentPath);
            const found = childFoundItem.path.length > 0; // 使用 path 而不是 result 判断是否找到
            if (found) {
              return childFoundItem;
            }
          }

          if (judgeFn(node)) {
            return { path: currentPath, target: node };
          }
          break;
        case 'pre':
        default:
          // 前序遍历（默认）
          if (judgeFn(node)) {
            return { path: currentPath, target: node };
          }

          if (isNotEmptyArray(node?.[childrenField])) {
            const childFoundItem = dfsTraverse(node[childrenField] || [], currentPath);
            const found = childFoundItem.path.length > 0; // 使用 path 而不是 result 判断是否找到
            if (found) {
              return childFoundItem;
            }
          }
          break;
      }
    }

    return {
      path: [],
      target: undefined,
    };
  })(trees);
}
