import type { Router } from 'vue-router';
import { getAllProjectList } from '../../hooks/useProjects.ts';
import { message as Message } from 'ant-design-vue/es/components';
import { useUserStoreWithOut } from '../../store/modules/user.ts';

async function getUserStoreProjectId() {
  const userStore = useUserStoreWithOut();
  if (!userStore.getUserInfo?.lastProjectId) {
    await userStore.getUserInfoAction();
  }

  return userStore.getUserInfo?.lastProjectId;
}

/**
 * 设置当前项目 ID，返回期望的项目 ID
 * 当前可以认为 projectId 是全局恒定有的，无论页面是否依赖均需要取得用户合法的项目 ID
 * @description 路由 ID > 内存 ID > 服务端 ID > 第一个合法的项目 ID
 */
async function setPreferredProjectId(urlProjectId?: number) {
  const userStore = useUserStoreWithOut();
  if (!userStore.getToken && !userStore.getAccessToken) {
    // 没有登录信息
    return undefined;
  }

  const userProjects = await getAllProjectList();
  if (userProjects.some((project) => project.ID === urlProjectId)) {
    // 当前 Url 合法，更新内存中的项目 ID
    userStore.setProjectId(urlProjectId);
    return urlProjectId;
  }

  const cachedProjectId = userStore.getProjectId;
  if (userProjects.some((project) => project.ID === cachedProjectId)) {
    // 缓存的项目 ID 合法，更新路由
    return cachedProjectId;
  }

  const serverProjectId = await getUserStoreProjectId();
  if (userProjects.some((project) => project.ID === serverProjectId)) {
    // 服务端的项目 ID 合法
    userStore.setProjectId(serverProjectId);
    return serverProjectId;
  }

  if (userProjects.length) {
    // 均不合法，需要初始化项目 ID
    const validProject = userProjects[0];
    if (validProject?.ID) {
      if (urlProjectId || serverProjectId || cachedProjectId) {
        // 如果有非法 ID 则提示
        Message.error(`无权限访问该项目, 已为您重置项目到 ${validProject.name}。`);
      }
      // 如果有合法的项目 ID，更新路由
      userStore.setProjectId(validProject.ID);
      return validProject.ID;
    }
  }

  return undefined;
}

export function withProjectEnter(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const urlProjectId = Number(to.query.p) || undefined;
    const nextProjectId = await setPreferredProjectId(urlProjectId);

    if (urlProjectId !== nextProjectId) {
      // 1. 跳转到新的项目 ID
      // 2. nextProjectId 可能为 undefined，因为某些页面可能不需要项目 ID，则去除 p 直接访问
      return next({
        ...to,
        query: {
          ...to.query,
          p: nextProjectId,
        },
      });
    }

    // 其他情况
    return next();
  });
}
