import { createRouter, createWebHistory } from 'vue-router';
import type { App } from 'vue';
import { withDocumentTitle } from './modules/withDocumentTitle.ts';
import { withTrack } from './modules/withTrack.ts';
import { PlatformEnterPoint, PlatformRoutePath } from '@hg-tech/oasis-common';

export const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      name: PlatformEnterPoint.PermissionCenterDashboard,
      path: PlatformRoutePath.PermissionCenterDashboard,
      component: () => import('../views/dashboard/AppList.vue'),
      meta: {
        title: '权限管理中心',
      },
    },
    {
      name: PlatformEnterPoint.PermissionCenterApp,
      path: PlatformRoutePath.PermissionCenterApp,
      component: () => import('../views/app/RoleConfig.vue'),
      meta: {
        title: '权限应用详情',
      },
    },
    {
      name: PlatformEnterPoint.PermissionCenterManagement,
      path: PlatformRoutePath.PermissionCenterManagement,
      component: () => import('../views/manage/CheckPointConfig.vue'),
      meta: {
        title: '编辑权限接口',
      },
    },
    {
      name: PlatformEnterPoint.PermissionCenterQuery,
      path: PlatformRoutePath.PermissionCenterQuery,
      component: () => import('../views/query/Tab.vue'),
      meta: {
        title: '权限查询',
      },
    },
    {
      path: '/:error(.*)*',
      component: () => import('../views/Redirect.vue'),
    },
  ],
  strict: true,
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

export function setupRouter(app: App<Element>) {
  app.use(router);

  // with plugins
  withDocumentTitle(router);
  withTrack(router);
}
