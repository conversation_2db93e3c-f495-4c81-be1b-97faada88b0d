<template>
  <PageWrapper
    v-track:v="'oyneqyu2zo'"
    :class="prefixCls"
    :backIcon="isSystemPage ? false : undefined"
    @back="goBack"
  >
    <template #title>
      <EllipsisText class="max-w-500px self-center text-xl font-bold">
        {{ getTitle }}
      </EllipsisText>
    </template>
    <template #subTitle>
      <div class="ml-2 pt-2px">
        <span>配置最底层的P4访问权限，用于给予或限制项目组成员目录的访问权限</span>
      </div>
    </template>
    <template #footer>
      <div class="flex flex-wrap-reverse items-center justify-between gap-y-4 pb-6">
        <div class="flex items-center">
          <LineTab
            v-if="curScenarioID"
            :tabList="tabList"
            :defaultActiveTab="curScenarioID"
            tabMargin="24px"
            showIcon
            @change="handleScenarioChange"
          >
            <template #item="{ item }">
              <APopover
                :content="item.description"
                placement="bottom"
                :overlayClassName="`max-w-300px ${item.description ? '' : 'hidden'}`"
                :mouseLeaveDelay="0"
              >
                <div class="max-w-220px flex items-center">
                  <Icon
                    icon="carbon:dot-mark"
                    :class="item.activate ? '!c-FO-Functional-Success1-Default' : '!text-gray-400'"
                  />
                  <EllipsisText>{{ item.title }}</EllipsisText>
                </div>
              </APopover>
            </template>
          </LineTab>
          <div class="ml-6">
            <template v-if="tabList?.length > 0 && !curScenario?.activate">
              <BasicButton
                v-if="hasP4PermissionConfigPermission"
                class="custom-rounded-btn mr-4"
                borderColor="success"
                noIcon
                @click="handleEffect"
              >
                使当前场景组生效
              </BasicButton>
            </template>
            <BasicButton
              v-if="hasP4PermissionConfigPermission"
              v-track="'2icejdkowc'"
              class="custom-rounded-btn"
              noIcon
              @click="handleScenarioManage"
            >
              管理权限场景组
            </BasicButton>
          </div>
        </div>
        <BasicButton v-if="hasP4PermissionConfigPermission" v-track="'unmahbwqyz'" class="custom-rounded-btn mr-4" @click="handleAdd()">
          <Icon icon="ant-design:plus-outlined" />
          新增权限
        </BasicButton>
      </div>
    </template>
    <div class="mb-4 rounded bg-FO-Container-Fill1 p-4 c-FO-Functional-Warning1-Default font-bold">
      当同一个干员在同一个子路径下具有不同权限时，排序最上方的P4权限组生效
    </div>
    <div :class="`${prefixCls}__list`">
      <div
        v-for="item in groupPermissions"
        :key="item.comment"
        class="mb-4 flex items-center rounded bg-FO-Container-Fill1 p-4"
        :data-id="item.comment"
      >
        <Icon v-if="hasP4PermissionConfigPermission" icon="ic:round-drag-handle" :size="20" :class="`${prefixCls}__drag-btn`" />
        <div>
          <div class="flex items-center">
            <div class="mr-2 max-w-600px font-bold">
              <EllipsisText>{{ item.comment }}</EllipsisText>
            </div>
            <BasicButton
              v-if="hasP4PermissionConfigPermission"
              v-track="'1dyzmxzq0q'"
              type="text"
              size="small"
              @click="handleEdit(item.permissions)"
            >
              <Icon icon="icon-park-outline:edit" />
            </BasicButton>
            <APopconfirm v-if="hasP4PermissionConfigPermission" title="确认删除该权限吗？" @confirm="handleDelete(item.permissions)">
              <BasicButton type="text" danger size="small">
                <Icon icon="icon-park-outline:delete" />
              </BasicButton>
            </APopconfirm>
          </div>
          <div class="my-2 flex flex-wrap items-center gap-x-4 gap-y-2">
            <div v-for="(path, i) in getPaths(item.permissions)" :key="i" class="">
              {{ path }}
            </div>
          </div>
          <div class="flex flex-wrap gap-2">
            <div
              v-for="combine in getCombinePermissionList(item.permissions)"
              :key="JSON.stringify(combine)"
              :class="`${prefixCls}__permit-item`"
            >
              <APopover
                :content="getAccessLevelByID(combine.accessLevelID)?.descriptionCn"
                overlayClassName="max-w-300px"
              >
                <div
                  class="mr-6 cursor-pointer rounded-3px px-2 py-1 c-FO-Content-Components1 font-bold"
                  :class="[
                    Boolean(getAccessLevelByID(combine.accessLevelID)?.realID) || getAccessLevelByID(combine.accessLevelID)?.isDeny ? 'bg-FO-Functional-Error1-Default' : 'bg-FO-Brand-Primary-Default',
                  ]"
                >
                  {{ getAccessLevelByID(combine.accessLevelID)?.displayName }}
                </div>
              </APopover>
              <div class="flex flex-wrap items-center gap-2">
                <div
                  v-for="member in getMemberOrGroupList(combine.permissions)"
                  :key="member"
                  :class="`${prefixCls}__permit-item-child`"
                >
                  {{ getUserDisplayName(member) }}
                </div>
                <div
                  v-for="group in getMemberOrGroupList(combine.permissions, true)"
                  :key="group"
                  :class="`${prefixCls}__permit-item-child`"
                  isGroup
                >
                  <GroupUsersPopover :groupName="group" :serverID="curServerID">
                    {{ group }}
                  </GroupUsersPopover>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ScenarioModal @register="registerModal" @success="handleSuccess" />
    <PermissionDrawer @register="registerDrawer" @success="handlePermissionSuccess" />
  </PageWrapper>
</template>

<script lang="ts" setup>
import { Popconfirm as APopconfirm, Popover as APopover } from 'ant-design-vue';
import { cloneDeep, omit, orderBy, union } from 'lodash-es';
import { computed, nextTick, onBeforeMount, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useP4Depot } from '../p4PermissionManage/hook';
import PermissionDrawer from './PermissionDrawer.vue';
import ScenarioModal from './ScenarioModal.vue';
import type {
  GroupPerforcePermissionsListItem,
  PerforceGroupsListItem,
  PerforcePermissionsListItem,
  PerforceScenariosListItem,
} from '/@/api/page/model/perforceModel';
import {
  batchPerforcePermissionsListSort,
  deletePerforcePermission,
  editPerforceScenarios,
  effectPerforcePermission,
  getPerforceGroupsAndLDAPGroupListByPage,
  getPerforcePermissionsListByPage,
  getPerforceScenariosListByPage,
} from '/@/api/page/perforce';
import { useDrawer } from '/@/components/Drawer';
import GroupUsersPopover from '/@/components/GroupUsersPopover';
import Icon from '/@/components/Icon';
import LineTab from '/@/components/LineTab';
import { useModal } from '/@/components/Modal';
import { PageWrapper } from '/@/components/Page';
import { useTrack } from '/@/hooks/system/useTrack';
import { useUserList } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useGo } from '/@/hooks/web/usePage';
import { useSortable } from '/@/hooks/web/useSortable';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { isNullOrUnDef } from '/@/utils/is';
import {
  getMemberOrGroupList,
  usePerforceAccessLevel,
  usePerforceServer,
} from '/@/views/versionControl/perforceManagement/hook';
import { EllipsisText } from '../../../components/EllipsisText';
import { BasicButton } from '../../../components/Button';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { usePermissionCheckPoint } from '/@/service/permission/usePermission';

const [hasP4PermissionConfigPermission] = usePermissionCheckPoint({
  scope: PlatformEnterPoint.DevGuard,
  any: ['p4_permission_config'],
}, { skipWhenSuperAdmin: true });
const { prefixCls } = useDesign('perforce-management');
const go = useGo();
const { currentRoute } = useRouter();
const urlDepotID = Number(currentRoute.value.params.id);
const userStore = useUserStoreWithOut();
const { createMessage } = useMessage();
const { curServer, getPerforceServerList } = usePerforceServer();
const {
  getAccessLevelList,
  perforceAccessLevelList,
  getAccessLevelByID,
  getCombinePermissionList,
} = usePerforceAccessLevel();

const { getDepotsList, curDepot } = useP4Depot(urlDepotID);
const [registerModal, { openModal }] = useModal();
const [registerDrawer, { openDrawer }] = useDrawer();
const { userList, getUserList } = useUserList();
const curServerID = ref<number>();
const { setTrack } = useTrack();

// 是否总览页
const isSystemPage = computed(() => currentRoute.value.name === 'SystemPerforceManagement');
const depotID = computed(() => (isSystemPage.value ? 0 : urlDepotID));
const getTitle = computed(() => (isSystemPage.value
  ? '系统P4权限列表'
  : `P4权限配置: ${curDepot.value?.description || curDepot.value?.prefix}`
));

const projectID = computed(() => (isSystemPage.value ? 0 : userStore.getProjectId));
const scenarioList = ref<PerforceScenariosListItem[]>([]);
const curScenarioID = ref<string>();
const groupList = ref<PerforceGroupsListItem[]>([]);
const perforcePermissionList = ref<PerforcePermissionsListItem[]>([]);
const groupPermissions = ref<GroupPerforcePermissionsListItem[]>([]);
const tabList = computed(() =>
  scenarioList.value.map((e) => ({
    ...e,
    name: e.ID!.toString(),
    title: e.name!,
  })));
const curScenario = computed(() =>
  scenarioList.value.find((e) => e.ID?.toString() === curScenarioID.value));

// 获取场景列表
async function getScenariosList(id?: number, systemOnly?: boolean) {
  if (!projectID.value || !depotID.value) {
    return;
  }

  const { list } = await getPerforceScenariosListByPage(projectID.value, depotID.value, {
    page: 1,
    pageSize: 999,
    systemOnly,
  });

  if (list?.length > 0) {
    scenarioList.value = list;

    const curActive = list.find((e) => e.activate);

    curScenarioID.value = (id || curActive?.ID || list[0]?.ID)?.toString();
  } else {
    scenarioList.value = [];
    curScenarioID.value = undefined;
  }
}

// 获取所有组(包括LDAP)列表
async function getAllGroupList() {
  if (isNullOrUnDef(curServerID.value)) {
    groupList.value = [];

    return;
  }
  if (!projectID.value) {
    return;
  }

  const { list } = await getPerforceGroupsAndLDAPGroupListByPage(projectID.value, {
    serverID: curServerID.value,
  });

  groupList.value = list || [];
}

// 获取权限列表
async function getPermissionList(systemOnly?: boolean) {
  if (!projectID.value || isNullOrUnDef(depotID)) {
    return;
  }

  const { list } = await getPerforcePermissionsListByPage(projectID.value, depotID.value, {
    page: 1,
    pageSize: 999,
    systemOnly,
    // 系统页面的全部分类仅显示激活的
    activateOnly: isSystemPage.value && !systemOnly,
    // 不是系统页面的全部分类才需要
    scenarioID: isSystemPage.value && !systemOnly ? undefined : Number(curScenarioID.value),
    serverID: curServerID.value,
  });

  if (list?.length > 0) {
    const sortList = orderBy(list, 'sort', 'desc');

    // 系统页面显示全部时再根据项目id排序一次
    if (isSystemPage.value && !systemOnly) {
      orderBy(sortList, 'projectID');
    }

    perforcePermissionList.value = sortList;

    const temp: GroupPerforcePermissionsListItem[] = [];

    sortList.forEach((e) => {
      if (!e.comment) {
        e.comment = e.path;
      }

      const index = temp.findIndex((item) => item.comment === e.comment);

      if (index === -1) {
        temp.push({
          comment: e.comment!,
          permissions: [e],
        });
      } else {
        temp[index].permissions.push(e);
      }
    });
    groupPermissions.value = temp;
  } else {
    perforcePermissionList.value = [];
    groupPermissions.value = [];
  }
}

function getUserDisplayName(userName: string) {
  const user = userList.value.find((e) => e.userName === userName);

  return user?.displayName || userName;
}

// 设置当前场景为激活状态
async function setCurScenarioActive() {
  if (!curScenario.value || !userStore.getProjectId) {
    return;
  }

  return await editPerforceScenarios(
    userStore.getProjectId,
    depotID.value,
    Object.assign(
      {},
      omit(curScenario.value, [
        'CreatedAt',
        'UpdatedAt',
        'ID',
        'permissions',
        'projectID',
        'depotID',
      ]),
      { activate: true },
    ),
    Number(curScenarioID.value)!,
  );
}

function getPaths(list: PerforcePermissionsListItem[]) {
  const temp: string[] = [];

  list.forEach((e) => {
    if (!temp.includes(e.path!)) {
      temp.push(e.path!);
    }
  });

  return temp;
}

function handleScenarioManage() {
  openModal(true, {
    curScenarioID: Number(curScenarioID.value),
  });
}

async function handleScenarioChange(tab: string) {
  curScenarioID.value = tab;
  await getPermissionList();
}

async function handleSuccess(id?: number) {
  await getScenariosList(id);
  await getPermissionList();
}

const drawerData = computed(() => ({
  perforceAccessLevelList: perforceAccessLevelList.value,
  groupList: groupList.value,
  projectID: projectID.value,
  depotID: depotID.value,
  curDepot: curDepot.value,
  scenarioID: Number(curScenarioID.value),
  serverID: curServerID.value,
  existComments: groupPermissions.value.map((e) => e.comment),
}));

function handleAdd() {
  openDrawer(true, drawerData.value);
}

function handleEdit(item: PerforcePermissionsListItem[]) {
  openDrawer(true, {
    isUpdate: true,
    list: item,
    ...drawerData.value,
  });
}

async function handleDelete(item: PerforcePermissionsListItem[]) {
  const deleteApiList = item.map((e) =>
    deletePerforcePermission(
      e.projectID || projectID.value!,
      e.depotID || depotID.value,
      e.ID!,
    ));

  try {
    const res = await Promise.all(deleteApiList);
    let hasError = false;

    res.forEach((e) => {
      if (e?.code === 7) {
        hasError = true;
      }
    });

    if (!hasError) {
      if (curScenario.value?.activate) {
        handleEffect();
        createMessage.success('保存成功，P4权限配置已生效', 5);
      } else {
        createMessage.success('删除成功');
      }

      setTrack('53ntzgo9pp');
    }
  } finally {
    if (!curScenario.value?.activate) {
      handleSuccess(Number(curScenarioID.value));
    }
  }
}

// 生效操作
async function handleEffect() {
  if (!curServerID.value && !curDepot.value?.serverID) {
    return;
  }

  const res = await setCurScenarioActive();

  if (res?.code === 7) {
    return;
  }

  const effectRes = await effectPerforcePermission(curServerID.value!, 'none');

  if (effectRes?.code === 7) {
    return;
  }

  if (!curScenario.value?.activate) {
    createMessage.success('保存成功，当前P4权限组已生效', 5);
  }

  setTrack('rg7rvbu1lj');
  await handleSuccess(Number(curScenarioID.value));
}

// 批量排序
async function batchSortList(idList: number[], scenarioID?: number) {
  if (!projectID.value || !depotID.value) {
    return;
  }
  const res = await batchPerforcePermissionsListSort(
    projectID.value,
    depotID.value,
    scenarioID || Number(curScenarioID.value),
    {
      idList: idList.reverse(),
    },
  );

  handleSuccess(scenarioID || Number(curScenarioID.value));

  return res;
}

async function handlePermissionSuccess(id?: number, originComment?: string, newComment?: string) {
  if (!!originComment || !!newComment) {
    // 获取原始路径权限位置
    const originIndex = groupPermissions.value.findIndex((e) => e.comment === originComment);

    await handleSuccess(id);

    // 获取新路径权限位置
    const newIndex = groupPermissions.value.findIndex((e) => e.comment === newComment);
    // 将位置还原
    const temp = cloneDeep(groupPermissions.value);
    const current = temp[newIndex];

    temp.splice(newIndex, 1);
    temp.splice(originIndex > -1 ? originIndex : 0, 0, current);

    // 获取还原后的权限id列表
    const realIDList: number[] = [];

    temp.forEach((e) => {
      e.permissions.forEach((item) => {
        realIDList.push(item.ID!);
      });
    });

    const res = await batchSortList(realIDList, id);

    if (!(res?.code === 7)) {
      if (curScenario.value?.activate) {
        handleEffect();
        createMessage.success('保存成功，P4权限配置已生效', 5);
      } else {
        createMessage.success('保存成功，但当前权限组未生效，可点击 使当前场景组生效 按钮', 5);
      }
    }
  } else {
    await handleSuccess(id);
  }
}

function initDrag() {
  // 拖动排序功能
  nextTick(() => {
    const el = document.querySelector(`.${prefixCls}__list`) as HTMLElement;
    const { initSortable, sortableRef } = useSortable(el, {
      handle: `.${prefixCls}__drag-btn`,
      dataIdAttr: 'data-id',
      onEnd: async ({ oldIndex, newIndex }) => {
        if (isNullOrUnDef(oldIndex) || isNullOrUnDef(newIndex) || oldIndex === newIndex) {
          return;
        }

        const newList = union(
          ...sortableRef.value
            .toArray()
            .map((e: any) => groupPermissions.value?.find((group) => group.comment === e)?.permissions),
        ) as PerforcePermissionsListItem[];

        const res = await batchSortList(newList.map((e) => e.ID!));

        if (!(res?.code === 7)) {
          if (curScenario.value?.activate) {
            handleEffect();
            createMessage.success('保存成功，P4权限配置已生效', 5);
          } else {
            createMessage.success('排序成功, 请点击 使当前场景组生效 按钮生效', 5);
          }
        }
      },
    });

    initSortable();
  });
}

async function init() {
  await getAccessLevelList();
  await getDepotsList();
  curServerID.value = curDepot.value?.serverID;
  await getAllGroupList();
  await getScenariosList();

  if (isSystemPage.value) {
    await getPerforceServerList();
    curServerID.value = curServer.value?.ID;
  }

  getUserList();
  getPermissionList();
  initDrag();
}

onBeforeMount(() => {
  init();
});

function goBack() {
  go({ name: 'P4Depots' });
}

watch(
  () => userStore.getProjectId,
  (v, oldValue) => {
    if (v && v !== oldValue) {
      // 切换项目后返回仓库列表
      go({ name: 'P4Depots' });
    }
  },
);
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-perforce-management';
.@{prefix-cls} {
  &__permit-item {
    display: flex;
    align-items: center;
    padding: 4px;
    border: 1px solid @FO-Content-Text1;
    border-radius: 4px;

    &-child {
      padding: 2px;
      border-radius: 4px;

      &[isGroup] {
        padding: 2px 8px;
        background-color: @member-card-background;
        cursor: pointer;
      }
    }
  }

  &__drag-btn {
    margin-right: 16px;
    color: @FO-Content-Text2;
    cursor: grab;

    &[disabled='true'] {
      cursor: not-allowed;
    }
  }

  &__list {
    min-height: 200px;
    max-height: calc(100vh - 340px);
    overflow: auto;
  }
}
</style>
