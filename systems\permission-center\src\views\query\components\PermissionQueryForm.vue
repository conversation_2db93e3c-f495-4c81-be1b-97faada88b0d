<template>
  <div>
    <Form :model="form">
      <!-- 主要查询条件 -->
      <div class="flex items-start justify-between">
        <div class="flex flex-wrap items-start gap-x-24px">
          <FormItem
            label="应用"
            name="appId"
            v-bind="validateInfos.appId"
          >
            <div class="w-320px">
              <Select
                v-model:value="form.appId"
                placeholder="请选择应用"
                :options="appOptions"
                :loading="appLoading"
                showSearch
                optionFilterProp="name"
                :fieldNames="{ label: 'name', value: 'id' }"
                :allowClear="true"
                @change="handleAppChange"
              />
            </div>
          </FormItem>

          <FormItem
            v-if="showTenantSelector"
            label="项目"
            name="tenantIds"
            v-bind="validateInfos.tenantIds"
          >
            <div class="w-320px">
              <Select
                v-model:value="form.tenantIds"
                mode="multiple"
                placeholder="请选择项目"
                :showArrow="true"
                :options="tenantOptions"
                :fieldNames="{ label: 'tenant', value: 'id' }"
                :loading="tenantLoading"
                optionFilterProp="tenant"
                showSearch
                :maxTagCount="2"
                :maxTagTextLength="6"
                :allowClear="true"
                @search="(val) => tenantSearchValue = val"
              >
                <template #dropdownRender="{ menuNode }">
                  <div>
                    <div v-if="!tenantSearchValue || tenantSearchValue.trim() === ''" class="flex items-center justify-between border-b border-FO-Container-Stroke1 p-8px">
                      <span class="FO-Font-R12 c-FO-Content-Text3">
                        共 {{ tenantOptions.length }} 个项目
                        <template v-if="form.tenantIds && form.tenantIds.length > 0">
                          ，已选择 {{ form.tenantIds.length }} 个
                        </template>
                      </span>
                      <Button type="link" size="small" @click="handleSelectAllTenants">
                        {{ isAllTenantsSelected ? '取消全选' : '选择全部' }}
                      </Button>
                    </div>
                    <component :is="menuNode" />
                  </div>
                </template>
              </Select>
            </div>
          </FormItem>

          <FormItem
            label="权限"
            name="permissionId"
            v-bind="validateInfos.permissionId"
          >
            <div class="w-320px">
              <Select
                v-model:value="form.permissionId"
                placeholder="请选择权限"
                showSearch
                optionFilterProp="label"
                :allowClear="true"
                :loading="loadingPermissions"
                :disabled="!form.appId"
              >
                <SelectOptGroup
                  v-for="category in categoriesWithPermissions"
                  :key="category.id"
                  :label="category.name"
                >
                  <SelectOption
                    v-for="permission in category.resources"
                    :key="`${category.id}-${permission.id}`"
                    :value="permission.id"
                    :label="permission.name"
                  >
                    {{ permission.name }}
                  </SelectOption>
                </SelectOptGroup>
              </Select>
            </div>
          </FormItem>
        </div>

        <div class="flex-shrink-0">
          <Button
            class="btn-fill-primary h-8"
            :loading="querying"
            @click="handleQuery"
          >
            <template #icon>
              <SearchIcon />
            </template>
            查询
          </Button>
        </div>
      </div>
    </Form>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { Button, Form, FormItem, Select, SelectOptGroup, SelectOption } from 'ant-design-vue';
import { useLatestPromise } from '@hg-tech/utils-vue';
import type { RuleObject } from 'ant-design-vue/es/form/interface';
import type { SelectValue } from 'ant-design-vue/es/select';
import SearchIcon from '../../../assets/icons/SystemStrokeSearch.svg?component';
import { getPermissionApps, getPermissionAppTenantList } from '../../../api/app';
import type { GetPermissionMembersRequest } from '../../../api/query';
import type { PermissionAppInfoV2, PermissionTenantInfo } from '../../../api/app';
import { usePermissionCategories } from '../../../composables/usePermissionCategories';
import { useUserProfileInfoStore } from '../../../store/modules/userProfileInfo';

interface QueryFormData {
  appId?: number;
  tenantIds?: number[];
  permissionId?: number;
}

const props = defineProps<{
  initialAppId?: number;
  initialTenantId?: number;
}>();

const emit = defineEmits<{
  query: [params: GetPermissionMembersRequest];
}>();

const form = ref<QueryFormData>({
  appId: undefined,
  tenantIds: undefined,
  permissionId: undefined,
});

const querying = ref(false);

/** 项目搜索状态 */
const tenantSearchValue = ref('');
const userProfileInfoStore = useUserProfileInfoStore();

/** 应用选择器相关 */
const { data: appsData, execute: fetchApps, loading: appLoading } = useLatestPromise(getPermissionApps);
const appOptions = computed<PermissionAppInfoV2[]>(() => {
  return (userProfileInfoStore.isSuperAdmin ? appsData.value?.data?.data?.allApp : appsData.value?.data?.data?.myApp) || [];
});

/** 是否显示项目选择器 */
const showTenantSelector = computed(() => !!appOptions.value.find((app) => app.id === form.value.appId)?.isMultiTenant);

/** 项目选择器相关 */
const { data: tenantsData, execute: fetchTenants, loading: tenantLoading } = useLatestPromise(getPermissionAppTenantList);

const tenantOptions = computed<PermissionTenantInfo[]>(() => {
  const responseData = tenantsData.value?.data;
  if (responseData?.code === 0 && responseData?.data) {
    return responseData.data;
  }
  return [];
});

/** 计算是否全选了所有项目 */
const isAllTenantsSelected = computed(() => {
  if (!form.value.tenantIds || tenantOptions.value.length === 0) {
    return false;
  }
  return form.value.tenantIds.length === tenantOptions.value.length;
});

/** 权限选择器相关 */
const { categoriesWithUncategorized, loading: loadingPermissions, refreshData } = usePermissionCategories();

// 过滤分类中有权限的分类
const categoriesWithPermissions = computed(() => {
  return categoriesWithUncategorized.value.filter((category) =>
    category.resources && category.resources.length > 0,
  );
});

/** 表单验证规则 */
const formRule = computed<Record<keyof QueryFormData, RuleObject[]>>(() => ({
  appId: [
    { required: true, message: '请选择应用' },
  ],
  tenantIds: [
    {
      required: showTenantSelector.value,
      message: '请选择项目',
      validator: () => {
        if (showTenantSelector.value && !form.value.tenantIds?.length) {
          return Promise.reject(new Error('请选择项目'));
        }
        return Promise.resolve();
      },
    },
  ],
  permissionId: [
    { required: true, message: '请选择权限' },
  ],
}));

const { validate, validateInfos, clearValidate } = Form.useForm(form, formRule);

/**
 * 应用选择变化处理
 * @param value 选择的应用ID
 */
async function handleAppChange(value: SelectValue) {
  // 清空项目选择和权限选择
  form.value.tenantIds = [];
  form.value.permissionId = undefined;

  // 如果是项目应用，加载项目列表
  const appId = value as number;
  const selectedApp = appOptions.value.find((app) => app.id === appId);
  if (selectedApp?.isMultiTenant && appId) {
    fetchTenants({ appId }, {});
    clearValidate('tenantIds');
  }

  // 加载权限列表
  if (appId) {
    await refreshData(appId);
    clearValidate('permissionId');
  }
}

/** 选择全部项目/取消全选 */
function handleSelectAllTenants() {
  if (isAllTenantsSelected.value) {
    // 取消全选
    form.value.tenantIds = [];
  } else {
    // 全选
    form.value.tenantIds = tenantOptions.value.map((tenant) => tenant.id!);
  }
}

/** 查询处理 */
async function handleQuery() {
  try {
    await validate();
    querying.value = true;

    const params: GetPermissionMembersRequest = {
      appId: form.value.appId!,
      resourceId: form.value.permissionId!,
    };

    if (showTenantSelector.value && form.value.tenantIds) {
      // 将数字ID转换为字符串
      params.tenantIds = form.value.tenantIds.map(String);
    }

    emit('query', params);
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    querying.value = false;
  }
}

/** 监听应用选择变化，重置项目选择和权限选择 */
watch(() => form.value.appId, () => {
  form.value.tenantIds = [];
  form.value.permissionId = undefined;
});

/** 初始化应用列表 */
fetchApps({}, {});

/** 预填应用和租户ID - 需要等待应用列表加载完成 */
watch(() => [props.initialAppId, props.initialTenantId, appOptions.value?.length], async ([appId, tenantId, appOptionsLength]) => {
  if (appId && typeof appId === 'number' && appOptionsLength) {
    form.value.appId = appId;

    // 检查是否是多租户应用
    const selectedApp = appOptions.value.find((app) => app.id === appId);
    if (selectedApp?.isMultiTenant) {
      // 加载租户列表
      await fetchTenants({ appId }, {});
      // 如果有指定租户ID，则预选择该租户
      if (tenantId) {
        const targetTenant = tenantOptions.value.find((tenant) => tenant.id === tenantId);
        if (targetTenant && targetTenant.id) {
          form.value.tenantIds = [targetTenant.id];
          clearValidate();
        }
      }
    }

    // 加载权限列表
    await refreshData(appId);
  }
}, { immediate: true });
</script>
