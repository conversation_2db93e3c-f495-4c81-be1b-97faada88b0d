{"name": "@hg-tech/utils", "type": "module", "version": "1.1.6", "description": "通用工具合集", "repository": {"type": "git", "url": "*************************:tech/platform/hypergryph-tech-fe-mono.git"}, "exports": {".": {"types": "./es/index.d.ts", "import": "./es/index.js", "require": "./es/index.js", "default": "./es/index.js"}}, "main": "./es/index.js", "module": "./es/index.js", "types": "./es/index.d.ts", "files": ["./es", "CHANGELOG.md", "readme.md"], "scripts": {"dev": "tsc -w", "build": "tsc", "test": "vitest run", "tdd": "vitest watch"}, "dependencies": {"lodash": "catalog:", "lru-cache": "catalog:", "lz-string": "catalog:", "tree-crawl": "catalog:"}, "devDependencies": {"@napi-rs/canvas": "catalog:", "@types/lodash": "catalog:", "jsdom": "catalog:", "vitest": "catalog:"}}