import { ForgeonTheme } from '@hg-tech/forgeon-style';
import { computed, ref, watchEffect } from 'vue';

export function useSystemTheme() {
  const systemTheme = ref<ForgeonTheme>(ForgeonTheme.Light);

  // 更新主题
  function updateTheme() {
    systemTheme.value = typeof window !== 'undefined' && window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
      ? ForgeonTheme.Dark
      : ForgeonTheme.Light;
  }

  // 使用 watchEffect 自动管理系统主题监听
  watchEffect((onCleanup) => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

      // 初始设置
      updateTheme();

      // 使用新的 addEventListener API（如果支持）或回退到 addListener
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', updateTheme);

        // 清理函数
        onCleanup(() => {
          mediaQuery.removeEventListener('change', updateTheme);
        });
      } else if (mediaQuery.addListener) {
        mediaQuery.addListener(updateTheme);

        // 清理函数
        onCleanup(() => {
          mediaQuery.removeListener(updateTheme);
        });
      }
    }
  });

  return computed(() => systemTheme.value);
}
