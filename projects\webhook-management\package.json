{"name": "@hg-tech/webhook-management", "type": "module", "version": "1.0.7", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "build:rnd": "cross-env NODE_ENV=production vite build --mode rnd", "build:pre": "cross-env NODE_ENV=production vite build --mode pre", "build:analyze": "vite build -- --analyze", "test": "run-p test:*", "test:type": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "test:circular": "madge -c ./src"}, "dependencies": {"@ant-design/icons": "catalog:", "@hg-tech/request-api": "workspace:*", "@hg-tech/utils": "workspace:*", "@sentry/react": "catalog:", "@sentry/types": "catalog:", "@sentry/vite-plugin": "catalog:", "@tanstack/react-router": "catalog:", "ahooks": "catalog:", "antd": "catalog:", "axios": "catalog:", "clsx": "catalog:", "dayjs": "catalog:", "history": "catalog:", "immer": "catalog:", "lodash": "catalog:", "lodash-es": "catalog:", "lru-cache": "catalog:", "react": "catalog:", "react-dom": "catalog:", "react-virtualized-auto-sizer": "catalog:", "react-window": "catalog:", "react-window-infinite-loader": "catalog:", "unocss": "catalog:", "zustand": "catalog:"}, "devDependencies": {"@hg-tech/configs": "workspace:^", "@tanstack/router-devtools": "catalog:", "@types/lodash": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "@types/react-window": "catalog:", "@types/react-window-infinite-loader": "catalog:", "@welldone-software/why-did-you-render": "catalog:", "less": "catalog:", "madge": "catalog:", "typescript": "catalog:", "vite": "catalog:"}}