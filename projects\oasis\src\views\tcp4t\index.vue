<template>
  <div :class="prefixCls">
    <div :class="`${prefixCls}__content`" class="overflow-hidden">
      <div :class="`${prefixCls}__header`" class="flex-none">
        <div class="text-2xl font-bold">
          技术中心P4技能认证考试-TCP4T
        </div>
        <div class="relative mt-2 w-full line-height-32px">
          <div class="text-center">
            考生: {{ formatNickName(userStore.getUserInfo) }}
          </div>
          <div class="absolute right-0 top-4px flex items-center">
            <a-button
              v-if="curPageIndex === 2"
              class="mx-1 !border-#424242 !text-#424242 !dark:border-#fff !dark:text-#fff"
              :loading="isLoading"
              shape="round"
              size="small"
              @click="rollBackLastTime"
            >
              复原本题操作
            </a-button>
            <a-button
              class="mx-1 !border-#424242 !text-#424242 !dark:border-#fff !dark:text-#fff"
              shape="round"
              size="small"
              @click="contactService"
            >
              联系客服
            </a-button>
            <a-button
              class="mx-1 !border-#424242 !text-#424242 !dark:border-#fff !dark:text-#fff"
              shape="round"
              size="small"
              @click="handleOpenCloseModal"
            >
              {{ curPageIndex === 3 ? '返回技术中心首页' : '退出考试' }}
            </a-button>
          </div>
        </div>
      </div>
      <div class="mb-12px h-full flex flex-auto flex-col gap-32px overflow-hidden">
        <div :class="`${prefixCls}__body`" class="relative h-full flex flex-auto flex-col overflow-hidden !relative">
          <BorderBox class="flex-auto overflow-hidden bg-FO-Container-Fill1 !static !border-2 !border-FO-Container-Fill6">
            <template v-if="curPage.title" #title>
              <div :class="`${prefixCls}__body-title`">
                {{ curPage.title }}
                {{ getExamCountMsg }}
                <HeartCount v-if="curPageIndex === 1" class="ml-3" />
              </div>
            </template>
            <curPage.component v-if="examProgressInfo" ref="curPageRef" />
          </BorderBox>
          <BorderBox v-if="curPageIndex === 2 && operationBtnType === 'modify'" class="flex-none bg-FO-Container-Fill1">
            <div class="ml-3 font-size-24px c-FO-Functional-Error1-Default font-bold">
              验证未通过，请修改后再次验证
            </div>
            <div class="px-21 py-8">
              {{ hint }}
            </div>
          </BorderBox>
        </div>
        <div :class="`${prefixCls}__footer`" class="flex-none">
          <a-button
            v-if="curPageIndex === 0 && !examProgressInfo?.isFrozen"
            shape="round"
            class="!border-none !bg-FO-Container-Fill6 !px-18px !py-4px !c-FO-Content-Components1"
            @click="goToExam"
          >
            进入考试
          </a-button>
          <a-button
            v-if="curPageIndex === 2"
            shape="round"
            class="!border-none !bg-#424242 !px-18px !py-4px !c-FO-Content-Components1"
            @click="validateOperationBtn"
          >
            {{ getOperationBtnText }}
          </a-button>
        </div>
      </div>
    </div>
    <CommonModal @register="registerModal" @exit="handleExit" />
  </div>
</template>

<script lang="ts" setup name="TCP4T">
import type {
  Component,
} from 'vue';
import {
  computed,
  onBeforeMount,
  onBeforeUnmount,
  ref,
  watchEffect,
} from 'vue';
import { useRouter } from 'vue-router';
import Certificate from './component/Certificate.vue';
import CommonModal from './component/CommonModal.vue';
import HeartCount from './component/HeartCount.vue';
import Notice from './component/Notice.vue';
import Operations from './component/Operations.vue';
import Selections from './component/Selections.vue';
import { useP4Exam, useP4ExamOperations } from './hook';
import { BorderBox } from '/@/components/Form';
import { useModal } from '/@/components/Modal';
import { formatNickName } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { useGo } from '/@/hooks/web/usePage';
import { useUserStore } from '/@/store/modules/user';
import { openWindow } from '/@/utils';
import { sendEvent } from '/@/service/tracker';

const { prefixCls } = useDesign('tcp4t');
const { currentRoute } = useRouter();
const userStore = useUserStore();

const [registerModal, { openModal }] = useModal();
const go = useGo();
const { handleOperationBtn, operationBtnType, hint, initCurOperation, isLoading }
    = useP4ExamOperations();
const fs = currentRoute.value.query.fs;
const curPageRef = ref<HTMLElement & { curOperation?: any } | null>();
const from = (currentRoute.value.query.from as string) || 'Home';
interface PageItem {
  title: string;
  component: Component;
}

const pageList: PageItem[] = [
  {
    title: '考生须知',
    component: Notice,
  },
  {
    title: '基础知识测试',
    component: Selections,
  },
  {
    title: '实际操作测试',
    component: Operations,
  },
  {
    title: '',
    component: Certificate,
  },
];

const getOperationBtnText = computed(() => {
  switch (operationBtnType.value) {
    case 'modify':
      return '修改完成，进行验证';
    case 'loading':
      return '正在验证，请稍候...';
    default:
      return '完成操作，进行验证';
  }
});

const {
  curPageIndex,
  getExamProgress,
  modalShow,
  getExamCountMsg,
  initData,
  examProgressInfo,
  handleInitData,
  modalType,
} = useP4Exam();

const curPage = computed(() => pageList[curPageIndex.value]);
async function validateOperationBtn() {
  await handleOperationBtn();
  if (curPageIndex.value === 2 && operationBtnType.value === 'modify') {
    const curOperation = curPageRef.value?.curOperation;
    sendEvent('p4_study_p4_exam_operation_wrong', {
      p4_study_operation_id: curOperation.validateMethod,
      p4_study_error_message: hint.value,
    });
  }
}
function contactService() {
  if (curPage.value.component.name === 'TCP4TSelections') {
    sendEvent('p4_study_p4_exam_click_contact_support', {
      p4_study_choice_id: '0',
    });
  } else if (curPage.value.component.name === 'TCP4TOperation') {
    const curOperation = curPageRef.value?.curOperation;
    sendEvent('p4_study_p4_exam_click_contact_support', {
      p4_study_operation_id: curOperation.validateMethod,
    });
  } else if (curPage.value.component.name === 'TCP4TNotice') {
    sendEvent('p4_study_p4_exam_click_contact_support', {
      p4_study_choice_id: '考生须知',
    });
  } else if (curPage.value.component.name === 'TCP4TCertificate') {
    sendEvent('p4_study_p4_exam_click_contact_support', {
      p4_study_choice_id: '考试通过',
    });
  }

  openWindow('https://applink.feishu.cn/T8I5wrhXP5Oq');
}

function goToExam() {
  sendEvent('p4_study_p4_exam_click_enter_exam');
  curPageIndex.value = 1;
}
async function rollBackLastTime() {
  const curOperation = curPageRef.value?.curOperation;
  sendEvent('p4_study_p4_exam_click_restore_question', {
    p4_study_operation_id: curOperation.validateMethod,
  });
  const res = await initCurOperation();
  if (res) {
    modalType.value = 'back';
    modalShow.value = true;
  }
}

function handleOpenCloseModal() {
  if (curPageIndex.value === 3) {
    sendEvent('p4_study_p4_exam_click_back_to_tech_center');

    go({ name: from });
  } else {
    if (curPage.value.component.name === 'TCP4TSelections') {
      sendEvent('p4_study_p4_exam_click_exit_exam', {
        p4_study_choice_id: '0',
      });
    } else if (curPage.value.component.name === 'TCP4TOperation') {
      const curOperation = curPageRef.value?.curOperation;
      sendEvent('p4_study_p4_exam_click_exit_exam', {
        p4_study_operation_id: curOperation.validateMethod,
      });
    } else if (curPage.value.component.name === 'TCP4TNotice') {
      sendEvent('p4_study_p4_exam_click_exit_exam', {
        p4_study_choice_id: '考生须知',
      });
    }
    modalShow.value = true;
  }
}

function handleExit() {
  if (curPage.value.component.name === 'TCP4TSelections') {
    sendEvent('p4_study_p4_exam_click_confirm_exit', {
      p4_study_choice_id: '0',
    });
  } else if (curPage.value.component.name === 'TCP4TOperation') {
    const curOperation = curPageRef.value?.curOperation;
    sendEvent('p4_study_p4_exam_click_confirm_exit', {
      p4_study_operation_id: curOperation.validateMethod,
    });
  } else if (curPage.value.component.name === 'TCP4TNotice') {
    sendEvent('p4_study_p4_exam_click_confirm_exit', {
      p4_study_choice_id: '考生须知',
    });
  }
  go({ name: from });
}

onBeforeMount(async () => {
  if (fs !== '1') {
    go({ query: { fs: '1' } }, true);
  } else {
    initData();
    await getExamProgress();
    handleInitData();
  }
});

onBeforeUnmount(() => {
  if (fs !== '0') {
    go({ query: { fs: '0' } }, true);
  }
});

watchEffect(() => modalShow.value && openModal(true, {}));
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-tcp4t';
.@{prefix-cls} {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: @FO-Container-Fill1;

  &__content {
    width: 100%;
    height: 100%;
    padding: 0 120px;
    display: flex;
    flex-direction: column;
    background: url('/@/assets/icons/tcp4t/bg.svg') bottom 10% right 2% / auto 120% no-repeat border-box border-box
      fixed;
  }

  &__header {
    padding-top: 32px;
    text-align: center;
  }

  &__body {
    border-radius: 6px;

    &-title {
      position: absolute;
      top: 0px;
      left: 20px;
      padding: 2px 8px;
      border-radius: 5px;
      background-color: #424242;
      color: #fff;
      font-size: 24px;
      font-weight: bold;
      user-select: none;
    }
  }

  &__footer {
    text-align: center;
  }
}

html[data-theme='dark'] .@{prefix-cls} {
  &__content {
    background: url('/@/assets/icons/login/bg.svg') bottom 28% right 12% / auto 200% no-repeat border-box border-box
      fixed;
  }
}
</style>
