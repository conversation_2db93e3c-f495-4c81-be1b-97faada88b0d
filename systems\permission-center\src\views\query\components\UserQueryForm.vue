<template>
  <Form :model="form">
    <!-- 主要查询条件 -->
    <div class="flex items-start justify-between">
      <div class="flex flex-wrap items-start gap-x-24px">
        <FormItem
          label="用户"
          name="userId"
          v-bind="validateInfos.userId"
        >
          <div class="w-320px">
            <Select
              v-model:value="form.userId"
              placeholder="请选择用户"
              showSearch
              :filterOption="false"
              :options="userListOptions"
              :loading="loadingUserList"
              :allowClear="true"
              @search="handleUserSearch"
              @focus="resetUserList"
            >
              <template #notFoundContent>
                <div class="p-8px text-center">
                  <span v-if="loadingUserList">搜索中...</span>
                  <span v-else>无匹配项</span>
                </div>
              </template>
            </Select>
          </div>
        </FormItem>

        <FormItem
          label="应用"
          name="appId"
          v-bind="validateInfos.appId"
        >
          <div class="w-320px">
            <Select
              v-model:value="form.appId"
              placeholder="请选择应用"
              :options="appOptions"
              :loading="appLoading"
              showSearch
              optionFilterProp="name"
              :fieldNames="{ label: 'name', value: 'id' }"
              :allowClear="true"
              @change="handleAppChange"
            />
          </div>
        </FormItem>

        <FormItem
          v-if="showTenantSelector"
          label="项目"
          name="tenantIds"
          v-bind="validateInfos.tenantIds"
        >
          <div class="w-320px">
            <Select
              v-model:value="form.tenantIds"
              mode="multiple"
              placeholder="请选择项目"
              :showArrow="true"
              :options="tenantOptions"
              :fieldNames="{ label: 'tenant', value: 'id' }"
              :loading="tenantLoading"
              showSearch
              optionFilterProp="tenant"
              :maxTagCount="2"
              :maxTagTextLength="6"
              :allowClear="true"
              @search="(val) => tenantSearchValue = val"
            >
              <template #dropdownRender="{ menuNode }">
                <div>
                  <div v-if="!tenantSearchValue || tenantSearchValue.trim() === ''" class="flex items-center justify-between border-b border-FO-Container-Stroke1 p-8px">
                    <span class="FO-Font-R12 c-FO-Content-Text3">
                      共 {{ tenantOptions.length }} 个项目
                      <template v-if="form.tenantIds && form.tenantIds.length > 0">
                        ，已选择 {{ form.tenantIds.length }} 个
                      </template>
                    </span>
                    <Button type="link" size="small" @click="handleSelectAllTenants">
                      {{ isAllTenantsSelected ? '取消全选' : '选择全部' }}
                    </Button>
                  </div>
                  <component :is="menuNode" />
                </div>
              </template>
            </Select>
          </div>
        </FormItem>
      </div>

      <div class="flex-shrink-0">
        <Button
          class="btn-fill-primary h-8"
          :loading="querying"
          @click="handleQuery"
        >
          <template #icon>
            <SearchIcon />
          </template>
          查询
        </Button>
      </div>
    </div>
  </Form>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { Button, Form, FormItem, Select } from 'ant-design-vue';
import { useDebounceFn } from '@vueuse/core';
import { useLatestPromise } from '@hg-tech/utils-vue';
import type { RuleObject } from 'ant-design-vue/es/form/interface';
import type { SelectValue } from 'ant-design-vue/es/select';
import SearchIcon from '../../../assets/icons/SystemStrokeSearch.svg?component';
import { getPermissionApps, getPermissionAppTenantList } from '../../../api/app';
import type { GetUserPermissionsRequest } from '../../../api/query';
import { useUserListOption } from '../../../composables/useUserInfo';
import type { PermissionAppInfoV2, PermissionTenantInfo } from '../../../api/app';
import { useUserProfileInfoStore } from '../../../store/modules/userProfileInfo';

interface QueryFormData {
  userId?: string;
  appId?: number;
  tenantIds?: number[];
}

const props = defineProps<{
  initialAppId?: number;
  initialTenantId?: number;
}>();

const emit = defineEmits<{
  query: [params: GetUserPermissionsRequest];
}>();

const form = ref<QueryFormData>({
  userId: undefined,
  appId: undefined,
  tenantIds: undefined,
});

const querying = ref(false);

/** 项目搜索状态 */
const tenantSearchValue = ref('');
const userProfileInfoStore = useUserProfileInfoStore();

/** 应用选择器相关 */
const { data: appsData, execute: fetchApps, loading: appLoading } = useLatestPromise(getPermissionApps);
const appOptions = computed<PermissionAppInfoV2[]>(() => {
  return (userProfileInfoStore.isSuperAdmin ? appsData.value?.data?.data?.allApp : appsData.value?.data?.data?.myApp) || [];
});

/** 是否显示项目选择器 */
const showTenantSelector = computed(() => !!appOptions.value.find((app) => app.id === form.value.appId)?.isMultiTenant);

/** 项目选择器相关 */
const { data: tenantsData, execute: fetchTenants, loading: tenantLoading } = useLatestPromise(getPermissionAppTenantList);

const tenantOptions = computed<PermissionTenantInfo[]>(() => {
  const responseData = tenantsData.value?.data;
  if (responseData?.code === 0 && responseData?.data) {
    return responseData.data;
  }
  return [];
});

/** 计算是否全选了所有项目 */
const isAllTenantsSelected = computed(() => {
  if (!form.value.tenantIds || tenantOptions.value.length === 0) {
    return false;
  }
  return form.value.tenantIds.length === tenantOptions.value.length;
});

const { userListOptions, userListData, loadingUserList, queryUser, resetUserList } = useUserListOption(computed(() => []));

/** 表单验证规则 */
const formRule = computed<Record<keyof QueryFormData, RuleObject[]>>(() => ({
  userId: [
    { required: true, message: '请选择用户' },
  ],
  appId: [
    { required: true, message: '请选择应用' },
  ],
  tenantIds: [
    {
      required: showTenantSelector.value,
      message: '请选择项目',
      validator: () => {
        if (showTenantSelector.value && !form.value.tenantIds?.length) {
          return Promise.reject(new Error('请选择项目'));
        }
        return Promise.resolve();
      },
    },
  ],
}));

const { validate, validateInfos, clearValidate } = Form.useForm(form, formRule);

/** 用户搜索处理 */
const handleUserSearch = useDebounceFn((searchText: string) => {
  if (searchText) {
    queryUser({ query: searchText }, {});
  } else {
    resetUserList();
  }
}, 300);

/**
 * 应用选择变化处理
 * @param value 选择的应用ID
 */
function handleAppChange(value: SelectValue) {
  // 清空项目选择
  form.value.tenantIds = [];

  // 如果是项目应用，加载项目列表
  const appId = value as number;
  const selectedApp = appOptions.value.find((app) => app.id === appId);
  if (selectedApp?.isMultiTenant && appId) {
    fetchTenants({ appId }, {});
    clearValidate('tenantIds');
  }
}

/** 选择全部项目/取消全选 */
function handleSelectAllTenants() {
  if (isAllTenantsSelected.value) {
    // 取消全选
    form.value.tenantIds = [];
  } else {
    // 全选
    form.value.tenantIds = tenantOptions.value.map((tenant) => tenant.id!);
  }
}

/** 查询处理 */
async function handleQuery() {
  try {
    await validate();
    querying.value = true;

    // 获取当前选中用户的完整信息
    const selectedUser = userListData.value.find(
      (user) => user.hgId === form.value.userId,
    );

    const params: GetUserPermissionsRequest = {
      hgId: form.value.userId,
      appId: form.value.appId,
      userInfo: selectedUser,
    };

    if (showTenantSelector.value && form.value.tenantIds) {
      // 将数字ID转换为字符串
      params.tenantIds = form.value.tenantIds.map(String);
    }

    emit('query', params);
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    querying.value = false;
  }
}

/** 监听应用选择变化，重置项目选择 */
watch(() => form.value.appId, () => {
  form.value.tenantIds = [];
});

/** 初始化应用列表和用户列表 */
fetchApps({}, {});

/** 预填应用和租户ID - 需要等待应用列表加载完成 */
watch(() => [props.initialAppId, props.initialTenantId, appOptions.value?.length], async ([appId, tenantId, appOptionsLength]) => {
  if (appId && typeof appId === 'number' && appOptionsLength) {
    form.value.appId = appId;

    // 检查是否是多租户应用
    const selectedApp = appOptions.value.find((app) => app.id === appId);
    if (selectedApp?.isMultiTenant) {
      // 加载租户列表
      await fetchTenants({ appId }, {});
      // 如果有指定租户ID，则预选择该租户
      if (tenantId) {
        const targetTenant = tenantOptions.value.find((tenant) => tenant.id === tenantId);
        if (targetTenant && targetTenant.id) {
          form.value.tenantIds = [targetTenant.id];
          clearValidate();
        }
      }
    }
  }
}, { immediate: true });
</script>
