{"name": "@hg-tech/forgeon-style", "type": "module", "version": "2.1.1", "description": "forgeon主题样式", "exports": {".": {"types": "./es/index.d.ts", "default": "./es/index.js"}, "./styles/*": "./dist/styles/*", "./style": "./dist/styles/index.css", "./vars.less": "./dist/styles/vars.less"}, "main": "./es/index.js", "module": "./es/index.js", "types": "./es/index.d.ts", "files": ["./dist", "./es", "CHANGELOG.md", "readme.md"], "scripts": {"code-gen": "tsx script/code-gen", "build": "pnpm code-gen && gulp", "test": "vitest run", "tdd": "vitest watch", "watch": "vite build --watch & vue-tsc --emitDeclarationOnly --watch"}, "peerDependencies": {"ant-design-vue": "^4.2.5", "lodash-es": "^4", "unocss": "^0.62.4"}, "devDependencies": {"@hg-tech/configs": "workspace:*", "@types/gulp": "catalog:", "@types/gulp-less": "catalog:", "@types/lodash-es": "catalog:", "ant-design-vue": "catalog:", "csstype": "catalog:", "gulp": "catalog:", "gulp-esbuild": "catalog:", "gulp-less": "catalog:", "gulp-typescript": "catalog:", "lodash-es": "catalog:", "rollup-plugin-copy": "catalog:", "ts-node": "catalog:", "unocss": "catalog:", "vitest": "catalog:"}}