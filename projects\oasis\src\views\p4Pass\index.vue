<template>
  <div :class="prefixCls" class="h-full flex flex-col p-[16px]">
    <div class="mb-[16px] flex flex-none items-center rounded-md bg-FO-Container-Fill1 p-[16px]">
      <router-link class="flex px-2 c-FO-Content-Text1" :to="{ name: 'P4Training' }">
        <Icon icon="icon-park-outline:left" :size="18" title="返回" />
        <span class="font-size-[16px] font-bold">返回P4学习首页</span>
      </router-link>
    </div>
    <div :class="`${prefixCls}__all`">
      <div :class="`${prefixCls}__left w-350px mr-[16px] rounded-md`">
        <div
          :class="`${prefixCls}__title font-size-20px font-bold flex rounded-t-md justify-center py-6`"
        >
          P4考试通过成员统计
        </div>
        <div :class="`${prefixCls}__tab`">
          <div
            v-for="item in deptList"
            :key="item.ID"
            :class="`${prefixCls}__tab-item`"
            :active="item.ID === activeDeptID"
            :data-id="item.ID"
            @click="handleGroupChange(item.ID!)"
          >
            <div class="w-250px">
              <ATypographyText
                :class="`${prefixCls}__tab-text ml-2`"
                :ellipsis="{ tooltip: true }"
                :content="item.name"
              />
            </div>
          </div>
        </div>
      </div>
      <div :class="`${prefixCls}__right rounded-md p-[16px] overflow-auto flex-1`">
        <UserSelect v-model:value="searchUserID" class="w-50" placeholder="请输入需要搜索的干员" />
        <a-button :class="`${prefixCls}__btn ml-4`" @click="searchUserByName(searchUserID!)">
          <Icon icon="icon-park-outline:search" size="16" />
          搜索
        </a-button>
        <span v-if="isTip" class="ml-2 color-#D7A800">当前研发组内找不到该干员</span>
        <div class="mt-8 font-size-5 color-#424242 font-bold">
          已通过干员{{ passedUserList?.length }}人
        </div>
        <div class="mt-4 flex flex-wrap">
          <div v-for="item in passedUserList" :key="item.ID" class="mb-2 mr-2">
            <div :class="{ highlight: isHighlighted(item.ID!) }">
              {{ formatNickName(item) }}
            </div>
          </div>
        </div>
        <div v-if="activeDeptID !== 0" class="mt-8 font-size-5 color-#424242 font-bold">
          未通过干员{{ unPassedUserList?.length }}人
        </div>
        <div class="mt-4 flex flex-wrap">
          <div v-for="item in unPassedUserList" :key="item.ID" class="mb-2 mr-2">
            <div :class="{ highlight: isHighlighted(item.ID!) }">
              {{ formatNickName(item) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="P4Pass">
import { TypographyText as ATypographyText } from 'ant-design-vue';
import { onBeforeMount, ref } from 'vue';
import type { P4DeptVisibleItem } from '/@/api/page/model/tcp4tModel';
import { getP4DeptStaffExamState, getP4DeptVisible } from '/@/api/page/tcp4t';
import type { UserInfoModel } from '/@/api/sys/model/userModel';
import { UserSelect } from '/@/components/Form';
import { Icon } from '/@/components/Icon/index';
import { formatNickName } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { sendEvent } from '/@/service/tracker';

const { prefixCls } = useDesign('p4-pass');
const { createMessage } = useMessage();
const deptList = ref<P4DeptVisibleItem[]>();
const activeDeptID = ref<number>();
const passedUserList = ref<UserInfoModel[]>();
const unPassedUserList = ref<UserInfoModel[]>();
const searchUserID = ref<number>();
const searchResult = ref<UserInfoModel[]>();
const isTip = ref<boolean>(false);

// 切换部门
function handleGroupChange(ID: number) {
  if (ID === activeDeptID.value) {
    return;
  }

  activeDeptID.value = ID;
  getMember();
  searchUserID.value = undefined;
  isTip.value = false;
}

// 获取可见部门列表
async function getP4DeptList() {
  const { data } = await getP4DeptVisible();
  const otherDept = {
    ID: 0,
    name: '非研发组及外包干员',
  };

  deptList.value = data?.concat(otherDept);
  activeDeptID.value = deptList.value[0].ID;
  getMember();
}

// 获取通过与未通过成员列表
async function getMember() {
  const { data } = await getP4DeptStaffExamState(activeDeptID.value!);

  passedUserList.value = data.passedUsers;
  unPassedUserList.value = data.unPassedUsers;
}

// 搜索干员
function searchUserByName(e: number) {
  sendEvent('p4_study_exam_stats_trigger_agent_search');
  if (!e) {
    createMessage.warning('请输入需要搜索的干员');
  } else {
    searchResult.value = passedUserList.value
      ?.concat(unPassedUserList.value ? unPassedUserList.value : [])
      .filter((item) => item.ID === e);
    isTip.value = searchResult.value?.length === 0;
  }
}

// 是否高亮
function isHighlighted(id: number) {
  if (!searchUserID.value) {
    return false;
  }

  return searchResult.value?.some((item) => item.ID === id);
}

onBeforeMount(() => {
  getP4DeptList();
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-p4-pass';
.@{prefix-cls} {
  &__all {
    display: flex;
    flex: auto;
    overflow: auto;
  }

  &__left {
    border: 1px solid @table-head-border;
    background-color: @FO-Container-Fill1;
  }

  &__right {
    border: 1px solid @table-head-border;
    background-color: @FO-Container-Fill1;
  }

  &__title {
    background-color: #424242;
    color: #fff;
  }

  &__tab {
    margin: 30px 5px;
    height: 80%;
    overflow: auto;

    &-text {
      color: #424242;
    }

    &-item {
      padding: 8px;
      margin: 8px 20px;
      border: 1px solid transparent;
      border-radius: 6px;
      display: flex;
      justify-content: space-between;
      cursor: pointer;

      &:hover {
        background-color: @FO-Container-Stroke1;
      }

      &[active='true'] {
        font-weight: bold;
        background-color: #424242;

        .@{prefix-cls}__tab-text {
          color: #fff;
        }
      }
    }
  }

  &__btn {
    color: #fff !important;
    background-color: #424242 !important;
    border: 0 !important;
  }

  .highlight {
    background-color: yellow;
  }
}

html[data-theme='dark'] {
  .@{prefix-cls} {
    &__title {
      background-color: #d1d5da;
      color: #424242;
    }

    &__tab {
      &-text {
        color: #d1d5da;
      }

      &-item {
        &:hover {
          background-color: @FO-Container-Stroke1;
        }

        &[active='true'] {
          font-weight: bold;
          background-color: #d1d5da;

          .@{prefix-cls}__tab-text {
            color: #424242;
          }
        }
      }
    }

    &__btn {
      color: #424242 !important;
      background-color: #d1d5da !important;
      border: 0 !important;
    }

    .highlight {
      background-color: rgb(145 145 59);
    }
  }
}
</style>
