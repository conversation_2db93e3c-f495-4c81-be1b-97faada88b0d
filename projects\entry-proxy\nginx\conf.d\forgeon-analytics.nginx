# 该文件为自动生成，请勿手动修改
map $http_upgrade $connection_upgrade {
  default upgrade;
  ''      close;
}

server {
    listen       80;
    server_name  forgeon-sys-analytics.hypergryph.net;

    
    
    # 日志
    access_log /dev/stdout proxy_log;

    set $fe_env_flag $http_x_fe_env_flag;
    set $be_env_flag $http_x_be_env_flag;
    set $oss_host "http://hg-techcenter-efficiency-fe-prod.oss-cn-shanghai-internal.aliyuncs.com";
    
    
    # 根据 x-fe-env-flag 转发到不同的 OSS 地址
    location =/ {
      # 禁止缓存 html
      expires -1;
      add_header Cache-Control no-store;
      
      rewrite ^/ \web\prod\forgeon-analytics\main\index.html break;
      
      # 转发到 OSS
      proxy_pass $oss_host;
      
      # 重设返回 Header Content-Disposition
      proxy_hide_header Content-Disposition;
      add_header Content-Disposition 'inline';
      
      # 启用缓存
      proxy_cache oss_proxy_cache;
      # 设置缓存时间，不缓存 404
      proxy_cache_valid 404 0s;
      proxy_cache_valid any 1m;
      # 设定缓存 key
      proxy_cache_key "$scheme$http_host$request_uri$oss_host$upstream_addr$http_x_fe_env_flag$http_x_be_env_flag";
      # 缓存控制头，防止绕过缓存
      add_header X-Cache-Status $upstream_cache_status;
  
    }

    location @spa-proxy {
      # 禁止缓存 html
      expires -1;
      add_header Cache-Control no-store;
      
      rewrite ^/ \web\prod\forgeon-analytics\main\index.html break;
      
      # 转发到 OSS
      proxy_pass $oss_host;
      
      # 重设返回 Header Content-Disposition
      proxy_hide_header Content-Disposition;
      add_header Content-Disposition 'inline';
      
      # 启用缓存
      proxy_cache oss_proxy_cache;
      # 设置缓存时间，不缓存 404
      proxy_cache_valid 404 0s;
      proxy_cache_valid any 1m;
      # 设定缓存 key
      proxy_cache_key "$scheme$http_host$request_uri$oss_host$upstream_addr$http_x_fe_env_flag$http_x_be_env_flag";
      # 缓存控制头，防止绕过缓存
      add_header X-Cache-Status $upstream_cache_status;
  
    }

    location / {
      proxy_intercept_errors on;
      proxy_redirect off;
      
      error_page 404 = @spa-proxy;
      
      rewrite ^/(?<path>.*) \web\prod\forgeon-analytics\main\$path break;
      
      # 转发到 OSS
      proxy_pass $oss_host;
      
      # 重设返回 Header Content-Disposition
      proxy_hide_header Content-Disposition;
      add_header Content-Disposition 'inline';
      
      # 启用缓存
      proxy_cache oss_proxy_cache;
      # 设置缓存时间，不缓存 404
      proxy_cache_valid 404 0s;
      proxy_cache_valid any 1m;
      # 设定缓存 key
      proxy_cache_key "$scheme$http_host$request_uri$oss_host$upstream_addr$http_x_fe_env_flag$http_x_be_env_flag";
      # 缓存控制头，防止绕过缓存
      add_header X-Cache-Status $upstream_cache_status;
  
    }
    
    
}