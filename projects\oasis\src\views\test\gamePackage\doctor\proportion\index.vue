<template>
  <div :class="isReportPage ? prefixCls : `${prefixCls}__card`">
    <Pies v-if="doctorData" :summaryData="summaryData" :uncompressedData="uncompressedData" />
  </div>
</template>

<script lang="ts" setup name="GamePackageDoctorProportion">
import { useDesign } from '/@/hooks/web/useDesign';
import {
  getGamePackagesDoctorByID,
  getGamePackagesDoctorByIDWithoutLogin,
} from '/@/api/page/test';
import { useRouter } from 'vue-router';
import Pies from '/@/views/test/gamePackage/doctor/proportion/Pies.vue';
import { ref } from 'vue';
import type {
  GamePackageDoctorDetail,
  GamePackageDoctorDetailSummary,
  GamePackageDoctorDetailSummaryUncompressed,
} from '/@/api/page/model/testModel';
import { useMessage } from '/@/hooks/web/useMessage';

const emits = defineEmits(['getDoctorData']);

const { prefixCls } = useDesign('game-package-doctor-proportion');

const { currentRoute } = useRouter();
const { createMessage } = useMessage();

const pkgID = Number(currentRoute.value.params.id);
const doctorID = Number(currentRoute.value.params.doctorID);
const projectID = Number(currentRoute.value.query.p);
const pid = Number(currentRoute.value.query.pid);
const doctorData = ref<GamePackageDoctorDetail>();
const summaryData = ref<GamePackageDoctorDetailSummary>();
const uncompressedData = ref<GamePackageDoctorDetailSummaryUncompressed>();

const isReportPage = currentRoute.value.name === 'GamePackageDoctor';

// 判断路由选择是否免登接口
const apiUrl = isReportPage ? getGamePackagesDoctorByID : getGamePackagesDoctorByIDWithoutLogin;

async function getDoctorList() {
  if (!pkgID) {
    createMessage.warning('游戏包ID不存在');
    return;
  }
  if (!doctorID) {
    createMessage.warning('包体检测ID不存在');
    return;
  }
  if (!projectID && !pid) {
    createMessage.warning('项目ID不存在');
    return;
  }

  const { packageDoctor } = await apiUrl(pid || projectID, pkgID, doctorID);
  const { doctor } = packageDoctor;

  emits('getDoctorData', packageDoctor);
  doctorData.value = doctor;
  summaryData.value = doctor?.summary;
  uncompressedData.value = doctor?.summary_uncompressed;
}

getDoctorList();
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-game-package-doctor-proportion';
.@{prefix-cls} {
  &__card {
    width: 100%;
    height: 100vh;
    background-color: #fff;
  }
}
</style>
