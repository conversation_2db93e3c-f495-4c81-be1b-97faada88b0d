<template>
  <div :class="prefixCls" class="h-full flex flex-col">
    <VxeBasicTable
      ref="tableRef"
      :key="tableKey"
      v-bind="vxeOptions"

      :loading="dataLoading"
      :virtualYConfig="{ enabled: true, gt: 20 }"
      height="100%"
      :rowClassName="({ row }) => (row?.isDeny ? 'c-FO-Functional-Error1-Default' : undefined)"
    >
      <template #toolbar>
        <div class="flex">
          <div class="flex-1 font-bold">
            系统P4权限列表
            <span class="c-FO-Functional-Error1-Default"> 「 请注意: 所有操作完 点击《生效》 才会生效 」 </span>
            <ARadioGroup v-model:value="radioBtnVal" buttonStyle="solid" size="small" @change="handleRadioBtnChange">
              <ARadioButton key="all" value="all">
                全部
              </ARadioButton>
              <ARadioButton key="system" value="system">
                系统
              </ARadioButton>
            </ARadioGroup>
            <a-select
              v-model:value="curServerID" showSearch class="!ml-4 !w-200px"
              :dropdownMatchSelectWidth="false" optionFilterProp="description" :options="perforceServersList"
              placeholder="请选择指定服务器" :fieldNames="{
                label: 'description',
                value: 'ID',
              }" @change="handleServerChange()"
            >
              <template #option="{ address, description }">
                <div>{{ description || address }}</div>
                <div v-if="description" class="ml-5 c-FO-Content-Text2">
                  {{ address }}
                </div>
              </template>
            </a-select>
          </div>
          <ASpace class="!mb-2">
            <PopConfirmButton type="warning" title="确认生效当前权限吗？" @confirm="handleEffect">
              生效
            </PopConfirmButton>

            <a-button v-show="isSystemOnly" type="primary" @click="handleCreate">
              + 新增系统权限
            </a-button>

            <PopConfirmButton type="success" title="确认保存所有操作吗？" @confirm="handleSave">
              保存
            </PopConfirmButton>
            <PopConfirmButton title="确认撤销所有操作吗？" @confirm="handleRevert">
              撤销
            </PopConfirmButton>
          </ASpace>
        </div>
      </template>
      <!--  sort start   -->
      <template #sortDefault>
        <div :class="`${prefixCls}__drag-btn`" title="长按拖动排序">
          <Icon icon="ooui:draggable" />
        </div>
      </template>
      <!--  sort start   -->

      <!--  projectID start   -->
      <template #projectIDDefault="{ row }">
        <ATag v-if="row.projectID === 0" color="#8f8f8f">
          系统
        </ATag>
        <ATag v-else color="red" :style="{ filter: `hue-rotate(${row.projectID * 50}deg)` }">
          {{ projectList?.find((e) => e.ID === row.projectID)?.name }}
        </ATag>
      </template>
      <!--  projectID end   -->

      <!--  accessLevelID start   -->
      <template #accessLevelIDDefault="{ row }">
        <APopover
          :content="getAccessLevelByID(row.accessLevelID)?.descriptionCn" arrowPointAtCenter
          overlayClassName="!max-w-320px"
        >
          {{ getAccessLevelByID(row.accessLevelID)?.displayName }}
        </APopover>
      </template>
      <template #accessLevelIDEdit="{ row }">
        <a-select
          v-model:value="row.accessLevelID" showSearch :options="perforceAccessLevelList"
          :dropdownMatchSelectWidth="false" :fieldNames="{
            label: 'displayName',
            value: 'ID',
          }" @change="handleAccessLevelIDChange(row)"
        >
          <template #option="{ displayName, descriptionCn }">
            <div
              v-tippy="{
                content: descriptionCn,
                placement: 'right',
              }"
            >
              {{ displayName }}
            </div>
          </template>
        </a-select>
      </template>
      <!--  accessLevelID end    -->

      <!--  isGroup start   -->
      <template #isGroupDefault="{ row }">
        {{ row.isGroup ? '组' : '用户' }}
      </template>
      <template #isGroupEdit="{ row }">
        <ARadioGroup v-model:value="row.isGroup" :options="isGroupOptions" @change="row.names = []" />
      </template>
      <!--  isGroup end    -->

      <!--  names start   -->
      <template #nameDefault="{ row }">
        <div :class="`${prefixCls}__name-list`">
          <!-- 只渲染前三个 -->
          <div v-for="name in getComputedNameList(row)?.slice(0, 3)" :key="name" :class="`${prefixCls}__name-list-item`">
            <GroupUsersPopover v-if="row.isGroup" :groupName="name" :serverID="row.serverID || curServerID">
              {{ name }}
            </GroupUsersPopover>
            <template v-else>
              {{ name }}
            </template>
          </div>
          <Tooltip>
            <template #title>
              <div v-for="name in getComputedNameList(row)?.slice(3)" :key="name" class="mb-4px">
                {{ name }}
              </div>
            </template>
            <div v-if="getComputedNameList(row)?.length > 3" :class="`${prefixCls}__name-list-item`" class="cursor-pointer">
              +{{ (getComputedNameList(row)?.length || 3) - 3 }}
            </div>
          </Tooltip>
        </div>
      </template>
      <template #nameEdit="{ row }">
        <ATreeSelect
          v-if="row.isGroup"
          v-model:value="row.names"
          mode="multiple"
          class="w-full z-1! !my-1"
          showSearch
          allowClear
          treeCheckable
          showArrow
          placeholder="请选择干员组"
          showCheckedStrategy="SHOW_PARENT"
          :treeData="groupList"
        />
        <UserSelect
          v-else v-model:value="row.names" isMultiple class="w-full !z-[99] !my-1" :treeData="showUserList" :fieldNames="{
            label: 'displayName',
            value: 'userName',
          }"
        />
      </template>
      <!--  name end    -->

      <!--  path start   -->
      <template #pathDefault="{ row }">
        {{ row.path }}
      </template>
      <template #pathEdit="{ row }">
        <a-input v-model:value="row.path" />
      </template>
      <!--  path end    -->

      <template #action="{ row }">
        <TableAction
          outside :actions="[
            {
              icon: 'ant-design:delete-outlined',
              tooltip: '删除',
              color: 'error',
              popConfirm: {
                title: '确定要删除吗',
                placement: 'left',
                confirm: handleDelete.bind(null, row),
              },
            },
          ]"
        />
      </template>
    </VxeBasicTable>

    <div v-if="isSystemOnly" class="px-6 pb-4">
      <a-button block type="dashed" @click="handleCreate">
        + 新增系统权限
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  Popover as APopover,
  Radio as ARadio,
  Space as ASpace,
  Tag as ATag,
  TreeSelect as ATreeSelect,
  Tooltip,
} from 'ant-design-vue';
import { cloneDeep, isEqual, pick, sortBy } from 'lodash-es';
import { computed, nextTick, onBeforeMount, reactive, ref, watch } from 'vue';
import { useTimeoutFn } from '@vueuse/shared';
import { columns, isGroupOptions } from './permissions.data';
import type { ApiListType } from '/@/api/model/baseModel';
import type {
  PerforceAccessLevelsListItem,
  PerforcePermissionsListItem,
  PerforceScenariosListItem,
} from '/@/api/page/model/perforceModel';
import {
  addPerforcePermission,
  deletePerforcePermission,
  editPerforcePermission,
  effectPerforcePermission,
} from '/@/api/page/perforce';
import { PopConfirmButton } from '/@/components/Button';
import { UserSelect } from '/@/components/Form';
import GroupUsersPopover from '/@/components/GroupUsersPopover';
import Icon from '/@/components/Icon';
import { TableAction } from '/@/components/Table';
import type { BasicTableProps, VxeGridInstance } from '/@/components/VxeTable';
import { VxeBasicTable } from '/@/components/VxeTable';
import { useUserList } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useSortable } from '/@/hooks/web/useSortable';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { isNullOrUnDef } from '/@/utils/is';
import { propTypes } from '/@/utils/propTypes';
import { buildUUID } from '/@/utils/uuid';
import {
  usePerforceAccessLevel,
  usePerforceServer,
} from '/@/views/versionControl/perforceManagement/hook';
import { useCachedProjectList } from '../../../hooks/useProjects.ts';

defineOptions({
  name: 'PerforcePermissionEditTable',
});

const props = defineProps({
  isSystemOnly: propTypes.bool.def(false),
  scenarioID: propTypes.number.def(),
  scenarioList: propTypes.array.def([] as PerforceScenariosListItem[]),
  perforcePermissionList: propTypes.array.def([] as PerforcePermissionsListItem[]),
  dataLoading: propTypes.bool.def(false),
});

const emits = defineEmits(['dataSave', 'filterChange', 'needAddScenario', 'serverChange']);

const ARadioGroup = ARadio.Group;
const ARadioButton = ARadio.Button;

const { prefixCls } = useDesign('perforce-permission-edit-table');
const userStore = useUserStoreWithOut();
const { createMessage, createWarningModal } = useMessage();
const tableRef = ref<VxeGridInstance>();
const tableKey = ref<string>(buildUUID());
const permissionList = ref<PerforcePermissionsListItem[]>([]);
const curServerID = ref<number>();
const ignoreCurReload = ref(false);
const { userList, getUserList } = useUserList();
const { data: projectList } = useCachedProjectList({ loadImmediately: true });

const { curServer, perforceServersList, getPerforceGroupList, groupList } = usePerforceServer();
const { perforceAccessLevelList } = usePerforceAccessLevel();

onBeforeMount(() => {
  curServerID.value = curServer.value?.ID;
  getPerforceGroupList();
  getUserList();
});

const showUserList = computed(() => {
  const newList = cloneDeep(userList.value);
  newList?.push({
    displayName: '*',
    userName: '*',
  });
  return newList || [];
});

// 排序最大值
const sortCount = ref<number>(0);

// 筛选值
const radioBtnVal = ref<'all' | 'system'>(props.isSystemOnly ? 'system' : 'all');

// 请求列表
const apiList = ref<ApiListType<PerforcePermissionsListItem | number>[]>([]);
// 需要删除的id列表
const needDeleteList = ref<PerforcePermissionsListItem[]>([]);

const projectID = '0';
const depotID = 0;

const dynamicColumn = columns.map((e) => {
  switch (e.field) {
    case 'sort':
      e.visible = props.isSystemOnly;
      break;
    case 'projectID':
      e.visible = !props.isSystemOnly;
      break;
    case 'action':
      e.visible = props.isSystemOnly;
      break;
    default:
      break;
  }
  return e;
});

// 通过ID获取对应访问等级
function getAccessLevelByID(ID: number) {
  return perforceAccessLevelList.value.find(
    (acc: PerforceAccessLevelsListItem) => acc.ID === ID,
  ) as PerforceAccessLevelsListItem;
}

const vxeOptions = reactive<BasicTableProps>({
  id: 'P4HierarchiesTable',
  toolbarConfig: {
    enabled: false,
  },
  showOverflow: true,
  keepSource: true,
  editConfig: { trigger: 'click', mode: 'cell', showStatus: true },
  columns: dynamicColumn,
  maxHeight: window.innerHeight - (props.isSystemOnly ? 100 : 60),
  minHeight: 300,
  data: permissionList.value,
  proxyConfig: {
    enabled: false,
    autoLoad: false,
  },
});

function handleAccessLevelIDChange(row) {
  const find = getAccessLevelByID(row.accessLevelID);
  // 是特殊访问等级，则设置拒绝属性为true
  row.isDeny = !!find?.realID;
}

const getComputedNameList = computed(() => {
  return (row: PerforcePermissionsListItem) => {
    const result = row.isGroup
      ? row.names
      : row.names?.map(
        (name) => userList.value.find((e) => e.userName === name)?.displayName || name,
      );
    return result;
  };
});

function initDrag() {
  // 拖动排序功能
  nextTick(() => {
    const el = document.querySelector(`.${prefixCls} .vxe-table--body tbody`) as HTMLElement;
    const { initSortable } = useSortable(el, {
      handle: `.${prefixCls}__drag-btn`,
      onEnd: ({ oldIndex, newIndex }) => {
        if (isNullOrUnDef(oldIndex) || isNullOrUnDef(newIndex) || oldIndex === newIndex) {
          return;
        }

        const temp = cloneDeep(permissionList.value);
        const currRow = temp.splice(oldIndex, 1)[0];
        temp.splice(newIndex, 0, currRow);
        temp.forEach((e, i) => {
          e.sort = i + 1;
        });
        sortCount.value = temp.length;
        permissionList.value = temp;
        const scroll = tableRef.value?.getScroll();
        tableKey.value = buildUUID();
        nextTick(() => {
          tableRef.value?.reloadData(permissionList.value);
          useTimeoutFn(() => {
            tableRef.value?.scrollTo(scroll?.scrollLeft || 0, scroll?.scrollTop || 0);
          }, 100);
          initDrag();
        });
      },
    });
    initSortable();
  });
}
initDrag();

// 新增操作
function handleCreate() {
  sortCount.value++;
  permissionList.value.push({
    accessLevelID: 3,
    isGroup: true,
    names: [],
    sort: sortCount.value,
    isDeny: false,
    UUID: buildUUID(),
    path: '...',
    comment: '',
  });
  tableRef.value?.reloadData(permissionList.value);
  useTimeoutFn(() => {
    tableRef.value?.scrollTo(0, 999999);
  }, 100);
}

// 格式化数据以适配接口参数
function formatSubmitData(e: PerforcePermissionsListItem) {
  e.serverID = e.serverID || curServerID.value;
  e.scenarioID = e.scenarioID || props.scenarioID;
  if (e.isGroup) {
    e.groupList = e.names;
  } else {
    e.userList = e.names;
  }
  delete e.names;
  if (e.UUID) {
    delete e.UUID;
  }
  if (e.isDeny) {
    // 是拒绝的话则是特殊访问等级，转换成后端需要的ID
    const find = getAccessLevelByID(e.accessLevelID!);
    e.accessLevelID = find.realID;
  }
}

// 筛选接口需要参数
function pickData(e: PerforcePermissionsListItem) {
  return pick(e, [
    'ID',
    'accessLevelID',
    'comment',
    'isDeny',
    'path',
    'sort',
    'serverID',
    'isGroup',
    'scenarioID',
    'projectID',
    'depotID',
    e.isGroup ? 'groupList' : 'userList',
  ]);
}

// 保存操作
async function handleSave() {
  if (!props.scenarioID) {
    emits('needAddScenario');
    return;
  }
  if (permissionList.value?.length > 0) {
    const msgList: string[] = [];
    permissionList.value.forEach((e, i) => {
      if (!e.names?.length) {
        msgList.push(`『第${i + 1}行』的 ${e.isGroup ? '组' : '用户'}名称 不能为空!`);
        return;
      }
      if (e.path && e.path.endsWith('/')) {
        msgList.push(`『第${i + 1}行』的 路径 不能以 / 结尾!`);
      }
    });
    if (msgList.length) {
      createWarningModal({
        title: '请注意',
        content: msgList.join('<br/>'),
      });
      return;
    }
    const submitData = cloneDeep(permissionList.value);
    submitData.map(async (e) => {
      if (!e.ID) {
        // 新增
        formatSubmitData(e);
        apiList.value.push({
          type: 'add',
          data: e,
        });
      } else {
        // 编辑
        const originData = pickData(
          props.perforcePermissionList.find(
            (f: PerforcePermissionsListItem) => f.ID === e.ID,
          ) as PerforcePermissionsListItem,
        );
        formatSubmitData(e);
        e = pickData(e);
        // 有修改才调用更新接口
        if (!isEqual(originData, e)) {
          apiList.value.push({
            type: 'edit',
            data: e,
          });
        }
      }
    });
  }
  if (needDeleteList.value?.length > 0) {
    // 删除
    needDeleteList.value.forEach((e) => {
      apiList.value.push({
        type: 'delete',
        data: e,
      });
    });
  }
  if (apiList.value.length > 0) {
    const promiseList: Promise<any>[] = [];
    apiList.value.forEach((e) => {
      const submitData = e.data as PerforcePermissionsListItem;
      switch (e.type) {
        case 'add':
          promiseList.push(addPerforcePermission(
            submitData?.projectID?.toString() || projectID,
            submitData?.depotID || depotID,
            submitData,
          ));
          break;
        case 'edit':
          promiseList.push(editPerforcePermission(
            submitData?.projectID?.toString() || projectID,
            submitData?.depotID || depotID,
            submitData,
            submitData!.ID as number,
          ));
          break;
        case 'delete':
          promiseList.push(deletePerforcePermission(
            submitData?.projectID?.toString() || projectID,
            submitData?.depotID || depotID,
            submitData!.ID as number,
          ));
          break;
      }
    });
    const res = await Promise.all(promiseList);
    const hasError = res.some((e) => e?.code === 7);
    // 全部请求结束再调用刷新列表
    if (!hasError) {
      emits('dataSave', props.isSystemOnly);
      createMessage.success('保存成功！');
    } else {
      const errorList = apiList.value.filter((_, i) => res[i]?.code === 7);
      ignoreCurReload.value = true;
      const oldPermissionList = cloneDeep(permissionList.value);
      const scroll = tableRef.value?.getScroll();
      emits('dataSave', props.isSystemOnly, false, () => {
        tableKey.value = buildUUID();
        initData(false);
        nextTick(() => {
          errorList.forEach((e) => {
            switch (e.type) {
              case 'add':
                permissionList.value.push({ ...e.data as PerforcePermissionsListItem, UUID: buildUUID() });
                break;
              case 'edit':
                {
                  const find = oldPermissionList.find((f) => f.ID === (e.data as PerforcePermissionsListItem).ID);
                  if (find) {
                    const index = permissionList.value.findIndex((f) => f.ID === find.ID);
                    if (index > -1) {
                      permissionList.value[index] = find;
                    }
                  }
                }
                break;
              default:
                break;
            }
          });
          permissionList.value = sortBy(permissionList.value, 'sort');
          tableRef.value?.reloadData(permissionList.value);
          useTimeoutFn(() => {
            initDrag();
            tableRef.value?.scrollTo(scroll?.scrollLeft || 0, scroll?.scrollTop || 0);
            ignoreCurReload.value = false;
          }, 100);
        });
      });
    }
  } else {
    createMessage.info('数据无变动无需保存');
  }
}

// 初始化数据
function initData(reload = true) {
  const newData = cloneDeep(props.perforcePermissionList) as PerforcePermissionsListItem[];
  let maxSort = 1;
  newData.forEach((e) => {
    if (e.isGroup) {
      e.names = e.groupList;
    } else {
      e.names = e.userList;
    }
    maxSort = e.sort! > maxSort ? e.sort! : maxSort;
    // 是拒绝的话则是特殊访问等级，切换成前端生成的ID便于显示
    if (e.isDeny) {
      const find = perforceAccessLevelList.value.find(
        (acc: PerforceAccessLevelsListItem) => acc.realID === e.accessLevelID,
      ) as PerforceAccessLevelsListItem;
      if (find) {
        e.accessLevelID = find.ID;
      }
    }
  });
  sortCount.value = maxSort;
  permissionList.value = newData;
  needDeleteList.value = [];
  apiList.value = [];
  reload && tableRef.value?.reloadData(permissionList.value);
}

// 撤销操作
function handleRevert() {
  initData();
}

// 删除操作
function handleDelete(record: PerforcePermissionsListItem) {
  const { UUID, ID } = record;
  if (ID) {
    needDeleteList.value.push(record);
  }
  permissionList.value = permissionList.value.filter(
    (e) => (UUID && e.UUID !== UUID) || (ID && e.ID !== ID),
  );
  tableRef.value?.reloadData(permissionList.value);
}

function handleRadioBtnChange() {
  emits('filterChange', radioBtnVal.value === 'system');
}

async function handleEffect() {
  await effectPerforcePermission(curServerID.value!);
  emits('dataSave', props.isSystemOnly);
}

watch(
  () => props.perforcePermissionList,
  (val: PerforcePermissionsListItem[]) => {
    if (val && !ignoreCurReload.value) {
      // 数据变动, 需初始化
      initData();
    }
  },
);

async function handleServerChange() {
  curServer.value = perforceServersList.value?.find((e) => e.ID === curServerID.value);
  await getPerforceGroupList(projectID);
  emits('serverChange');
}

defineExpose({
  handleSave,
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-perforce-permission-edit-table';
.@{prefix-cls} {
  background-color: @FO-Container-Fill1;

  &__drag-btn {
    cursor: pointer;
  }

  & .ant-tag {
    border-radius: 12px !important;
    border-color: transparent !important;
  }

  &__name-list {
    display: flex;

    row-gap: 4px;
    margin: 4px 0;

    &-item {
      margin-right: 4px;
      padding: 0 4px;
      border: 1px solid @FO-Container-Stroke1;
      border-radius: 4px;
    }
  }
}
</style>
