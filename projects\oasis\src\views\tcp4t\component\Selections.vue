<template>
  <div :class="prefixCls">
    <div :class="`${prefixCls}__list`">
      <div v-for="(item, i) in selectionList" :key="item.ID" :class="`${prefixCls}__list-item`">
        <div class="mb-2 flex items-center c-FO-Content-Text1 !font-bold">
          <div class="mr-3 self-start">
            {{ i + 1 }}
          </div>
          <span v-if="isNoFile(item.questStem)" class="break-all">
            {{ item.questStem }}
          </span>
          <MarkdownViewer v-else :value="item.questStem" />
        </div>
        <div
          v-for="opt in item.options?.data"
          :key="opt.identity"
          :class="`${prefixCls}__option`"
          :isFinished="item.isFinished"
          :identity="opt.identity"
          :itemID="item.ID"
          @click="handleSelect(opt, item)"
        >
          <div :class="`${prefixCls}__option-radio`" :status="opt.status">
            <Icon
              v-if="opt.status"
              :size="14"
              v-bind="getOptIconProps(opt.status, item.isFinished)"
            />
          </div>
          <span v-if="isNoFile(opt.text)" class="flex-1 break-all c-FO-Content-Text1">{{ opt.text }}</span>
          <MarkdownViewer v-else :value="opt.text" class="flex-1" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="TCP4TSelections">
import { useTimeoutFn } from '@vueuse/shared';
import gsap from 'gsap';
import { onBeforeMount } from 'vue';
import { useP4ExamSelection } from '../hook';
import type { P4ExamOptionsItem, P4ExamSelectionItem } from '/@/api/page/model/tcp4tModel';
import { Icon } from '/@/components/Icon';
import { MarkdownViewer } from '/@/components/Markdown';
import { useDesign } from '/@/hooks/web/useDesign';
import { sendEvent } from '/@/service/tracker';

const { prefixCls } = useDesign('tcp4t-selections');

const { getSelectionList, selectionList, getSelectionProof } = useP4ExamSelection();

function getOptIconProps(status?: string, isFinished = false) {
  switch (status) {
    case 'success':
      return { icon: 'mingcute:check-fill', class: 'c-FO-Functional-Success1-Default' };
    case 'error':
      return {
        icon: 'mingcute:close-fill',
        class: `c-FO-Functional-Error1-Default ${isFinished ? 'opacity-0' : ''}`,
      };
    default:
      return undefined;
  }
}

async function handleSelect(opt: P4ExamOptionsItem, selection: P4ExamSelectionItem) {
  if (opt.status || selection.isFinished) {
    return;
  }
  const correct = await getSelectionProof(selection.ID!, opt.identity);
  opt.status = correct ? 'success' : 'error';
  selection.isFinished = correct;
  if (!correct) {
    sendEvent('p4_study_p4_exam_choice_wrong', {
      p4_study_choice_id: selection.questStem,
      p4_study_wrong_option: opt.text,
    });
    const element = document.querySelector(
      `.${prefixCls}__option[identity="${opt.identity}"][itemID="${selection.ID}"]`,
    ) as HTMLElement;
    gsap.to(element, { x: '+=10', yoyo: true, repeat: -1, duration: 0.1 });
    gsap.to(element, { x: '-=10', yoyo: true, repeat: -1, duration: 0.1 });
    useTimeoutFn(() => {
      gsap.killTweensOf(element);
      element.style.transform = '';
    }, 600);
  }
}

function isNoFile(text?: string) {
  return text?.indexOf('uploads/') === -1;
}
onBeforeMount(() => {
  getSelectionList();
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-tcp4t-selections';
.@{prefix-cls} {
  padding: 30px;
  height: calc(100vh - 200px);
  min-height: 300px;
  overflow: auto;

  &__list {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  &__option {
    display: flex;
    align-items: start;
    cursor: pointer;
    width: fit-content;

    &:not(:last-child) {
      margin-bottom: 8px;
    }

    &-radio {
      width: 22px;
      height: 22px;
      margin: 2px 10px 2px 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 100%;
      border: 2px solid @FO-Content-Text1;
    }

    &[isFinished='true'] {
      cursor: default;
      & .@{prefix-cls}__option-radio:not([status='success']) {
        border: none;
      }
    }
  }

  & .vditor-reset {
    & p {
      margin-bottom: 8px;
    }

    img {
      min-width: 30px;
    }
  }
}
</style>
