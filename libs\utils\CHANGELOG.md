# @hg-tech/utils

## 1.1.6

### Patch Changes

- 27b5691: 导出 SafeDecodeOptions/SafeEncodeOptions，优化 findInTrees

## 1.1.5

### Patch Changes

- 9836dfb: toHighlightSegments 转换函数支持匹配多个值

## 1.1.4

### Patch Changes

- 74a17db: 新增高亮分块方法

## 1.1.3

### Patch Changes

- bbc5f5e: 修复并发情况下可能出现的时序异常问题

## 1.1.2

### Patch Changes

- 9225188: 修复 safeToArray 类型提示，修复 types 类型导出

## 1.1.1

### Patch Changes

- 1eb376b: 修复对非 type=module 下支持

## 1.1.0

### Minor Changes

- 69b69a3: 新增部分数组及树方法
- 0e1434d: 新增 createFetchPoolLRU
