import { type Ref, onMounted } from 'vue';
import { computed } from 'vue';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { defineStore } from 'pinia';
import { type SysUserInfo, getCurrentUserInfo, queryUserList } from '../api/users.ts';

/**
 * 获取当前用户信息
 */
export const useLoginUserInfoStore = defineStore('login-user-info', () => {
  const { data, loading: loadingLoginUserInfo, execute } = useLatestPromise(getCurrentUserInfo);

  onMounted(() => {
    execute({}, {});
  });

  return {
    loginUserInfo: computed(() => data.value?.data?.data),
    loadingLoginUserInfo,
  };
});

export function useUserListOption(fallback: Ref<SysUserInfo[]>) {
  const { data: userListRes, loading: loadingUserList, execute: queryUser, reset: resetUserList } = useLatestPromise(queryUserList);

  const userListData = computed(() => {
    if (userListRes.value?.data?.data?.length) {
      return userListRes.value.data.data;
    }
    return fallback.value.filter(Boolean);
  });

  const userListOptions = computed(() => {
    return userListData.value.map((i) => ({
      label: formatUserDisplay(i),
      value: i.hgId,
    }));
  });

  return {
    userListOptions,
    userListData,
    loadingUserList,
    queryUser,
    resetUserList,
  };
}

export function formatUserDisplay(user?: SysUserInfo) {
  return `${user?.name}${user?.nickname ? `(${user.nickname})` : ''}`;
}
