<template>
  <div class="flex">
    <div
      v-for="item in tabList"
      :key="item.key"
      class="FO-Font-B14 cursor-pointer select-none rd-6px px-12px py-5px transition-300"
      :class="[
        item.key === value ? 'c-FO-Brand-Primary-Default' : 'c-FO-Content-Text3',
        item.key === value ? 'bg-FO-Brand-Tertiary-Active' : 'bg-transparent',
      ]"
      @click="() => valueModel = item.key"
    >
      {{ item.label }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue';

const props = defineProps<{
  tabList?: Array<{
    key: string;
    label: string;
  }>;
}>();

const valueModel = defineModel<string>('value');

watch([valueModel, () => props.tabList], ([v, list]) => {
  if (list?.length && !list.some((i) => i.key === v)) {
    valueModel.value = list[0].key;
  }
});
</script>
