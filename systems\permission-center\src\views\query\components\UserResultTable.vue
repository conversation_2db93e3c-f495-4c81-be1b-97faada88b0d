<template>
  <div class="h-full flex flex-col gap-12px">
    <div v-if="data?.length && stats" class="mb-12px">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-8px">
          <span class="FO-Font-R14 c-FO-Content-Text2">
            已配置 <span class="FO-Font-B18 c-FO-Content-Text1">{{ stats.roleCount }}</span> 个角色，拥有
            <span class="FO-Font-B18 c-FO-Content-Text1">{{ stats.enabledCount }}</span> 项权限
          </span>
          <button
            class="FO-Font-B14 cursor-pointer border-none bg-transparent p-0 c-FO-Brand-Primary-Default hover:opacity-80"
            @click="handleViewRoles"
          >
            查看角色
          </button>
        </div>

        <div class="flex items-center gap-8px">
          <span class="FO-Font-B14 c-FO-Content-Text1">仅查看已开启权限</span>
          <Switch v-model:checked="onlyShowEnabled" @change="handleFilterChange" />
        </div>
      </div>
    </div>

    <div class="flex-auto">
      <div v-if="!data?.length && !loading" class="h-full flex flex-col items-center justify-center c-FO-Content-Text3">
        <Empty description="请选择查询条件" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
      </div>

      <div v-else-if="loading" class="h-full flex flex-col items-center justify-center c-FO-Content-Text3">
        <Spin size="large" />
        查询中...
      </div>

      <div v-else-if="filteredFlatData.length === 0" class="h-full flex flex-col items-center justify-center c-FO-Content-Text3">
        <Empty :description="onlyShowEnabled && props.data.length > 0 ? '应用没有配置权限点' : '无匹配项'" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
      </div>

      <div v-else class="h-full">
        <BasicVxeTable
          ref="tableRef"
          :options="gridOptions"
        />
      </div>
    </div>

    <ConfiguredRolesDrawerHolder />
    <PermissionConfigDrawerHolder />
  </div>
</template>

<script setup lang="tsx">
import { computed, nextTick, ref, useTemplateRef } from 'vue';
import { Empty, Spin, Switch, Tooltip } from 'ant-design-vue';
import type { VxeGridInstance, VxeGridProps } from 'vxe-table';
import { BasicVxeTable, EllipsisText, PlatformEnterPoint } from '@hg-tech/oasis-common';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import type { GetUserPermissionsRequest, PermissionRoleItem } from '../../../api/query';
import type { ConfiguredRoleInfo, PermissionCategory, PermissionQueryStats, QueryPermissionRow } from '../permission';
import { searchPermissionGroupMembers } from '../../../api/group';
import { batchGetGroupByPermissionV2 } from '../../../api/query';
import ConfiguredRolesDrawer from './ConfiguredRolesDrawer.vue';
import PermissionConfigDrawer from './PermissionConfigDrawer.vue';
import ArrowDownIcon from '../../../assets/icons/SystemStrokeArrowDown.svg?component';
import { useRouter } from 'vue-router';

const props = defineProps<{
  data: QueryPermissionRow[];
  loading: boolean;
  stats: PermissionQueryStats | null;
  queryParams?: GetUserPermissionsRequest;
  categories?: PermissionCategory[];
}>();

const emit = defineEmits(['viewRoles', 'configPermission', 'filterChange']);

const router = useRouter();

const [ConfiguredRolesDrawerHolder, showConfiguredRolesDrawer] = useModalShow(ConfiguredRolesDrawer);
const [PermissionConfigDrawerHolder, showPermissionConfigDrawer] = useModalShow(PermissionConfigDrawer);

const { execute: fetchGroupMembers } = useLatestPromise(searchPermissionGroupMembers);
const { data: permissionRolesData, execute: fetchPermissionRoles, loading: fetchingPermissionRoles } = useLatestPromise(batchGetGroupByPermissionV2);

const tableRef = useTemplateRef<VxeGridInstance<QueryPermissionRow>>('tableRef');

const onlyShowEnabled = ref<boolean>(false);

/**
 * 收集需要保留的分类节点 uniqueKey
 */
function collectRequiredCategories(enabledPermissions: QueryPermissionRow[]): Set<string> {
  const requiredCategoryKeys = new Set<string>();

  // 直接收集启用权限的父分类 uniqueKey
  enabledPermissions.forEach((permission) => {
    if (permission.type === 'permission' && permission.parentUniqueKey) {
      requiredCategoryKeys.add(permission.parentUniqueKey);
    }
  });

  return requiredCategoryKeys;
}

const filteredFlatData = computed(() => {
  if (!props.data?.length) {
    return [];
  }

  if (!onlyShowEnabled.value) {
    return props.data;
  }

  // 过滤出所有启用的权限节点
  const enabledPermissions = props.data.filter((row) =>
    row.type === 'permission' && row.isEnabled,
  );

  // 如果没有启用的权限，返回空数组
  if (enabledPermissions.length === 0) {
    return [];
  }

  // 收集所有需要保留的分类节点 uniqueKey
  const requiredCategoryKeys = collectRequiredCategories(enabledPermissions);

  // 返回启用的权限节点和必需的分类节点
  return props.data.filter((row) => {
    if (row.type === 'category') {
      return requiredCategoryKeys.has(row.uniqueKey);
    }
    return row.type === 'permission' && row.isEnabled;
  });
});

const gridOptions = computed((): VxeGridProps => ({
  data: filteredFlatData.value,
  height: 'auto',
  rowConfig: {
    keyField: 'uniqueKey',
    isHover: true,
  },
  treeConfig: {
    transform: true,
    rowField: 'uniqueKey',
    parentField: 'parentUniqueKey',
    expandAll: true,
  },
  columnConfig: {
    resizable: false,
  },
  columns: [
    { field: 'blank', title: '', width: 30 },
    {
      field: 'name',
      title: '权限名称',
      width: 365,
      treeNode: true,
      slots: {
        default: ({ row }) => {
          if (row.type === 'category') {
            return (
              <div class="flex items-center gap-3">
                <EllipsisText class="FO-Font-B14 c-FO-Content-Text1">{row.name}</EllipsisText>
                <span class="rounded bg-FO-Container-Fill2 px-2 py-1 text-12px c-FO-Content-Text1">
                  {row.childCount}
                </span>
              </div>
            );
          }
          return (
            <span class="FO-Font-R14 c-FO-Content-Text1">{row.name}</span>
          );
        },
      },
    },
    {
      field: 'roles',
      title: '权限来源角色',
      minWidth: 400,
      slots: {
        default: ({ row }) => {
          if (row.type === 'category') {
            return '';
          }

          // 如果没有角色，显示配置权限链接
          if (!row.roles || row.roles.length === 0) {
            return (
              <button
                class="FO-Font-B14 cursor-pointer border-none bg-transparent p-0 c-FO-Brand-Primary-Default hover:opacity-80"
                onClick={() => handleConfigPermission(row)}
              >
                配置权限
              </button>
            );
          }

          return (
            <div class="flex flex-wrap gap-1">
              {row.roles.map((role: PermissionRoleItem, index: number) => (
                <Tooltip title="前往配置角色">
                  <div
                    class="flex cursor-pointer items-center gap-2px border border-FO-Container-Stroke1 rounded-md bg-FO-Container-Fill4 px-2 py-1 hover:bg-FO-Container-Fill3"
                    key={index}
                    onClick={() => handleGotoRoleConfig(role)}
                  >
                    <span class="FO-Font-B14 c-FO-Content-Text2">
                      {role.tenant ? `${role.tenant}: ${role.name}` : role.name}
                    </span>
                    <ArrowDownIcon class="c-FO-Content-Icon2 -rotate-90" />
                  </div>
                </Tooltip>
              ))}
            </div>
          );
        },
      },
    },
  ],
}));

async function handleFilterChange() {
  emit('filterChange', onlyShowEnabled.value);
  await nextTick();
  tableRef.value?.setAllTreeExpand(true);
}

async function handleViewRoles() {
  if (!props.data?.length || !props.queryParams?.appId) {
    return;
  }

  // 从权限数据中提取所有角色信息
  const roleMap = new Map<string, ConfiguredRoleInfo>();

  // 只处理权限节点，跳过分类节点
  props.data.forEach((row) => {
    if (row.type === 'permission') {
      row.roles?.forEach((role) => {
        const uniqueKey = `${role.id}-${role.tenantId || 'default'}`;
        if (!roleMap.has(uniqueKey)) {
          roleMap.set(uniqueKey, {
            roleId: role.id,
            roleName: role.name,
            projectName: role.tenant,
            tenantId: role.tenantId,
            memberCount: 0,
            members: [],
          });
        }
      });
    }
  });

  const roles = Array.from(roleMap.values());

  // 获取用户hgAccount用于筛选
  const userHgAccount = props.queryParams?.userInfo?.hgAccount;

  // 并行获取所有角色的成员信息
  const memberPromises = roles.map(async (role) => {
    try {
      const response = await fetchGroupMembers({
        appId: props.queryParams!.appId!,
        groupId: role.roleId,
        search: userHgAccount,
        tenantId: role.tenantId ? role.tenantId : undefined,
      }, {});

      if (response?.data?.code === 0 && response.data.data) {
        const members = response.data.data.map((member) => member.name).filter(Boolean);
        return {
          ...role,
          members,
          memberCount: members.length,
        };
      } else {
        console.warn(`获取角色 ${role.roleName} 成员信息失败`);
        return {
          ...role,
          members: [],
          memberCount: 0,
        };
      }
    } catch (error) {
      console.error(`获取角色 ${role.roleName} 成员信息失败:`, error);
      return {
        ...role,
        members: [],
        memberCount: 0,
      };
    }
  });

  // 等待所有请求完成
  const rolesWithMembers = await Promise.allSettled(memberPromises);
  const enrichedRoles = rolesWithMembers.map((result) =>
    (result.status === 'fulfilled' ? result.value : result.reason),
  ).filter(Boolean);

  // 判断是否为多租户模式
  const isMultiTenant = Boolean(props.queryParams?.appId && props.queryParams?.tenantIds && props.queryParams.tenantIds.length > 0);

  const flatTableData: any[] = [];

  if (isMultiTenant) {
    // 多租户模式：按租户分组
    const tenantMap = new Map();
    enrichedRoles.forEach((role) => {
      const tenantId = role.tenantId;
      if (!tenantMap.has(tenantId)) {
        tenantMap.set(tenantId, {
          tenantId,
          tenantName: role.projectName,
          roles: [],
        });
      }
      tenantMap.get(tenantId).roles.push(role);
    });

    // 生成 flat 数据结构
    tenantMap.forEach((tenant) => {
      // 租户节点
      const tenantRow = {
        uniqueKey: `tenant-${tenant.tenantId}`,
        roleId: 0,
        roleName: tenant.tenantName,
        projectName: tenant.tenantName,
        members: [],
        memberCount: 0,
        originalData: {},
        type: 'project',
        roleCount: tenant.roles.length,
        parentUniqueKey: undefined,
      };
      flatTableData.push(tenantRow);

      // 角色节点
      tenant.roles.forEach((role: any) => {
        const roleRow = {
          uniqueKey: `role-${role.roleId}`,
          roleId: role.roleId,
          roleName: role.roleName,
          projectName: role.projectName,
          members: role.members || [],
          memberCount: role.memberCount || 0,
          originalData: role,
          type: 'role',
          parentUniqueKey: tenantRow.uniqueKey,
        };
        flatTableData.push(roleRow);
      });
    });
  } else {
    // 非多租户模式：直接列出角色
    enrichedRoles.forEach((role) => {
      const roleRow = {
        uniqueKey: `role-${role.roleId}`,
        roleId: role.roleId,
        roleName: role.roleName,
        projectName: role.projectName,
        members: role.members || [],
        memberCount: role.memberCount || 0,
        originalData: role,
        type: 'role',
        parentUniqueKey: undefined,
      };
      flatTableData.push(roleRow);
    });
  }

  // 传递 flat 数据给 Drawer
  showConfiguredRolesDrawer({
    flatTableData,
    isMultiTenant,
    queryParams: props.queryParams,
  });

  emit('viewRoles');
}

function handleGotoRoleConfig(role: PermissionRoleItem) {
  const appId = props.queryParams?.appId;
  if (!appId) {
    return;
  }
  // 新页面打开角色配置页面
  const { fullPath } = router.resolve({
    name: PlatformEnterPoint.PermissionCenterApp,
    params: { appId },
    query: {
      tab: 'member',
      roleId: role.id,
      tenantId: role?.tenantId,
    },
  });
  window.open(location.origin + fullPath);
}

async function handleConfigPermission(permission: QueryPermissionRow) {
  // 确保是权限节点而不是分类节点
  if (permission.type !== 'permission') {
    return;
  }

  // 获取应用参数
  const appId = props.queryParams?.appId;
  const tenantIds = props.queryParams?.tenantIds;
  const resourceId = Number(permission.id);

  if (!appId || !resourceId) {
    console.warn('缺少必要参数：appId 或 resourceId');
    return;
  }

  try {
    await fetchPermissionRoles({}, {
      appId,
      resourceId,
      tenantIds: tenantIds || [],
    });

    // 处理接口返回数据
    const responseData = permissionRolesData.value?.data;
    const availableRolesData: any[] = [];
    const isMultiTenant = !!(tenantIds?.length);

    if (responseData?.code === 0 && responseData?.data) {
      const data = responseData.data;

      // 判断是否为多租户数据
      const isMultiTenantData = data && typeof data === 'object' && 'tenants' in data;

      if (isMultiTenantData) {
        // 多租户数据处理
        Object.entries(data.tenants).forEach(([, tenantGroups]) => {
          tenantGroups.forEach((group: any) => {
            availableRolesData.push({
              roleId: group.id,
              roleName: group.name,
              projectName: group.tenant,
              tenantId: group.tenantId,
              memberCount: 0,
            });
          });
        });
      } else {
        // 非多租户数据处理
        if (data && typeof data === 'object' && 'groups' in data && Array.isArray(data.groups)) {
          data.groups.forEach((group: any) => {
            availableRolesData.push({
              roleId: group.id,
              roleName: group.name,
              projectName: group.tenant,
              tenantId: group.tenantId,
              memberCount: 0,
            });
          });
        }
      }
    }

    // 打开抽屉并传递处理好的数据
    showPermissionConfigDrawer({
      permission,
      queryParams: props.queryParams,
      availableRoles: availableRolesData,
      isMultiTenant,
      loading: fetchingPermissionRoles.value,
    });
  } catch (error) {
    console.error('获取权限配置数据失败:', error);
  }

  emit('configPermission', permission);
}
</script>
