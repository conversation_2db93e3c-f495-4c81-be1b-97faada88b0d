{"name": "@hg-tech/utils-vue", "type": "module", "version": "1.1.4", "description": "Vue 工具合集", "repository": {"type": "git", "url": "*************************:tech/platform/hypergryph-tech-fe-mono.git"}, "exports": {".": {"types": "./es/index.d.ts", "import": "./es/index.js"}}, "main": "./es/index.js", "module": "./es/index.js", "types": "./es/index.d.ts", "files": ["./es", "CHANGELOG.md", "readme.md"], "scripts": {"dev": "tsc -w", "build": "tsc", "test": "vitest run --dom --typecheck", "tdd": "vitest watch --dom --typecheck"}, "peerDependencies": {"vue": "^3"}, "dependencies": {"@hg-tech/utils": "workspace:*"}, "devDependencies": {"@vue/test-utils": "catalog:", "happy-dom": "catalog:", "vitest": "catalog:", "vue": "catalog:"}}