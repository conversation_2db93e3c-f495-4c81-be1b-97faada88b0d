import { useStyleTag } from '@vueuse/core';

export function useViewTransitionClick<Args extends Array<any>>(callback: (...args: Args) => unknown) {
  useStyleTag(`:root {
  &::view-transition-old(root),
  &::view-transition-new(root) {
    animation: none;
    mix-blend-mode: normal;
  }
  
  &::view-transition-old(root) {
    z-index: 99999;
  }
  
  &::view-transition-new(root) {
    z-index: 1;
  }
}`);

  return async function (event: MouseEvent, ...args: Args) {
    const x = event.clientX;
    const y = event.clientY;
    const endRadius = Math.hypot(Math.max(x, window.innerWidth - x), Math.max(y, window.innerHeight - y));

    // 兼容性处理
    if (!('startViewTransition' in document)) {
      return callback(...args);
    }

    const transition = document.startViewTransition(() => callback(...args));
    await transition.ready;

    // 创建动画关键帧 - 统一使用从中心扩散透明的效果
    const keyframes = [];
    const duration = 600;
    const steps = duration / 1000 * 30;

    for (let i = 0; i <= steps; i++) {
      const progress = i / steps;
      // 内环半径
      const innerRadius = Math.max(0, endRadius * progress - 120);
      // 外环半径
      const outerEdgeRadius = innerRadius + 240;

      // 从中心扩散透明区域
      const mask = `radial-gradient(circle at ${x}px ${y}px, transparent 0px, transparent ${innerRadius}px, black ${outerEdgeRadius}px)`;

      keyframes.push({
        mask,
        WebkitMask: mask, // Safari 兼容性
        offset: progress,
      });
    }

    const animation = document.documentElement.animate(
      keyframes,
      {
        duration,
        easing: 'ease-out',
        pseudoElement: '::view-transition-old(root)',
        fill: 'forwards', // 保持最终状态
      },
    );

    // 等待动画完成，防止闪烁
    await animation.finished;
  };
}
