import type { AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { RequestService } from '@hg-tech/request-api';
import { message } from 'ant-design-vue';

/**
 * @deprecated 需要被迁移到 CommonErrorCode
 */
enum ResultEnum {
  ERROR = -1,
  SUCCESS = 200,
  SUCCESS2 = 0,
  TIMEOUT = 401,
  API_ERROR = 7,
}

export enum CommonErrorCode {
  Success = 0, // 成功
  NoAuth = 1, // 未认证
  BadRequest = 3, // 错误请求
  Forbidden = 7, // 权限不足
}

export type CommonBaseRes<T = unknown, C extends number = CommonErrorCode> = null | {
  code?: CommonErrorCode | C;
  data?: T;
  message?: string;
  msg?: string;
};

export interface RequestAdditionConfig {
  /**
   * 跳过错误处理
   */
  skipErrorHandler?: boolean;
}

interface AuthInterceptorsOptions {
  authTokens: Array<{
    /**
     * 请求头权限 token 字段
     */
    accessTokenKey: string;
    /**
     * 获取 token 方法
     */
    getAccessToken: () => string | undefined;
    /**
     * 更新后的 token 字段
     */
    newTokenKey?: string;
    /**
     * 设置新的 token
     */
    setNewToken?: (token: string) => void;
  }>;
}
/**
 * 处理权限相关拦截器
 */
function withAuthInterceptors<S extends RequestService<any>>(service: S, options: AuthInterceptorsOptions) {
  service.addRequestInterceptors((config) => {
    for (const authToken of options.authTokens) {
      const token = authToken.getAccessToken();
      if (token) {
        // 附带 token 到请求头
        config.headers[authToken.accessTokenKey] = token;
      }
    }
    return config;
  });

  // update token
  service.addResponseInterceptor((res) => {
    // 如果有新的token，则更新token
    for (const authToken of options.authTokens) {
      if (authToken.newTokenKey && res?.headers[authToken.newTokenKey]) {
        authToken.setNewToken?.(res.headers[authToken.newTokenKey]);
      }
    }
    return res;
  });

  return service;
}

interface ErrorMsgInterceptorsOptions<Res extends CommonBaseRes> {
  onUnauthorized?: (res: AxiosResponse<Res>) => void | Promise<AxiosResponse<Res> | void>;
  onForbidden?: (res: AxiosResponse<Res>) => void | Promise<AxiosResponse<Res> | void>;
  getResErrorMsg?: (res: Res | undefined, fallback?: string) => string;
}

export function getDefaultErrorMsg(res?: CommonBaseRes, fallback?: string) {
  return `${res?.message || res?.msg || fallback || '服务器返回异常'}`;
}

/**
 * 处理消息提示相关拦截器
 */
function withErrorMsgInterceptors<S extends RequestService<any>, Res extends CommonBaseRes>(service: S, options: ErrorMsgInterceptorsOptions<Res>) {
  type RequestConfig = InternalAxiosRequestConfig & RequestAdditionConfig;
  const getResErrorMsg = options.getResErrorMsg ?? getDefaultErrorMsg;

  service.addResponseInterceptor(
    async (response) => {
      const payload: Res | undefined = response?.data;
      const reqConfig: RequestConfig = response?.config;

      if (reqConfig?.skipErrorHandler) {
        return response;
      }

      // 非 GET 请求强制提示
      const forceNotify = reqConfig?.method?.toUpperCase() !== 'GET';

      if (payload?.code != null) {
        switch (payload.code) {
          case CommonErrorCode.Success:
          case ResultEnum.SUCCESS as any:
            return response;
          case CommonErrorCode.NoAuth:
          case ResultEnum.TIMEOUT as any:
            return await options.onUnauthorized?.(response) ?? response;
          case CommonErrorCode.Forbidden:
            return await options.onForbidden?.(response) ?? response;
          case CommonErrorCode.BadRequest:
          default:
            // 不是正确的 code
            makeAggregatedErrorMsg(getResErrorMsg(payload, getDefaultErrorMsg(payload)), forceNotify);
            throw payload;
        }
      }

      return response;
    },
    async (error) => {
      const reqConfig: RequestConfig = error?.config;

      if (reqConfig?.skipErrorHandler) {
        throw error;
      }

      const payload: Res | undefined = error.response?.data;
      const status = error.response?.status;
      const serverMsg = getResErrorMsg(payload, getDefaultErrorMsg(payload, error.message));

      // 非 GET 请求强制提示
      const forceNotify = reqConfig?.method?.toUpperCase() !== 'GET';

      switch (status) {
        case 400:
          makeAggregatedErrorMsg(serverMsg ?? `请求参数出错`, forceNotify);
          break;
        case 401:
          await options.onUnauthorized?.(error.response);
          throw error;
        case 403:
          makeAggregatedErrorMsg(serverMsg ?? '服务器拒绝访问', forceNotify);
          await options.onForbidden?.(error.response);
          throw error;
        case 404:
          makeAggregatedErrorMsg(serverMsg ?? `请求地址出错: ${reqConfig?.url}`, forceNotify);
          break;
        case 408:
          makeAggregatedErrorMsg(serverMsg ?? '请求超时', forceNotify);
          break;
        case 500:
          makeAggregatedErrorMsg(serverMsg ?? '服务器内部错误', forceNotify);
          break;
        case 501:
          makeAggregatedErrorMsg(serverMsg ?? '服务未实现', forceNotify);
          break;
        case 502:
          makeAggregatedErrorMsg(serverMsg ?? '服务器网关错误', forceNotify);
          break;
        case 503:
          makeAggregatedErrorMsg(serverMsg ?? '服务不可用', forceNotify);
          break;
        case 504:
          makeAggregatedErrorMsg(serverMsg ?? '服务器网关超时', forceNotify);
          break;
        case 505:
          makeAggregatedErrorMsg(serverMsg ?? '服务器不支持该请求', forceNotify);
          break;
        default:
          makeAggregatedErrorMsg(serverMsg ?? `请求出错`, forceNotify);
          break;
      }

      throw error;
    },
  );

  return service;
}

/**
 * 聚合错误提示，防止重复提示
 */
const showingRequestError = new Set<string>();
async function makeAggregatedErrorMsg(msg?: string, force = false) {
  if (!msg || (!force && showingRequestError.has(msg))) {
    return;
  }
  showingRequestError.add(msg);
  await message.error(msg);
  showingRequestError.delete(msg);
}

export function createRequestService<Config extends Record<string, any>, Res extends CommonBaseRes<unknown, number> = CommonBaseRes>(
  configs: AuthInterceptorsOptions & ErrorMsgInterceptorsOptions<Res>,
  ...requestOptions: ConstructorParameters<typeof RequestService<Config & RequestAdditionConfig>>
): RequestService<Config & RequestAdditionConfig> {
  const baseReq = new RequestService(...requestOptions);

  withAuthInterceptors(baseReq, configs);
  withErrorMsgInterceptors(baseReq, configs);

  return baseReq;
}
