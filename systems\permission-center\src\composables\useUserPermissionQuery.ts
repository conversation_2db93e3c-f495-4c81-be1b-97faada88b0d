import type { GetUserPermissionsResponse, PermissionRoleItem } from '../api/query';
import type {
  PermissionCategory,
  PermissionQueryStats,
  QueryPermissionRow,
} from '../views/query/permission';
import type { PermissionCategoryWithUncategorized } from './usePermissionCategories';

/**
 * 按用户权限查询数据处理
 */
export function useUserPermissionQuery() {
  function transformPermissionsToTableData(
    response: GetUserPermissionsResponse['data'],
    categoriesWithPermissions: PermissionCategoryWithUncategorized[],
    onlyShowEnabled?: boolean,
  ): {
      tableData: QueryPermissionRow[];
      categories: PermissionCategory[];
      stats: PermissionQueryStats;
    } {
    const tableData: QueryPermissionRow[] = [];
    const categoryMap = new Map<string, number>();
    let enabledCount = 0;
    const rolesSet = new Set<number>();

    // 获取用户有权限的权限点ID集合和角色信息
    const enabledPermissionIds = new Set<number>();
    const permissionRoleMap = new Map<number, PermissionRoleItem[]>();

    if (response?.resource) {
      Object.entries(response.resource).forEach(([permissionIdStr, roleItems]) => {
        const permissionId = Number.parseInt(permissionIdStr);
        enabledPermissionIds.add(permissionId);

        permissionRoleMap.set(permissionId, roleItems);
        roleItems.forEach((role) => rolesSet.add(role.id));
      });
    }

    // 用于收集每个分类下的权限节点，以便计算childCount
    const categoryPermissionsMap = new Map<string, QueryPermissionRow[]>();

    // 遍历所有分类和权限点，构建扁平化数据
    categoriesWithPermissions.forEach((category) => {
      const categoryName = category.name;
      const categoryPermissions: QueryPermissionRow[] = [];

      category.resources?.forEach((permission) => {
        if (!permission.id) {
          return;
        }

        const isEnabled = enabledPermissionIds.has(permission.id);
        const roles = permissionRoleMap.get(permission.id) || [];

        // 应用筛选逻辑：只处理启用状态筛选
        if (onlyShowEnabled && !isEnabled) {
          return;
        }

        // 创建权限节点 - 获取对应的category对象获取ID用于parentUniqueKey
        const categoryInfo = categoriesWithPermissions.find((cat) => cat.name === categoryName);
        const categoryId = categoryInfo?.id ?? categoryName; // 如果找不到ID，使用name作为fallback

        const permissionRow: QueryPermissionRow = {
          id: permission.id,
          uniqueKey: `category-${categoryId}-permission-${permission.id}`,
          name: permission.name || '',
          type: 'permission',
          parentUniqueKey: `category-${categoryId}`,
          categoryId: categoryName,
          code: permission.code || '',
          description: permission.description || '',
          isEnabled,
          roles,
          rawData: permission as any,
        };

        categoryPermissions.push(permissionRow);

        // 统计状态
        if (isEnabled) {
          enabledCount++;
        }
      });

      // 如果该分类有权限节点，添加到map中
      if (categoryPermissions.length > 0) {
        categoryPermissionsMap.set(categoryName, categoryPermissions);
        categoryMap.set(categoryName, categoryPermissions.length);
      }
    });

    // 分类节点 + 权限节点
    categoryPermissionsMap.forEach((permissions, categoryName) => {
      // 添加分类节点 - 获取对应的category对象获取ID
      const categoryInfo = categoriesWithPermissions.find((cat) => cat.name === categoryName);
      const categoryId = categoryInfo?.id ?? categoryName; // 如果找不到ID，使用name作为fallback

      const categoryRow: QueryPermissionRow = {
        id: categoryId,
        uniqueKey: `category-${categoryId}`,
        name: categoryName,
        type: 'category',
        childCount: permissions.length,
        categoryData: {
          label: categoryName,
          value: categoryName,
          count: permissions.length,
        },
      };

      tableData.push(categoryRow);

      // 添加该分类下的所有权限节点
      tableData.push(...permissions);
    });

    // 生成分类列表
    const categories: PermissionCategory[] = Array.from(categoryMap.entries()).map(([value, count]) => ({
      label: value,
      value,
      count,
    }));

    // 生成统计信息
    const stats: PermissionQueryStats = {
      totalCount: tableData.filter((row) => row.type === 'permission').length,
      enabledCount,
      roleCount: rolesSet.size,
      categoryCount: categories.length,
    };

    return {
      tableData,
      categories,
      stats,
    };
  }

  return {
    transformPermissionsToTableData,
  };
}
