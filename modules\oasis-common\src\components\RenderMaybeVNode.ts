import { type PropType, type VNode, defineComponent } from 'vue';
import { factoryCall } from '@hg-tech/utils';

export type RenderNode = null | string | VNode | VNode[];
export type MaybeVNode = RenderNode | (() => RenderNode);

/**
 * 将 vNodes 以 template 形式进行渲染
 */
export const RenderMaybeVNode = defineComponent({
  props: {
    nodes: [Array, Object, String, Function] as PropType<MaybeVNode>,
  },
  setup(props) {
    return factoryCall(props.nodes);
  },
});
