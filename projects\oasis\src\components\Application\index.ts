import { withInstall } from '/@/utils';

import appProvider from './src/AppProvider.vue';

import appProjectSelect from './src/AppProjectSelect.vue';
import appHelpDoc from './src/helpDoc/AppHelpDoc.vue';

export { useAppProviderContext } from './src/useAppContext';

export const AppProvider = withInstall(appProvider);
export const AppProjectSelect = withInstall(appProjectSelect);
export const AppHelpDoc = withInstall(appHelpDoc);
