import { ForgeonThemeCssVar } from '@hg-tech/forgeon-style';

export enum instanceTypeEnum {
// 1 - 通用；2 - 编译；3 - 非编译
  All = 0,
  Common = 1,
  Compile = 2,
  NonCompile = 3,
}

export const instanceTypeOptions = [
  {
    label: '全部',
    value: instanceTypeEnum.All,
  },
  {
    label: '通用',
    value: instanceTypeEnum.Common,
  },
  {
    label: '代码',
    value: instanceTypeEnum.Compile,
  },
  {
    label: '资产',
    value: instanceTypeEnum.NonCompile,
  },
];

// 提交的检查状态
export enum checkStateType {
// 1 - 检查中；2 - 通过；3 - 出错；4 - 中断
  Checking = 1,
  Passed = 2,
  Failed = 3,
  Interrupted = 4,
}

export const checkStateOptions = [
  {
    label: '检查中',
    value: checkStateType.Checking,
    colorStyle: {
      color: ForgeonThemeCssVar.FunctionalWarning1Default,
    },
    bgStyle: {

      backgroundColor: ForgeonThemeCssVar.FunctionalWarning1Default,
    },
  },
  {
    label: '通过',
    value: checkStateType.Passed,
    colorStyle: {
      color: ForgeonThemeCssVar.FunctionalSuccess1Default,
    },
    bgStyle: {
      backgroundColor: ForgeonThemeCssVar.FunctionalSuccess1Default,
    },
  },
  {
    label: '出错',
    value: checkStateType.Failed,
    colorStyle: {
      color: ForgeonThemeCssVar.FunctionalError1Default,

    },
    bgStyle: {
      backgroundColor: ForgeonThemeCssVar.FunctionalError1Default,
    },
  },
  {
    label: '中断',
    value: checkStateType.Interrupted,
    colorStyle: {
      color: ForgeonThemeCssVar.ContentText4,

    },
    bgStyle: {
      backgroundColor: ForgeonThemeCssVar.ContentText4,
    },
  },
];
