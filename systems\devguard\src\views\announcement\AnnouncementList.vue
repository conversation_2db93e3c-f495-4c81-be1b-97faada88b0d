<template>
  <div ref="containerRef" class="flex flex-col transition-all transition-duration-300">
    <div v-for="item in commitList" :key="item" class="line-height-20px">
      {{ item }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const commitList = ref(['123123123123123']);
const containerRef = ref<HTMLElement>();
// onMounted(() => {
//   setInterval(() => {
//     commitList.value.push(`1${new Date().getTime()}`);
//     containerRef.value?.style.setProperty('transform', `translateY(-${(commitList.value.length - 1) * 20}px)`);
//   }, 10000);
// });
</script>

<style lang="less" scoped>

</style>
