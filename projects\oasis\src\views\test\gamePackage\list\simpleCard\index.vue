<template>
  <div :class="prefixCls">
    <div v-if="!isLoading" :class="`${prefixCls}__card`">
      <div class="flex items-center p-2">
        <img class="h-120px w-120px rounded-2xl" :src="detail?.icon" alt="logo">
        <div class="mx-3">
          <div class="mb-3 flex items-center">
            <div class="mr-1 text-xl font-bold">
              {{ pkg?.name }}
            </div>
            <Icon
              :icon="getPlatform?.fullIcon ?? getPlatform?.icon"
              :style="{ color: getPlatform?.color }"
              :size="22"
            />
          </div>
          <ATypographyParagraph
            :ellipsis="{ rows: 2 }"
            :content="`版本: ${detail?.version}`"
            class="w-318px"
          />
        </div>
        <div class="w-120px">
          <QrCode
            v-if="isMobilePlatform(detail?.platform)"
            :value="qrCodeUrl"
            tag="img"
            :width="120"
          />
        </div>
      </div>
      <div :class="`${prefixCls}__footer`">
        <div :class="`${prefixCls}__footer-item`">
          <div class="w-1/2 flex">
            <div class="w-70px">
              大小
            </div>
            {{ formatKBSize(detail?.sizeKB) }}
          </div>
          <div class="flex">
            <div class="w-80px">
              发布时间
            </div>
            {{ formatTISOToDate(detail?.CreatedAt, true, true) }}
          </div>
        </div>
        <div v-if="showLabels?.length" :class="`${prefixCls}__footer-item`">
          <div class="w-70px">
            标签
          </div>
          <div :class="`${prefixCls}__footer-item-content`">
            <template v-for="label in showLabels" :key="label.ID">
              <div :class="`${prefixCls}__footer-label`">
                <div class="mr-2 font-bold">
                  {{ label.name }}
                </div>
                <div
                  v-for="val in label.values"
                  :key="val.ID"
                  :class="`${prefixCls}__footer-label-item`"
                  :style="{
                    backgroundColor: val.checked ? label.color || defaultColorList[0] : undefined,
                    color: val.checked ? ForgeonThemeCssVar.ContentComponents1 : ForgeonThemeCssVar.ContentComponents2,
                    borderColor: 'transparent',
                  }"
                  :isSingle="label.singleChoice"
                  :checked="val.checked"
                >
                  {{ val.value }}
                </div>
              </div>
            </template>
          </div>
        </div>
        <div v-if="detail?.marks" :class="`${prefixCls}__footer-item`">
          <div class="w-70px">
            标记
          </div>
          <div :class="`${prefixCls}__footer-item-content`">
            <div
              v-for="mark in detail?.marks"
              :key="mark.name"
              :class="`${prefixCls}__footer-mark`"
            >
              <div :class="`${prefixCls}__footer-mark-before`" />
              <div
                :class="`${prefixCls}__footer-mark-name`"
                :style="{
                  backgroundColor: mark.color || defaultColorList[0],
                }"
              >
                {{ mark.name }}
              </div>
              <div :class="`${prefixCls}__footer-mark-after`" />
            </div>
          </div>
        </div>
        <div v-if="detail?.attachments?.length" :class="`${prefixCls}__footer-item`">
          <div class="w-70px">
            关联资源
          </div>
          <div :class="`${prefixCls}__footer-item-content`">
            <template v-for="att in detail?.attachments" :key="att.ID">
              <div :class="`${prefixCls}__footer-item-att`">
                <div class="max-w-200px truncate">
                  {{ att.name }}
                </div>
              </div>
            </template>
          </div>
        </div>
        <div v-if="buildInfo" :class="`${prefixCls}__footer-item !items-start`">
          <div class="w-70px text-nowrap">
            打包信息
          </div>

          <div class="font-normal">
            <div class="flex items-center gap-2">
              <div class="flex items-center gap-2">
                <span class="min-w-50px text-right text-nowrap">引擎CL:</span>

                <div class="w-170px flex items-center">
                  <span v-if="buildInfo.engineCL">{{ buildInfo.engineCL }}</span>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <span class="min-w-70px text-right text-nowrap">Shelve CL:</span>

                <div class="w-170px flex items-center">
                  <span v-if="buildInfo.shelveCL">{{ buildInfo.shelveCL }}</span>
                </div>
              </div>
            </div>
            <div class="mt-2 flex items-center gap-2">
              <div class="flex items-center gap-2">
                <span class="min-w-50px text-right text-nowrap">工程CL: </span>

                <div class="w-170px flex items-center">
                  <span v-if="buildInfo.projectCL">{{ buildInfo.projectCL }}</span>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <span class="min-w-70px text-right text-nowrap"> 触发人: </span>
                {{ getNickNameByFieldName(buildInfo.trigger) }}
              </div>
            </div>
          </div>
        </div>
        <div :class="`${prefixCls}__footer-item`">
          <div class="w-70px">
            MD5
          </div>
          <span class="font-normal">
            {{ getMD5ByDownloadUrl(detail?.downloadLink) }}
          </span>
        </div>
        <div v-if="detail?.releaseNote" :class="`${prefixCls}__footer-item`">
          <div class="w-70px">
            备注
          </div>
          <ATypographyParagraph
            class="w-0 flex-1 text-13px font-normal !mb-0"
            :ellipsis="{ rows: 2 }"
            :content="detail.releaseNote"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="GamePackageSimpleCard">
import { TypographyParagraph as ATypographyParagraph } from 'ant-design-vue';
import { cloneDeep, sortBy } from 'lodash-es';
import { ForgeonThemeCssVar } from '@hg-tech/forgeon-style';
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import { getMD5ByDownloadUrl } from '../helper';
import type {
  GameLabelsListItem,
  GamePackagesListItem,
  GamePackagesVersionsListItem,
  ProjectPkgBuildInfoListItem,
} from '/@/api/page/model/testModel';
import {
  getGamePackageByID,
  getGamePackagesVersionByID,
  getProjectPkgBuildInfoListByPage,
} from '/@/api/page/test';
import { defaultColorList } from '/@/components/ColorPopover';
import Icon from '/@/components/Icon';
import { QrCode } from '/@/components/Qrcode';
import { useUserList } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { formatTISOToDate } from '/@/utils/dateUtil';
import { formatKBSize } from '/@/utils/file/size';
import { isMobilePlatform, platformOptions } from '/@/views/test/gamePackage/settings/settings.data';
import { useGlobSetting } from '/@/hooks/setting';
import { useMessage } from '/@/hooks/web/useMessage';

const { prefixCls } = useDesign('game-package-simple-card');
const { currentRoute } = useRouter();
const projectID = Number(currentRoute.value.query.p);
const pid = Number(currentRoute.value.query.pid);
const pkgID = Number(currentRoute.value.params.id);
const verID = Number(currentRoute.value.params.versionID);

/**
 * 逻辑由 withAuthCheck 接管，此处仅标记
 * @deprecated 后续需要重构此类侵入全局的方式
 */
const accessToken = currentRoute.value.query.accessToken as string;
const detail = ref<GamePackagesVersionsListItem>();
const pkg = ref<GamePackagesListItem>();
const showLabels = ref<GameLabelsListItem[]>([]);
const { getUserList, getNickNameByFieldName } = useUserList();
// 打包信息
const buildInfo = ref<ProjectPkgBuildInfoListItem>();

const isLoading = ref(false);
const { createMessage } = useMessage();
// 获取host, 将http替换为https
const { beOrigin } = useGlobSetting();

// ios二维码下载用前缀
const itms = 'itms-services://?action=download-manifest&url=';

const qrCodeUrl = computed(() => `${detail.value?.platform === 2 ? itms : ''}${beOrigin}${detail.value?.shortURL?.shortURL}`);

async function getPkg() {
  const { regamePkg } = await getGamePackageByID(pid || projectID, pkgID);
  pkg.value = regamePkg;
}
async function getVersion() {
  const { repkgVersion } = await getGamePackagesVersionByID(pid || projectID, pkgID, verID);
  detail.value = repkgVersion;
}

const getPlatform = computed(() => {
  return platformOptions.find((e) => e.value === detail.value?.platform);
});

// 处理标签选中
function handleLabelCheck() {
  let temp = cloneDeep(detail.value?.labels ?? []);

  temp = sortBy(temp, 'sort');
  temp?.forEach((e) => {
    e.values?.forEach((val) => {
      const findVal = detail.value?.labels
        ?.find((d) => d.ID === e.ID)
        ?.values
        ?.find((v) => v.ID === val.ID);

      val.checked = !!findVal;
    });
  });
  showLabels.value = temp;
}

async function getBuildInfo() {
  const { list } = await getProjectPkgBuildInfoListByPage(pid || projectID, {
    page: 1,
    pageSize: 999,
  });
  buildInfo.value = list?.find((e) => e.versionID === detail.value?.ID);
}

async function init() {
  isLoading.value = true;
  if (!pkgID) {
    createMessage.warning('游戏包ID不存在');
    return;
  }
  if (!projectID && !pid) {
    createMessage.warning('项目ID不存在');
    return;
  }
  if (!verID) {
    createMessage.warning('版本ID不存在');
    return;
  }
  try {
    await Promise.all([
      getPkg(),
      getVersion(),
      getBuildInfo(),
      getUserList(),
    ]);
    handleLabelCheck();
  } finally {
    isLoading.value = false;
  }
}

init();
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-game-package-simple-card';
.@{prefix-cls} {
  width: 100%;
  min-height: 100vh;
  background-color: @table-background-color;

  &__card {
    width: fit-content;
    max-width: 600px;
    overflow: hidden;
    transform: scale(2);
    transform-origin: left top;
    border: 1px solid @report-card-background;
    border-radius: 8px;
    background-color: @table-background-color;
  }

  &__footer {
    padding: 8px;
    background-color: @member-card-background;

    &-item {
      display: flex;
      position: relative;
      z-index: 1;
      align-items: center;
      padding: 8px 16px;
      border-radius: 8px;
      background-color: @FO-Container-Fill1;
      font-weight: bold;

      &:not(:last-child) {
        margin-bottom: 8px;
      }

      &-att {
        display: flex;
        position: relative;
        align-items: center;
        margin-right: 16px;
        padding: 2px 10px;
        border-radius: 100px;
        background-color: @FO-Container-Fill5;
        font-size: 10px;
      }

      &-content {
        display: flex;
        flex: 1;
        flex-wrap: wrap;
        align-items: center;
        width: 0;
        row-gap: 4px;
      }
    }

    // 菱形背景
    &-mark {
      position: relative;

      &-before {
        position: absolute;
        z-index: 1;
        top: 0;
        left: 0;

        &::before {
          content: ' ';
          position: absolute;
          z-index: 2;
          top: -2px;
          left: 0;
          width: 0;
          height: 0;
          border-top: 12px solid @FO-Container-Fill1;
          border-right: 12px solid transparent;
          border-bottom: 12px solid @FO-Container-Fill1;
        }
      }

      &-name {
        display: flex;
        position: relative;
        align-items: center;
        margin-right: 4px;
        padding: 2px 10px;
        color: #fff;
        font-size: 10px;
        line-height: 16px;
        white-space: nowrap;
      }

      &-after {
        position: absolute;
        top: 0;
        right: 0;

        &::after {
          content: ' ';
          position: absolute;
          z-index: 2;
          top: -2px;
          left: -16px;
          width: 0;
          height: 0;
          border-top: 12px solid @FO-Container-Fill1;
          border-bottom: 12px solid @FO-Container-Fill1;
          border-left: 12px solid transparent;
        }
      }

      &-color-picker {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 16px;
        height: 16px;
        transform: rotate(45deg);
      }
    }

    &-label {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      padding: 2px 2px 2px 8px;
      border-radius: 15px;
      background-color: @FO-Container-Fill5;
      font-size: 10px;
      row-gap: 4px;

      &:not(:last-child) {
        margin-right: 4px;
      }

      &-item {
        display: flex;
        align-items: center;
        padding: 2px 8px;
        border-radius: 4px;
        color: @FO-Content-Components1;
        font-size: 8px;
        white-space: nowrap;
        user-select: none;

        &[isSingle='false'] {
          margin-right: 4px;
        }

        &[isSingle='true'] {
          border-radius: 100px;
        }
      }
    }
  }
}
</style>
