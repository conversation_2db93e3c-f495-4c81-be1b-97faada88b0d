<template>
  <PageLayout>
    <template #headerTitle>
      <div class="FO-Font-B18 leading-relaxed">
        权限管理中心
      </div>
    </template>
    <template #headerActions />

    <div class="h-full flex flex-col py-16px">
      <div class="w-full flex items-center justify-between gap-4 px-16px">
        <div class="flex items-center gap-2">
          <div class="FO-Font-B20">
            应用列表
          </div>
          <EllipsisText class="FO-Font-R14 min-w-0 flex-1 c-FO-Content-Text3">
            你可以在应用中创建角色，管理权限，不同应用中的权限互相独立。
          </EllipsisText>
        </div>
        <div class="flex items-center gap-12px">
          <Button class="btn-fill-plain" @click="handleGoToPermissionQuery">
            <template #icon>
              <SearchIcon />
            </template>
            权限查询
          </Button>
          <Button v-if="userProfileInfoStore.isSuperAdmin" class="btn-fill-primary" @click="handleAddApp">
            <template #icon>
              <AddIcon />
            </template>
            创建应用
          </Button>
        </div>
      </div>
      <div class="m-16px rounded-12px bg-FO-Container-Fill1 p-16px px-16px">
        <div class="flex items-center justify-between">
          <PermissionSolidTabs
            v-model:value="currentTab" :tabList="[
              { key: AppTab.My, label: '我的应用' },
              { key: AppTab.All, label: '全部应用' },
            ]"
          />
          <div class="flex items-center gap-6">
            <Input v-model:value="inputSearchKw" placeholder="搜索应用名称或code" class="w-240px" allowClear>
              <template #prefix>
                <SearchIcon class="c-FO-Content-Icon3" />
              </template>
            </Input>
          </div>
        </div>
      </div>
      <div class="min-h-0 flex-1 overflow-y-auto px-16px">
        <Spin :spinning="loading">
          <template v-if="Object.keys(renderAppDir).length">
            <Collapse
              v-model:activeKey="activeKeys" ghost class="flex flex-col gap-16px !bg-transparent"
              expandIconPosition="start"
            >
              <template #expandIcon="panelProps">
                <CaretRightOutlined :rotate="panelProps?.isActive ? 90 : 0" />
              </template>
              <Collapse.Panel
                v-for="(apps, category) in renderAppDir" :key="category"
                class="overflow-hidden bg-FO-Container-Fill1 !rounded-xl"
              >
                <template #header>
                  <div class="flex items-center gap-12px">
                    <span class="FO-Font-B14">{{ category }}</span>
                    <div class="rounded-4px bg-FO-Container-Fill2 px-8px py-1px">
                      <span class="FO-Font-R14">{{ apps.length }}</span>
                    </div>
                  </div>
                </template>

                <div class="card-grid">
                  <Card
                    v-for="app in apps" :key="app.code"
                    class="group rounded-xl bg-FO-Container-Fill2 hover:bg-FO-Container-Fill1"
                    :bodyStyle="{ padding: '16px' }" :class="app.isAdmin ? 'cursor-pointer' : 'cursor-not-allowed'"
                    hoverable :bordered="false" @click="() => handleEnterApp(app)"
                  >
                    <div class="h-full flex flex-col justify-between gap-12px">
                      <div class="flex items-start gap-12px">
                        <img
                          v-if="app.icon" :src="app.icon"
                          class="h-40px w-40px overflow-hidden rounded-2xl object-cover"
                        >

                        <img v-else :src="AvatarIcon" class="size-[40px]">

                        <div class="min-w-0 flex-1">
                          <div class="flex items-center justify-between gap-2">
                            <div class="FO-Font-B16 truncate c-FO-Content-Text1">
                              <template v-if="app.name && searchKw">
                                <span
                                  v-for="(seg, idx) in getHighlightSegments(app.name, searchKw)" :key="idx"
                                  :class="{ 'c-FO-Brand-Primary-Default': seg.highlight }"
                                >
                                  {{ seg.text }}
                                </span>
                              </template>
                              <span v-else>{{ app.name }}</span>
                            </div>
                            <div
                              v-if="app.isMultiTenant"
                              class="FO-Font-R12 rounded-4px bg-FO-Datavis-Blue3 px-4px py-2px c-FO-Datavis-Blue1"
                            >
                              项目
                            </div>
                          </div>
                          <div class="FO-Font-R12 c-FO-Content-Text3">
                            <template v-if="app.code && searchKw">
                              <span
                                v-for="(seg, idx) in getHighlightSegments(app.code, searchKw)" :key="idx"
                                :class="{ 'c-FO-Brand-Primary-Default': seg.highlight }"
                              >
                                {{ seg.text }}
                              </span>
                            </template>
                            <span v-else>{{ app.code }}</span>
                          </div>
                        </div>
                      </div>

                      <EllipsisText v-if="app.description" class="FO-Font-R12 c-FO-Content-Text2">
                        {{ app.description }}
                      </EllipsisText>

                      <div class="w-full flex items-center justify-between">
                        <div class="flex items-center gap-8px">
                          <span class="FO-Font-R12 c-FO-Content-Text2">管理员</span>
                          <AvatarList :avatarList="app.member" @click.stop />
                        </div>
                        <Tooltip v-if="!app.isAdmin" title="暂无权限，点击管理员头像获取权限">
                          <LockIcon class="c-FO-Content-Icon4" />
                        </Tooltip>
                      </div>
                    </div>
                  </Card>
                </div>
              </Collapse.Panel>
            </Collapse>
          </template>
          <Empty
            v-else class="h-full flex flex-col items-center justify-center c-FO-Content-Text3"
            description="暂无应用数据"
          />
        </Spin>
      </div>
    </div>
    <AppFormDrawerHolder />
  </PageLayout>
</template>

<script setup lang="ts">
import type { PermissionAppInfoV2 } from '../../api/app';
import { EllipsisText, PlatformEnterPoint } from '@hg-tech/oasis-common';
import {
  Button,
  Card,
  Collapse,
  Empty,
  Input,
  message,
  Spin,
  Tooltip,
} from 'ant-design-vue';
import {
  CaretRightOutlined,
} from '@ant-design/icons-vue';
import { computed, ref, watch } from 'vue';
import { match } from 'pinyin-pro';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import { createPermissionApp, getPermissionAppCategories, getPermissionApps } from '../../api/app';
import { groupBy } from 'lodash';
import { useRouter } from 'vue-router';
import PageLayout from '../../components/PageLayout.vue';
import AppFormDrawer from '../app/components/AppFormDrawer.vue';
import AvatarList from '../app/components/AvatarList.tsx';
import AddIcon from '../../assets/icons/SystemStrokeAdd.svg?component';
import SearchIcon from '../../assets/icons/SystemStrokeSearch.svg?component';
import AvatarIcon from '../../assets/icons/avatar.svg?url';
import LockIcon from '../../assets/icons/SystemFillLock.svg?component';
import { useUserProfileInfoStore } from '../../store/modules/userProfileInfo';
import { refDebounced } from '@vueuse/core';
import PermissionSolidTabs from '../../components/PermissionSolidTabs.vue';
import { useRouteQuery } from '@vueuse/router';
import { useHighlight } from '../../composables/useHighlight';

enum AppTab {
  My = 'my',
  All = 'all',
}

const router = useRouter();
const userProfileInfoStore = useUserProfileInfoStore();
const { getHighlightSegments } = useHighlight();
const inputSearchKw = ref('');
const searchKw = refDebounced(inputSearchKw, 200);

const currentTab = useRouteQuery('tab', AppTab.My);
// 展开收起状态管理 - 默认所有分类都展开
const activeKeys = ref<string[]>([]);

const { data, execute: fetchAllApps, loading } = useLatestPromise(getPermissionApps);
const { data: categoriesData, execute: fetchCategories } = useLatestPromise(getPermissionAppCategories);

const renderAppDir = computed(() => {
  let filteredApps = currentTab.value === AppTab.My
    ? (data.value?.data?.data?.myApp || [])
    : (data.value?.data?.data?.allApp || []);

  // 搜索过滤
  if (searchKw.value) {
    const kw = searchKw.value.toLocaleLowerCase();
    filteredApps = filteredApps.filter((item) => {
      const name = item.name?.toLocaleLowerCase() || '';
      const code = item.code?.toLocaleLowerCase() || '';
      return name.includes(kw) // 匹配名称
        || code.includes(kw) // 匹配code
        || match(name, kw)?.length; // 匹配拼音
    });
  }

  // 按分类分组
  const groupedApps = groupBy(filteredApps, (i) => i.category || '未分类');

  // 获取分类顺序
  const categoryOrder = categoriesData.value?.data?.data || [];

  // 按照分类顺序重新排列，未分类放在最后
  const orderedResult: Record<string, typeof filteredApps> = {};

  // 先按顺序添加有序分类
  categoryOrder.forEach((category) => {
    if (groupedApps[category]) {
      orderedResult[category] = groupedApps[category];
    }
  });

  // 最后添加未分类
  if (groupedApps['未分类']) {
    orderedResult['未分类'] = groupedApps['未分类'];
  }

  return orderedResult;
});

watch(renderAppDir, (newValue) => {
  activeKeys.value = Object.keys(newValue);
}, { immediate: true, deep: true });

const [AppFormDrawerHolder, show] = useModalShow(AppFormDrawer);

// 初始化数据
fetchAllApps({}, {});
fetchCategories({}, {});

async function handleAddApp() {
  await show({
    title: '创建应用',
    async sentReq(formValue: any) {
      try {
        const res = await createPermissionApp({}, formValue);
        if (res.data?.code === 0) {
          const appId = res.data?.data?.id || '';
          if (appId) {
            message.success('提交成功');
            router.push({
              name: PlatformEnterPoint.PermissionCenterApp,
              params: {
                appId,
              },
            });
            return true;
          }
        }
        return false;
      } catch (error) {
        console.error(error);
        return false;
      }
    },
  });
}

function handleEnterApp(app: PermissionAppInfoV2) {
  if (!app.isAdmin) {
    return;
  }
  router.push({
    name: PlatformEnterPoint.PermissionCenterApp,
    params: {
      appId: app.id,
    },
    query: {
      fromTab: currentTab.value,
    },
  });
}

function handleGoToPermissionQuery() {
  router.push({
    name: PlatformEnterPoint.PermissionCenterQuery,
  });
}
</script>

<style scoped>
:deep(.ant-collapse-header) {
  @apply !p-16px;

  & .ant-collapse-expand-icon {
    @apply !h-auto !p-8px;
  }
}

:deep(.ant-collapse-content-box) {
  @apply !pt-0 !pb-16px;
}

.card-grid {
  @apply grid grid-cols-[repeat(auto-fill,minmax(375px,1fr))] gap-12px;

  & :deep(.ant-card) {
    @apply flex flex-col;

    & .ant-card-body {
      @apply flex-1 h-full;
    }
  }
}
</style>
