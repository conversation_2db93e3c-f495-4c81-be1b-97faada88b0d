/// https://www.figma.com/design/tDf0zHfSRgs7ZtYdsUG9um/ForgeOn%E7%BB%84%E4%BB%B6%E8%A7%84%E8%8C%83-2.0%EF%BC%88%E8%BF%81%E7%A7%BB%E4%B8%AD%EF%BC%89?node-id=3269-202&t=3uGqKm50VVfzs8ko-1

@import (reference) '@hg-tech/forgeon-style/vars.less';

// button
.btn-base() {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;

  &,
  &:hover,
  &:active,
  &:disabled,
  &.ant-btn-loading,
  &.n-button--disabled {
    border: none !important;
    box-shadow: none !important;
  }
}

/**
 * FO/Button/Primary
 */
.btn-fill-primary {
  .btn-base();

  &,
  &:focus {
    background-color: @FO-Brand-Primary-Default !important;
    color: @FO-Content-Components1 !important;
  }

  &:hover {
    background-color: @FO-Brand-Primary-Hover !important;
    color: @FO-Content-Components1 !important;
  }

  &:active {
    background-color: @FO-Brand-Primary-Active !important;
    color: @FO-Content-Components1 !important;
  }

  &:disabled,
  &.ant-btn-loading,
  &.n-button--disabled {
    background-color: @FO-Brand-Primary-Disabled !important;
    color: @FO-Content-Components2 !important;
  }
}

/**
 * FO/Button/Secondary
 */
.btn-fill-secondary {
  .btn-base();
  box-shadow: none !important;

  &,
  &:focus {
    background-color: @FO-Brand-Secondary-Default !important;
    color: @FO-Brand-Primary-Default !important;
  }

  &:hover {
    background-color: @FO-Brand-Secondary-Hover !important;
    color: @FO-Brand-Primary-Default !important;
  }

  &:active {
    background-color: @FO-Brand-Secondary-Active !important;
    color: @FO-Brand-Primary-Default !important;
  }

  &:disabled,
  &.ant-btn-loading,
  &.n-button--disabled {
    background-color: @FO-Brand-Secondary-Disabled !important;
    color: @FO-Brand-Primary-Disabled !important;
  }
}

/**
 * FO/Button/Tertiary
 */
.btn-fill-default {
  .btn-base();

  &,
  &:focus {
    background-color: @FO-Container-Fill3 !important;
    color: @FO-Content-Text1 !important;
  }

  &:hover {
    background-color: @FO-Container-Fill4 !important;
    color: @FO-Content-Text1 !important;
  }

  &:active {
    background-color: @FO-Container-Fill5 !important;
    color: @FO-Content-Text1 !important;
  }

  &:disabled,
  &.ant-btn-loading,
  &.n-button--disabled {
    background-color: @FO-Container-Fill2 !important;
    color: @FO-Content-Text3 !important;
  }
}

/**
 * FO/Button/Icon
 */
.btn-fill-basic {
  .btn-base();

  &,
  &:focus {
    background-color: @FO-Container-Fill1 !important;
    color: @FO-Content-Icon2 !important;
  }

  &:hover {
    background-color: @FO-Container-Fill1 !important;
    color: @FO-Content-Icon1 !important;
  }

  &:active {
    background-color: @FO-Container-Fill3 !important;
    color: @FO-Content-Icon1 !important;
  }

  &:disabled,
  &.ant-btn-loading,
  &.n-button--disabled {
    background-color: @FO-Container-Fill2 !important;
    color: @FO-Content-Icon4 !important;
  }
}

/**
 * FO/Button/Plenty
 */
.btn-fill-text {
  .btn-base();

  &,
  &:focus {
    background-color: @FO-Container-Fill0 !important;
    color: @FO-Content-Text1 !important;
  }

  &:hover {
    background-color: @FO-Container-Fill3 !important;
    color: @FO-Content-Text1 !important;
  }

  &:active {
    background-color: @FO-Container-Fill4 !important;
    color: @FO-Content-Text1 !important;
  }

  &:disabled,
  &.ant-btn-loading,
  &.n-button--disabled {
    background-color: @FO-Container-Fill0 !important;
    color: @FO-Content-Text3 !important;
  }
}

/**
 * FO/Button/Danger1
 */
.btn-fill-error {
  .btn-base();

  &,
  &:focus {
    background-color: @FO-Functional-Error1-Default !important;
    color: @FO-Content-Components1 !important;
  }

  &:hover {
    background-color: @FO-Functional-Error1-Hover !important;
    color: @FO-Content-Components1 !important;
  }

  &:active {
    background-color: @FO-Functional-Error1-Active !important;
    color: @FO-Content-Components1 !important;
  }

  &:disabled,
  &.ant-btn-loading,
  &.n-button--disabled {
    background-color: @FO-Functional-Error1-Disabled !important;
    color: @FO-Content-Components2 !important;
  }
}

/**
 * FO/Button/Danger2
 */
.btn-fill-error-secondary {
  .btn-base();

  &,
  &:focus {
    background-color: @FO-Functional-Error2-Default !important;
    color: @FO-Functional-Error1-Default !important;
  }

  &:hover {
    background-color: @FO-Functional-Error2-Hover !important;
    color: @FO-Functional-Error1-Default !important;
  }

  &:active {
    background-color: @FO-Functional-Error2-Active !important;
    color: @FO-Functional-Error1-Default !important;
  }

  &:disabled,
  &.ant-btn-loading,
  &.n-button--disabled {
    background-color: @FO-Functional-Error2-Disabled !important;
    color: @FO-Functional-Error1-Disabled !important;
  }
}

/**
 * FO/Button/Plain
 */
.btn-fill-plain {
  .btn-base();

  &,
  &:focus {
    background-color: @FO-Container-Fill1 !important;
    color: @FO-Content-Text1 !important;
  }

  &:hover {
    background-color: @FO-Container-Fill3 !important;
    color: @FO-Content-Text1 !important;
  }

  &:active {
    background-color: @FO-Container-Fill4 !important;
    color: @FO-Content-Text1 !important;
  }

  &:disabled,
  &.ant-btn-loading,
  &.n-button--disabled {
    background-color: @FO-Container-Fill1 !important;
    color: @FO-Content-Text3 !important;
  }
}
