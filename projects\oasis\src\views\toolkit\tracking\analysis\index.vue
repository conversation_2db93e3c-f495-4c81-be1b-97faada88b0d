<template>
  <div v-track:v="'dlihnquxgn'" :class="prefixCls">
    <Layout :class="`${prefixCls}-header-wrapper`">
      <LayoutHeader :class="`${prefixCls}-header`">
        <template #projectSelect>
          <ForgeonProjectSelector
            class="w-[160px]"
            containerClass=" bg-FO-Container-Fill2"
            :options="showProjectList"
            :value="projectID"
            :onSelect="onProjectIDSelect"
          />
        </template>
      </LayoutHeader>
    </Layout>
    <div class="px-[20px]">
      <template v-if="state.curTool">
        <div class="my-[16px] ml-1 mt-[20px] flex items-center justify-between">
          <ADropdown :overlayClassName="`${prefixCls}__dropdown`">
            <span class="inline-flex cursor-pointer items-center font-bold">
              {{ state.curTool?.toolName }}
              <Icon icon="mdi:menu-down" :size="20" />
            </span>

            <template #overlay>
              <AMenu @click="handleChangeTool">
                <AMenuItem v-for="tool in state.toolList" :key="tool.ID" :disabled="state.curTool?.ID === tool.ID">
                  {{ tool.toolName }}
                </AMenuItem>
              </AMenu>
            </template>
          </ADropdown>
          <router-link :to="{ name: 'TrackingAnalysisSettings' }">
            <Button class="bg-FO-Container-Fill1" type="text">
              <div class="flex items-center">
                <SettingConfig class="mr-1 flex" theme="outline" />
                <span>配置</span>
              </div>
            </Button>
          </router-link>
        </div>
        <div v-if="state.curTool?.ID" :key="state.curTool?.ID">
          <GrowCard class="enter-y" :loading="state.loading" :sum="state.sum" />
          <div class="enter-y md:flex md:gap-4">
            <DistributionCard
              class="my-4 md:w-1/3" title="版本分布" :chartApi="state.versionsApi"
              :toolID="state.curTool.ID" :projectID
            />
            <DistributionCard
              class="my-4 md:w-1/3" title="活跃人数" :chartApi="state.activeApi"
              :toolID="state.curTool.ID" :projectID
            />
            <DistributionCard
              class="my-4 md:w-1/3" title="使用总量" :chartApi="state.projectsApi"
              :toolID="state.curTool.ID" :projectID
            />
          </div>
          <div class="enter-y mb-4">
            <FunctionCard :toolID="state.curTool.ID" :projectID />
          </div>
        </div>
      </template>
      <div v-else-if="!state.loading" class="m-4 rounded-lg bg-FO-Container-Fill1 p-4">
        <AEmpty :image="emptyImg" description="您暂无权限查看埋点分析, 如需查看, 请点击下方按钮申请权限">
          <a-button type="primary" @click="handleContact()">
            联系技术中心服务台
          </a-button>
        </AEmpty>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TrackingToolListItem } from '/@/api/page/model/trackingModel';
import { Dropdown as ADropdown, Empty as AEmpty, Menu as AMenu, Button, Layout } from 'ant-design-vue';
import { computed, onBeforeMount, reactive, ref, watch } from 'vue';
import { SettingConfig } from '@icon-park/vue-next';
import DistributionCard from './components/DistributionCard.vue';
import FunctionCard from './components/FunctionCard.vue';
import GrowCard from './components/GrowCard.vue';
import {
  getCurUserTrackingToolAuthority,
  getTrackingToolAccess,
  getTrackingToolActive,
  getTrackingToolCover,
  getTrackingToolList,
  getTrackingToolProjects,
  getTrackingToolVersions,
} from '/@/api/page/tracking';
import Icon from '/@/components/Icon';
import { useDesign } from '/@/hooks/web/useDesign';
import { CONTACT_URL } from '/@/settings/siteSetting';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { openWindow } from '/@/utils';
import { isNullOrUnDef } from '/@/utils/is';
import LayoutHeader from '/@/layouts/default/header/index.vue';
import { useCachedProjectList } from '../../../../hooks/useProjects.ts';
import { ForgeonProjectSelector } from '@hg-tech/oasis-common';

const { prefixCls } = useDesign('tracking-analysis');
const AMenuItem = AMenu.Item;
const userStore = useUserStoreWithOut();
const emptyImg = AEmpty.PRESENTED_IMAGE_SIMPLE;
const projectID = ref<number>(0);

interface stateType {
  toolList: TrackingToolListItem[];
  curTool: Nullable<TrackingToolListItem>;
  loading: boolean;
  sum: {
    coverSum: number;
    accessSum: number;
  };
  versionsApi: any;
  activeApi: any;
  projectsApi: any;
}

const state = reactive<stateType>({
  toolList: [],
  curTool: null,
  loading: true,
  sum: {
    coverSum: 0,
    accessSum: 0,
  },
  versionsApi: (p) => getTrackingToolVersions(p),
  activeApi: (p) => getTrackingToolActive(p),
  projectsApi: (p) => getTrackingToolProjects(p),
});

const canManage = ref<boolean>(false);
const { data: projectList } = useCachedProjectList({ loadImmediately: true });
const showProjectList = computed(() => {
  const list = canManage.value ? [{ name: '全部', ID: 0 }, ...(projectList.value || [])] : projectList.value;
  return list?.map((item) => ({
    name: item.name || '未命名项目',
    id: item.ID || 0,
  }));
});
function onProjectIDSelect(id: number | undefined) {
  projectID.value = id ?? 0;
}

async function getAuthority() {
  if (!state.curTool?.ID) {
    return;
  }

  const { authority } = await getCurUserTrackingToolAuthority({ toolID: state.curTool?.ID });

  // 超管、工具创建者、工具管理员
  canManage.value = userStore.isSuperAdmin || authority.includes(0) || authority.includes(1);
  projectID.value = canManage.value ? 0 : projectList.value?.[0]?.ID || 0;
}

async function getToolList() {
  const { list } = await getTrackingToolList();

  state.toolList = list || [];
  state.curTool = list?.[0];
}

async function getToolCover() {
  if (!state.curTool || isNullOrUnDef(projectID.value)) {
    return;
  }

  const { sum } = await getTrackingToolCover({
    toolID: state.curTool?.ID,
    projectID: projectID.value,
  });

  state.sum.coverSum = sum;
}

async function getToolAccess() {
  if (!state.curTool || isNullOrUnDef(projectID.value)) {
    return;
  }

  const { sum } = await getTrackingToolAccess({
    toolID: state.curTool?.ID,
    projectID: projectID.value,
  });

  state.sum.accessSum = sum;
}

async function getChartData() {
  state.loading = true;
  await Promise.all([getToolCover(), getToolAccess()]);
  state.loading = false;
}

onBeforeMount(async () => {
  await getToolList();
  await getAuthority();
  await getChartData();
});

function handleChangeTool(event) {
  state.curTool = state.toolList.find((e) => e.ID === Number(event.key)) || null;
  getAuthority();
  getChartData();
}

function handleContact() {
  openWindow(CONTACT_URL);
}

watch(
  () => projectID.value,
  (val, oldVal) => {
    if (val !== oldVal) {
      getChartData();
    }
  },
);
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-tracking-analysis';

.@{prefix-cls} {
  &-header-wrapper {
    position: relative;
  }
  &-header {
    z-index: 10;
  }

  &__dropdown {
    & .ant-dropdown-menu-item-disabled {
      cursor: default !important;
      color: #000000d8 !important;
      background-color: @FO-Container-Background;
      margin: 4px 0 !important;
      &:first-child {
        margin-top: 0 !important;
      }
      &:hover {
        background-color: @FO-Container-Background !important;
      }
    }
  }
}

html[data-theme='dark'] .@{prefix-cls} {
  &__dropdown {
    & .ant-dropdown-menu-item-disabled {
      color: #c9d1d9 !important;
    }
  }
}
</style>
