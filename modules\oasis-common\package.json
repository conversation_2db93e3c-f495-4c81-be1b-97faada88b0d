{"name": "@hg-tech/oasis-common", "type": "module", "version": "2.0.1", "private": true, "exports": {".": {"types": "./es/index.d.ts", "import": "./dist/index.js"}, "./styles/*": "./dist/styles/*", "./style": "./dist/style.css"}, "files": ["./es", "CHANGELOG.md", "dist", "readme.md"], "scripts": {"dev": "run-p -sl 'build:* -w'", "build": "run-p build:*", "build:code": "vite build", "build:type": "vue-tsc --emitDeclarationOnly", "analyze": "vite build -- --analyze", "test": "vitest run --dom", "watch": "vite build --watch & vue-tsc --emitDeclarationOnly --watch", "postbuild": "tsx scripts/postbuild.ts", "upload:sitemap": "tsx scripts/upload-site-map.ts"}, "peerDependencies": {"@ant-design/icons-vue": "^7", "@hg-tech/event-bus": "workspace:*", "@hg-tech/forgeon-style": "workspace:*", "@hg-tech/request-api": "workspace:*", "@hg-tech/utils": "workspace:*", "@hg-tech/utils-vue": "workspace:*", "@micro-zoe/micro-app": "^1.0.0-rc.20", "@vueuse/core": "^11", "ant-design-vue": "^4", "axios": "^1", "lodash": "^4", "pinyin-pro": "^3", "ts-pattern": "^5", "vue": "^3", "vue-router": "^4", "vxe-pc-ui": "^4", "vxe-table": "^4"}, "devDependencies": {"@ant-design/icons-vue": "catalog:", "@hg-tech/configs": "workspace:*", "@hg-tech/event-bus": "workspace:*", "@hg-tech/forgeon-style": "workspace:*", "@hg-tech/forgeon-uno-config": "workspace:*", "@hg-tech/oss-uploader": "workspace:*", "@hg-tech/request-api": "workspace:*", "@hg-tech/track-sitemap": "workspace:*", "@hg-tech/utils": "workspace:*", "@hg-tech/utils-vue": "workspace:*", "@micro-zoe/micro-app": "catalog:", "@types/ali-oss": "catalog:", "@types/lodash": "catalog:", "@vueuse/core": "catalog:", "ali-oss": "catalog:", "ant-design-vue": "^4", "axios": "catalog:", "dayjs": "catalog:", "glob": "catalog:", "happy-dom": "catalog:", "lodash": "catalog:", "p-queue": "catalog:", "pinyin-pro": "catalog:", "rollup-plugin-copy": "catalog:", "ts-pattern": "catalog:", "unocss": "catalog:", "unplugin-vue-setup-extend-plus": "catalog:", "vite": "catalog:", "vite-plugin-lazy-import": "catalog:", "vite-svg-loader": "catalog:", "vitest": "catalog:", "vue": "catalog:", "vue-router": "catalog:", "vue-tsc": "catalog:", "vxe-pc-ui": "catalog:", "vxe-table": "catalog:"}}