import type { ComponentInstance } from '@vue/devtools-api';
import {
  NAvatar,
  NButton,
  NConfigProvider,
  NDropdown,
  NGlobalStyle,
  NIcon,
  NLayout,
  NLayoutContent,
  NLayoutSider,
  NP,
  NText,
} from 'naive-ui';
import { computed, defineComponent, KeepAlive, Transition } from 'vue';
import { RouterView, useRoute, useRouter } from 'vue-router';
import { preprocessFilePath, ThemeSwitcher } from '@hg-tech/oasis-common';

import { AppHeader } from './header';
import { useAside } from './hook';
import { AppMenus } from './menus';

import { useAppTheme, useUserBase } from '@/common/hooks';
import { themeOverrides } from '@/configs/theme-override';
import { DarkModeFilled, LightModeOutlined, LogOutOutline, MenuFoldOutlined, MenuUnfoldOutlined, PersonCircleOutline } from '@/common/components/svg-icons';
import { LOGIN_STATE } from '@/common/constants/login.constant';
import { renderIcon } from '@/common/utils/utils';
import { PageName } from '@/configs/page-config';
import { usePlatformConfig } from '@/common/hooks/platform-config.hook';

const AppLayout = defineComponent({
  setup() {
    const route = useRoute();
    const router = useRouter();
    const { collapsed, toggle } = useAside();
    const { switchTheme, currentTheme } = useAppTheme();
    const { userInfo, userLogout, loginState } = useUserBase();
    const { isFullScreen } = usePlatformConfig();

    const avatar = computed(() => {
      return preprocessFilePath(userInfo.value?.avatar, { origin: import.meta.env.VITE_USER_INFO_ORIGION, replaceOrigin: false });
    });

    const MenuOptions = computed(() => [
      {
        label: '用户资料',
        key: 'profile',
        icon: renderIcon(() => <PersonCircleOutline />),
      },
      {
        label: `${currentTheme.value === 'dark' ? 'Light' : 'Dark'}模式`,
        key: 'editTheme',
        icon: renderIcon(
          currentTheme.value === 'dark' ? <LightModeOutlined /> : <DarkModeFilled />,
        ),
      },
      {
        label: '退出登录',
        key: 'logout',
        icon: renderIcon(<LogOutOutline />),
      },
    ]);

    const onSelectedDropdownItem = (key: string) => {
      switch (key) {
        case 'editTheme':
          switchTheme();
          break;
        case 'profile':
          window.open(
            'https://tech.int.hypergryph.com/#/system/account/settings',
            '_self',
          );
          break;
        case 'logout':
          userLogout();
          break;
        default:
          break;
      }
    };

    const onLogin = () => {
      router.push({
        name: PageName.Login,
      });
    };

    const onHandleMenuFoldClick = () => {
      toggle(!collapsed.value);
    };

    return () => (
      <div class="hg-container h-full">
        {isFullScreen.value && (
          <ThemeSwitcher
            class="pos-absolute right-0 top-50% z-100 font-size-[20px] c-FO-Content-Components1"
            onToggle={switchTheme}
            placement="right"
            theme={currentTheme.value}
          />
        )}
        <NLayout class="h-full" has-sider>
          {!window.__MICRO_APP_ENVIRONMENT__ && !isFullScreen.value
            ? (
              <NConfigProvider
                themeOverrides={themeOverrides[currentTheme.value]}
              >
                <NGlobalStyle class="font-noto" />
                <NLayoutSider
                  bordered
                  collapse-mode="width"
                  collapsed={collapsed.value}
                  collapsed-width={60}
                  native-scrollbar={false}
                  onUpdate:collapsed={(col: boolean) => collapsed.value = col}
                  width={240}
                >
                  <div class="h-100vh flex flex-col justify-between">
                    <div>
                      <div class={`h-64px py-5px flex-c-start ${collapsed.value ? 'pl-12px' : 'pl-24px'}`}>
                        <div class="user-info mr-24px w-full flex-c-start">
                          {loginState.value === LOGIN_STATE.LOGGED
                            ? (
                              <>
                                <NDropdown
                                  onSelect={onSelectedDropdownItem}
                                  options={MenuOptions.value}
                                >
                                  <div class="flex-c-start">
                                    <NAvatar
                                      round
                                      size={40}
                                      src={
                                        avatar.value ?? 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg'
                                      }
                                    />
                                    <Transition enterActiveClass="a-fade-in" leaveActiveClass="a-fade-out">
                                      <NText class="ml-12px block whitespace-nowrap font-size-[14px] font-700" depth={1} v-show={!collapsed.value}>
                                        {`${userInfo.value?.nickName ?? '--'}${userInfo.value?.cName ? `（${userInfo.value?.cName}）` : ''}`}
                                      </NText>
                                    </Transition>

                                  </div>

                                </NDropdown>

                              </>
                            )
                            : (
                              <NButton class="w-full flex-1" onClick={onLogin}>
                                <NP>登录</NP>
                              </NButton>
                            )}
                        </div>
                      </div>
                      <AppMenus collapsed={collapsed.value} />
                    </div>

                    <div class="flex justify-end p-20px">
                      <NButton class="ml-20px font-size-[18px] c-#A0A2AF" onClick={onHandleMenuFoldClick} text>
                        <NIcon size={22}>
                          {collapsed.value
                            ? (
                              <MenuUnfoldOutlined />
                            )
                            : (
                              <MenuFoldOutlined />
                            )}
                        </NIcon>
                      </NButton>
                    </div>

                  </div>

                </NLayoutSider>
              </NConfigProvider>
            )
            : null}

          <NLayout>
            <AppHeader />
            <NLayoutContent
              class="pos-relative min-h-[280px]"
              style={{
                /**
                 * fixme：用于解决chrome渲染图片渲染未卸载问题
                 * @see：https://project.feishu.cn/test-techcenter/issue/detail/**********
                 */
                transform: 'translateZ(0)',
              }}
            >
              <RouterView
                // key={route.matched[1]?.name as string}
                v-slots={{
                  default: (ctx: { Component: ComponentInstance }) => {
                    const keepAlive = route.meta.keepAlive;
                    return (
                      <Transition
                        enterActiveClass="a-move-in-left"
                        mode="out-in"
                      >
                        {keepAlive
                          ? (
                            <KeepAlive>
                              <ctx.Component />
                            </KeepAlive>
                          )
                          : (
                            <ctx.Component />
                          )}
                      </Transition>
                    );
                  },
                }}
              />
            </NLayoutContent>
          </NLayout>
        </NLayout>
      </div>
    );
  },
});

export { AppLayout };
