import { shuffle, sortBy } from 'lodash-es';
import { computed, ref } from 'vue';
import type {
  P4ExamOperationItem,
  P4ExamProgressItem,
  P4ExamSelectionItem,
} from '/@/api/page/model/tcp4tModel';
import {
  backLastTimeP4ExamOperation,
  finishP4Exam,
  freezeP4ExamProgress,
  getP4ExamProgress,
  getP4ExamReplyOperationContent,
  getP4ExamReplyOperationCount,
  getP4ExamReplySelection,
  getP4ExamReplySelectionProofByID,
  updateP4ExamProgress,
  verifyP4ExamReplyOperationResult,
} from '/@/api/page/tcp4t';
import { useScrollTo } from '/@/hooks/event/useScrollTo';
import { useMessage } from '/@/hooks/web/useMessage';
import { sendEvent } from '/@/service/tracker';

const { createMessage } = useMessage();

export type ExamModalType = 'close' | 'freeze' | 'error' | 'back';

// 模态框类型
const modalType = ref<ExamModalType>();
// 模态框显隐
const modalShow = ref<boolean>(false);

// 考试进度信息
const examProgressInfo = ref<P4ExamProgressItem>();
// 当前页面索引
const curPageIndex = ref<number>(0);
// 题目总数
const questionTotal = ref<number>(0);
// 当前题目索引
const questionIndex = ref<number>(0);
// 错误次数
const errorCount = ref<number>(0);

export function useP4Exam() {
  // 初始化数据
  const initData = () => {
    curPageIndex.value = 0;
    questionTotal.value = 0;
    questionIndex.value = 0;
    errorCount.value = 0;
    modalType.value = 'close';
    modalShow.value = false;
  };

  // 获取考试进度信息
  const getExamProgress = async () => {
    const { data } = await getP4ExamProgress();
    examProgressInfo.value = data;
  };

  // 初始化考试进度信息
  const handleInitData = (curPage?: number) => {
    const data = examProgressInfo.value;
    curPageIndex.value = curPage || (data?.statusCode === 1 ? 2 : 0);
    if (data?.isFrozen) {
      modalType.value = 'freeze';
      modalShow.value = true;
    } else if (data?.statusCode === 1) {
      curPageIndex.value = 2;
      createMessage.success('已成功恢复考试进度');
    } else if (data?.statusCode === 2) {
      curPageIndex.value = 3;
      createMessage.success('已通过考试');
    }
  };

  const getExamCountMsg = computed(() => {
    return [1, 2].includes(curPageIndex.value)
      ? `(${questionIndex.value}/${questionTotal.value})`
      : '';
  });
  return {
    initData,
    examProgressInfo,
    curPageIndex,
    questionTotal,
    questionIndex,
    getExamProgress,
    modalType,
    modalShow,
    getExamCountMsg,
    handleInitData,
  };
}

export function useP4ExamSelection() {
  // 选择题列表
  const selectionList = ref<P4ExamSelectionItem[]>([]);

  // 获取选择题列表
  const getSelectionList = async () => {
    questionTotal.value = 0;
    questionIndex.value = 0;
    const { list } = await getP4ExamReplySelection();
    selectionList.value
      = sortBy(list, 'examOrder')?.map((e) => {
        e.options!.data = shuffle(e.options!.data);
        return e;
      }) || [];
    questionTotal.value = selectionList.value.length;
  };

  // 判断当前选择题是否正确
  const getSelectionProof = async (selectionID: number, answer: string) => {
    const { correct } = await getP4ExamReplySelectionProofByID(selectionID, answer);
    errorCount.value = correct ? errorCount.value : errorCount.value + 1;
    questionIndex.value = correct ? questionIndex.value + 1 : questionIndex.value;
    if (errorCount.value === 5) {
      sendEvent('p4_study_p4_exam_trigger_freeze');
      const { getExamProgress } = useP4Exam();
      await freezeP4ExamProgress();
      await getExamProgress();
      const { handleInitData } = useP4Exam();
      handleInitData(1);
    } else if (questionIndex.value === questionTotal.value) {
      await updateP4ExamProgress(1);
      questionIndex.value = 0;
      questionTotal.value = 0;
      curPageIndex.value = 2;
    }
    return correct;
  };
  return {
    getSelectionList,
    selectionList,
    getSelectionProof,
    errorCount,
  };
}

// 操作题输入框内容
const operationInput = ref('');
// 当前操作题内容
const curOperation = ref<P4ExamOperationItem>();
// 操作题按钮类型
const operationBtnType = ref<'' | 'loading' | 'modify'>('');
const scrollOperationRef = ref();
const examOrder = ref<number>();

export function useP4ExamOperations() {
  const isVerify = ref(false);
  const hint = ref('');
  const isInputHint = ref(true);
  const isLoading = ref(false);

  // 获取操作题数量
  const getOperationCount = async () => {
    const { count } = await getP4ExamReplyOperationCount();
    questionTotal.value = count || 0;
  };

  // 获取当前操作题内容
  const getCurOperation = async () => {
    operationInput.value = '';
    const { operation } = await getP4ExamReplyOperationContent(
      examProgressInfo.value!.lastSuccessOrder! + 1,
    );
    curOperation.value = operation;
    examOrder.value = operation.examOrder;
    questionIndex.value = examProgressInfo.value!.lastSuccessOrder!;
  };

  // 验证操作题结果
  const verifyOperation = async (inputValue?: string) => {
    let isPass = false;
    try {
      const { operation } = await verifyP4ExamReplyOperationResult(
        curOperation.value!.ID!,
        examOrder.value!,
        inputValue,
      );
      isPass = !!operation.passed;
      hint.value = operation.hint || '';
    } finally {
      let toValue: number | undefined;
      if (isPass) {
        operationBtnType.value = '';
        if (questionIndex.value === questionTotal.value - 1) {
          const res = await finishP4Exam();
          if (res?.data.statusCode === 2) {
            curPageIndex.value = 3;
            examProgressInfo.value = res.data;
          }
        } else {
          const { getExamProgress } = useP4Exam();
          await getExamProgress();
          await getCurOperation();
          toValue = 0;
        }
      } else {
        operationBtnType.value = 'modify';
        if (curOperation.value?.needsInput) {
          toValue = scrollOperationRef.value.scrollHeight;
        }
      }
      if (toValue || toValue === 0) {
        const { start } = useScrollTo({
          el: scrollOperationRef.value,
          to: toValue,
        });
        start();
      }
      operationInput.value = '';
    }
  };

  // 操作题按钮点击事件
  const handleOperationBtn = async () => {
    if (curOperation.value?.needsInput) {
      if (operationInput.value) {
        operationBtnType.value = 'loading';
        await verifyOperation(operationInput.value);
      } else {
        modalType.value = 'error';
        modalShow.value = true;
      }
    } else {
      operationBtnType.value = 'loading';
      await verifyOperation();
    }
  };

  // 初始化操作题
  const initCurOperation = async () => {
    try {
      isLoading.value = true;
      const res = await backLastTimeP4ExamOperation();
      if (res.madeRestore) {
        operationInput.value = '';
        operationBtnType.value = '';
        return res.madeRestore;
      }
    } finally {
      isLoading.value = false;
    }
  };

  return {
    curOperation,
    isVerify,
    hint,
    isInputHint,
    getOperationCount,
    getCurOperation,
    handleOperationBtn,
    operationInput,
    operationBtnType,
    scrollOperationRef,
    initCurOperation,
    isLoading,
  };
}
