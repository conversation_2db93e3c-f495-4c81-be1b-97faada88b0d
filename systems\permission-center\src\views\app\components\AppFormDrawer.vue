<template>
  <Drawer
    :open="show" :width="532" :closable="false" :maskClosable="false" placement="right"
    :bodyStyle="{ padding: '12px 8px 8px 8px' }" @afterOpenChange="o => !o && modalDestroy()"
    @close="() => modalCancel()"
  >
    <template #title>
      <span class="FO-Font-B18">{{ title }}</span>
    </template>
    <template #extra>
      <Button class="btn-fill-text" @click="() => modalCancel()">
        <template #icon>
          <CloseIcon />
        </template>
      </Button>
    </template>
    <div class="px-6 py-6 space-y-6">
      <Form :model="formValue" :labelCol="{ style: { width: '95px' } }">
        <FormItem label="应用图标" name="icon">
          <Upload
            v-model:fileList="iconFileList" :customRequest="handleIconUpload" :showUploadList="false"
            accept="image/*"
          >
            <div
              class="h-24 w-24 flex flex-col cursor-pointer items-center justify-center border-2 border-FO-Container-Stroke1 rounded-2xl border-dashed bg-FO-Container-Fill2 transition-colors hover:border-FO-Brand-Primary-Default"
              :class="{ 'pointer-events-none opacity-60': uploading }"
            >
              <template v-if="uploading">
                <LoadingOutlined class="FO-Font-R24 mb-2 c-FO-Brand-Primary-Default" />
                <span class="FO-Font-R14 c-FO-Brand-Primary-Default">上传中...</span>
              </template>
              <template v-else-if="formValue.icon">
                <img :src="formValue.icon" class="h-full w-full rounded-6 object-cover">
              </template>
              <template v-else>
                <PlusOutlined class="FO-Font-R24 mb-2 c-FO-Content-Text2" />
                <span class="FO-Font-R14 c-FO-Content-Text2">点击上传</span>
              </template>
            </div>
          </Upload>
        </FormItem>

        <FormItem label="应用名称" name="name" v-bind="validateInfos.name">
          <Input v-model:value="formValue.name" placeholder="请输入应用名称" :maxlength="30" showCount />
        </FormItem>

        <FormItem label="应用 Code" name="code" v-bind="validateInfos.code">
          <Input
            v-model:value="formValue.code" placeholder="请输入应用 Code"
            :disabled="props.appInfo != null"
          >
            <template #suffix>
              <span class="c-FO-Content-Text3">
                {{ formValue.code?.length || 0 }} / 30
              </span>
            </template>
          </Input>
        </FormItem>

        <FormItem label="应用类型" name="appType" v-bind="validateInfos.appType">
          <CardSelector v-model="formValue.isMultiTenant" :options="appTypeOptions" :disabled="isEdit" />
        </FormItem>

        <FormItem label="应用分类" name="category">
          <Select v-model:value="formValue.category" placeholder="请选择应用分类" :options="categoryListOptions" allowClear />
        </FormItem>

        <FormItem label="应用描述" name="description" v-bind="validateInfos.description">
          <Textarea
            v-model:value="formValue.description" placeholder="请输入应用描述" :rows="4" :maxlength="100"
            resize="vertical"
          />
        </FormItem>

        <FormItem label="应用管理员" name="admin" v-bind="validateInfos.admin">
          <Select
            v-model:value="(formValue.admin as string[])" placeholder="请选择管理员" mode="multiple"
            notFoundContent="无成员数据" :loading="loadingUserList" :options="userListOptions" allowClear
            :filterOption="false" showArrow @focus="resetUserList" @search="handleUserSearch"
          />
        </FormItem>
      </Form>
    </div>

    <template #footer>
      <div class="flex justify-end gap-12px px-8px py-12px">
        <Button class="btn-fill-default w-100px" @click="() => modalCancel()">
          取消
        </Button>
        <Button class="btn-fill-primary w-100px" :loading="submitting" @click="handleConfirm">
          保存
        </Button>
      </div>
    </template>
  </Drawer>
</template>

<script setup lang="tsx">
import { type ModalBaseProps, useLatestPromise } from '@hg-tech/utils-vue';
import { type PermissionAppForm, type PermissionAppInfo, getPermissionAppCategories } from '../../../api/app.ts';
import {
  Button,
  Drawer,
  Form,
  FormItem,
  Input,
  message,
  Select,
  Textarea,
  Upload,
} from 'ant-design-vue';
import { computed, ref, watch } from 'vue';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { debounce } from 'lodash';
import { useLoginUserInfoStore, useUserListOption } from '../../../composables/useUserInfo.ts';
import { uploadImageFile, validateImageFormat } from '../../../utils/upload.ts';
import CloseIcon from '../../../assets/icons/SystemStrokeClose.svg?component';
import CardSelector from '../../../components/CardSelector.vue';

const props = defineProps<ModalBaseProps<{ deleted: boolean }> & {
  title?: string;
  appInfo?: PermissionAppInfo;
  sentReq?: (formValue: PermissionAppForm) => Promise<boolean>;
}>();

const loginUserInfoStore = useLoginUserInfoStore();
const { data: categoryList, execute: fetchCategoryList, loading: _loadingCategoryList } = useLatestPromise(getPermissionAppCategories);

fetchCategoryList({}, {});
const categoryListOptions = computed(() => categoryList.value?.data?.data?.map((i) => ({ label: i, value: i })) || []);

const { userListOptions, loadingUserList, queryUser, resetUserList } = useUserListOption(
  computed(() => (props.appInfo?.member?.admin ?? (loginUserInfoStore.loginUserInfo ? [loginUserInfoStore.loginUserInfo!] : []))),
);

const isEdit = computed(() => !!props.appInfo);
// 表单数据
const formValue = ref<PermissionAppForm>({
  name: '',
  code: '',
  description: '',
  isMultiTenant: false,
  admin: [],
  icon: '',
  category: undefined,
});

// 图片上传相关
const iconFileList = ref([]);
const submitting = ref(false);
const uploading = ref(false);

// 应用类型选项
const appTypeOptions = ref([
  {
    value: false,
    title: '普通应用',
    description: '不区分项目的应用',
  },
  {
    value: true,
    title: '项目应用',
    description: '区分项目，支持管理员配置角色权限',
  },
]);

// 表单验证规则
const formRules = ref({
  name: [
    { required: true, message: '请输入应用名称' },
    { max: 30, message: '最多30个字' },
  ],
  code: [
    { required: true, message: '请输入应用 code' },
    { pattern: /^\w+$/, message: '只能输入字母、数字、下划线' },
    { max: 30, message: '最多30个字' },
  ],
  description: [
    { max: 200, message: '最多200个字' },
  ],
  appType: [
    { required: true, message: '请选择应用类型' },
  ],
  admin: [
    { required: true, type: 'array', message: '请选择应用管理员' },
  ],
});

// 使用 Form.useForm
const { validate, validateInfos } = Form.useForm(formValue, formRules);

// 用户搜索防抖处理
const handleUserSearch = debounce((v: string) => {
  if (v) {
    queryUser({ query: v }, {});
  } else {
    resetUserList();
  }
}, 300);

// 图片上传处理
async function handleIconUpload(options: any) {
  const { file, onSuccess, onError } = options;

  // 文件大小验证（10MB限制）
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    message.error('文件大小需小于10M');
    onError?.(new Error('文件大小超限'));
    return;
  }

  // 文件格式验证
  const formatValidation = validateImageFormat(file);
  if (!formatValidation.isValid) {
    message.error(formatValidation.errorMessage || '请上传图片格式的文件');
    onError?.(new Error('文件格式不支持'));
    return;
  }

  try {
    uploading.value = true;

    // 调用上传接口
    const result = await uploadImageFile(file);

    if (result.success && result.url) {
      formValue.value.icon = result.url;
      message.success('图片上传成功');
      onSuccess?.(result);
    } else {
      message.error(result.errorMessage || '上传失败，请重试');
      onError?.(new Error(result.errorMessage || '上传失败'));
    }
  } catch (error: any) {
    console.error('图片上传失败:', error);
    message.error('上传失败，请重试');
    onError?.(error);
  } finally {
    uploading.value = false;
  }
}

// 初始化表单数据
function initFormData() {
  const appInfo = props.appInfo;
  formValue.value = {
    name: appInfo?.name || '',
    code: appInfo?.code || '',
    description: appInfo?.description || '',
    isMultiTenant: Boolean(appInfo?.isMultiTenant),
    admin: appInfo?.member?.admin?.length
      ? appInfo.member.admin.map((i) => i.hgId)
      : loginUserInfoStore.loginUserInfo?.hgId ? [loginUserInfoStore.loginUserInfo.hgId] : [],
    icon: appInfo?.icon || '',
    category: appInfo?.category || undefined,
  };
}

// 监听 appInfo 变化
watch(() => props.appInfo, () => {
  initFormData();
}, { immediate: true });

// 监听登录用户信息变化
watch(() => loginUserInfoStore.loginUserInfo?.hgId, async (hgId) => {
  if (!props.appInfo?.member?.admin?.length && hgId) {
    formValue.value.admin = [hgId];
  }
}, { immediate: true });

// 表单提交
async function handleConfirm() {
  try {
    submitting.value = true;
    await validate();
    const isSuccess = await props.sentReq?.({ ...formValue.value, category: formValue.value.category || '' });
    if (isSuccess) {
      await props.modalConfirm({ deleted: false });
    }
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    submitting.value = false;
  }
}
</script>
