import { createRequestService, getDefaultErrorMsg } from '@hg-tech/oasis-common';
import { store } from '../store/pinia.ts';
import { useUserAuthStore } from '../store/modules/userAuth.ts';
import { useRouteNavigationStore } from '../store/modules/routeNavigation.ts';
import { message } from 'ant-design-vue';

const authStore = useUserAuthStore(store);
const routeNavigationStore = useRouteNavigationStore(store);

export const requestService = createRequestService(
  {
    authTokens: [
      {
        accessTokenKey: 'Access-Token',
        getAccessToken: () => authStore.userAuthInfo?.privateToken,
      },
      {
        accessTokenKey: 'X-Token',
        getAccessToken: () => authStore.userAuthInfo?.accessToken,
        newTokenKey: 'new-token',
        setNewToken: authStore.userAuthInfo?.setAccessToken,
      },
    ],
    onUnauthorized() {
      routeNavigationStore.onUnauthorized();
    },
    onForbidden(res) {
      // 后端未符合error code 规范，先注释掉
      // routeNavigationStore.onForbidden();
      message.error(getDefaultErrorMsg(res.data, '暂无权限访问！'));
    },
  },
  {
    baseURL: `${import.meta.env.VITE_BASE_API_ORIGIN}/api`,
  },
);
