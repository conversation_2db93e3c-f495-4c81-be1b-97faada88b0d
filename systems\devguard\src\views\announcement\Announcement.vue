<template>
  <div>
    <Button
      class="custom-rounded-btn flex items-center gap-4px"
      @click="openAnnouncementConfigModal()"
    >
      <Icon :icon="broadcastIcon" />
      公告配置
    </Button>
    <AnnouncementConfigModalHolder />
  </div>
</template>

<script lang="ts" setup>
import { Icon } from '@iconify/vue';
import { Button } from 'ant-design-vue';
import broadcastIcon from '@iconify-icons/icon-park-outline/broadcast';
import AnnouncementConfigModal from './AnnouncementConfigModal.vue';
import { useModalShow } from '@hg-tech/utils-vue';

const [AnnouncementConfigModalHolder, showAnnouncementConfigModal] = useModalShow(AnnouncementConfigModal);
function openAnnouncementConfigModal() {
  showAnnouncementConfigModal({});
}
</script>

<style lang="less" setup>

</style>
