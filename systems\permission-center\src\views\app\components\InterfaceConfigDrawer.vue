<template>
  <Drawer
    :open="show"
    :width="924"
    :closable="false"
    :maskClosable="false"
    placement="right"
    :bodyStyle="{ padding: '0' }"
    @afterOpenChange="o => !o && modalDestroy()"
    @close="() => modalCancel()"
  >
    <template #title>
      <span class="FO-Font-B18">接口配置</span>
    </template>
    <template #extra>
      <Button class="btn-fill-text" @click="() => modalCancel()">
        <template #icon>
          <CloseIcon />
        </template>
      </Button>
    </template>

    <div class="h-full flex flex-col">
      <div class="flex-none px-24px pt-24px">
        <div class="mb-12px flex items-center justify-between">
          <Button
            :class="isTopSelectedEnabled ? 'btn-fill-secondary' : 'btn-fill-default'"
            @click="handleTopSelected"
          >
            <template #icon>
              <VerticalAlignTopOutlined />
            </template>
            置顶已选项目
          </Button>
          <div class="w-200px">
            <Input
              v-model:value="searchKeyword"
              allowClear
              placeholder="搜索接口"
            >
              <template #prefix>
                <SearchIcon class="c-FO-Content-Icon3" />
              </template>
            </Input>
          </div>
        </div>
      </div>
      <div class="flex-1 overflow-hidden px-24px">
        <BasicVxeTable ref="tableRef" class="interface-config-table" :options="gridOptions" />
      </div>
    </div>
    <template #footer>
      <div class="flex justify-end gap-12px px-8px py-12px">
        <Button class="btn-fill-default w-100px" @click="() => modalCancel()">
          取消
        </Button>
        <Button
          class="btn-fill-primary w-100px"
          :loading="submitting"
          @click="handleSave"
        >
          保存
        </Button>
      </div>
    </template>
  </Drawer>
</template>

<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { Button, Drawer, Input, message } from 'ant-design-vue';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import { toHighlightSegments } from '@hg-tech/utils';
import { match } from 'pinyin-pro';
import { useHighlight } from '../../../composables/useHighlight';
import { refDebounced } from '@vueuse/core';
import { isNil, map, partition } from 'lodash';
import type { VxeGridInstance, VxeGridProps } from 'vxe-table';
import { BasicVxeTable } from '@hg-tech/oasis-common';
import CloseIcon from '../../../assets/icons/SystemStrokeClose.svg?component';
import SearchIcon from '../../../assets/icons/SystemStrokeSearch.svg?component';
import { VerticalAlignTopOutlined } from '@ant-design/icons-vue';
import type {
  PermissionCheckPointApi,
} from '../../../api/manage.ts';
import ApiMethodTag from '../../manage/components/ApiMethodTag.vue';

const props = defineProps<ModalBaseProps & {
  apiList?: PermissionCheckPointApi[];
  selectedApiIds?: number[];
  sentReq?: (selectedApiIds: number[]) => Promise<boolean>;
}>();

// 保存状态
const submitting = ref(false);

// 表格实例引用
const tableRef = ref<VxeGridInstance>();

// 搜索功能
const searchKeyword = ref('');
const debouncedSearchKeyword = refDebounced(searchKeyword, 200);

// 置顶开关状态
const isTopSelectedEnabled = ref(false);

// 当前选中的项目ID集合（用于置顶功能）
const selectedIds = ref<Set<number>>(new Set());

interface RenderRow {
  id: number;
  name: string;
  method: string;
  path: string;
  item: PermissionCheckPointApi;
}

// 过滤后的接口列表（支持置顶已选项目）
const filteredApiList = computed(() => {
  let filtered = [...(props.apiList || [])];

  // 搜索过滤
  if (debouncedSearchKeyword.value) {
    const keyword = debouncedSearchKeyword.value.toLowerCase();
    filtered = filtered.filter((api) =>
      api.name?.toLowerCase().includes(keyword)
      || api.path?.toLowerCase().includes(keyword)
      || match(api.name || '', keyword)?.length, // 匹配拼音
    );
  }

  // 置顶已选项目（仅当开关开启时）
  if (isTopSelectedEnabled.value) {
    const [selected, unselected] = partition(filtered, (api) => api.id && selectedIds.value.has(api.id));
    return [...selected, ...unselected];
  }

  return filtered;
});

const tableRows = computed<RenderRow[]>(() => {
  return filteredApiList.value
    .filter((api) => !isNil(api.id))
    .map((api) => ({
      id: api.id as number,
      name: api.name || '',
      method: api.method || '',
      path: api.path || '',
      item: api,
    }));
});

// 初始化选中状态
watch(() => props.selectedApiIds, (selectedApiIds) => {
  selectedIds.value = new Set(selectedApiIds || []);
}, { immediate: true });

// 监听表格选中状态变化，更新selectedIds
function handleCheckboxChange() {
  if (tableRef.value) {
    const checkedRecords = tableRef.value.getCheckboxRecords() as RenderRow[];
    selectedIds.value = new Set(checkedRecords.map((record) => record.id));
  }
}

const { getHighlightSegments } = useHighlight();

const tableColumns = computed<VxeGridProps<RenderRow>['columns']>(() => [
  { type: 'checkbox', width: 60 },
  {
    field: 'name',
    title: '接口名称',
    minWidth: 250,
    slots: {
      default({ row }) {
        return debouncedSearchKeyword.value
          ? getHighlightSegments(row.name, debouncedSearchKeyword.value).map((i: any) => <span class={`FO-Font-R14 ${i.highlight ? 'c-FO-Brand-Primary-Default' : ''}`}>{i.text}</span>)
          : <span class="FO-Font-R14">{row.name}</span>;
      },
    },
  },
  {
    field: 'method',
    title: '请求方法',
    width: 100,
    slots: {
      default({ row }) {
        return <ApiMethodTag method={row.method} />;
      },
    },
  },
  {
    field: 'path',
    title: 'API路径',
    minWidth: 250,
    slots: {
      default({ row }) {
        return debouncedSearchKeyword.value
          ? toHighlightSegments(row.path, debouncedSearchKeyword.value).map((i: any) => <span class={`FO-Font-R14 ${i.highlight ? 'c-FO-Brand-Primary-Default' : ''}`}>{i.text}</span>)
          : <span class="FO-Font-R14">{row.path}</span>;
      },
    },
  },
] as VxeGridProps<RenderRow>['columns']);

const gridOptions = computed(() => ({
  rowConfig: {
    keyField: 'id',
    isHover: true,
  },
  height: 'auto',
  columns: tableColumns.value,
  data: tableRows.value,
  checkboxConfig: {
    trigger: 'row',
    checkRowKeys: props.selectedApiIds ?? [],
  },
  columnConfig: {
    resizable: false,
  },
  onCheckboxChange: handleCheckboxChange,
  onCheckboxAll: handleCheckboxChange,
}) as VxeGridProps<RenderRow>);

// 切换置顶状态
function handleTopSelected() {
  isTopSelectedEnabled.value = !isTopSelectedEnabled.value;
}

// 保存配置
async function handleSave() {
  if (!tableRef.value) {
    message.error('表格未初始化');
    return;
  }

  try {
    submitting.value = true;

    // 从 vxe-table 获取当前勾选的行
    const checkedRecords = tableRef.value.getCheckboxRecords() as RenderRow[];
    const selectedApiIds = map(checkedRecords, 'id');

    // 使用sentReq处理保存逻辑
    if (props.sentReq) {
      const success = await props.sentReq(selectedApiIds);
      if (success) {
        // 保存成功后关闭drawer
        props.modalConfirm();
      }
    }
  } catch (error) {
    console.error('保存失败:', error);
  } finally {
    submitting.value = false;
  }
}
</script>

<style lang="less" scoped>
.interface-config-table :deep(.vxe-cell--checkbox) {
  font-size: 14px;
}
</style>
