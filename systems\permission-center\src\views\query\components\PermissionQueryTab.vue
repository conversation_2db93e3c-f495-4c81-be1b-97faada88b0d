<template>
  <div class="h-full flex flex-col rounded-xl bg-FO-Container-Fill1 p-24px">
    <!-- 查询表单区域 -->
    <div class="flex-none">
      <PermissionQueryForm
        :initialAppId="initialAppId"
        :initialTenantId="initialTenantId"
        @query="handleQuery"
      />
    </div>

    <!-- 结果展示区域 -->
    <div class="flex-auto">
      <PermissionResultTable
        :data="queryResult"
        :loading="queryLoading"
        :queryParams="lastQueryParams"
        :categories="queryCategories"
        :isMultiTenant="isMultiTenant"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import { useLatestPromise } from '@hg-tech/utils-vue';
import PermissionQueryForm from './PermissionQueryForm.vue';
import PermissionResultTable from './PermissionResultTable.vue';
import type { GetPermissionMembersRequest } from '../../../api/query';
import type {
  PermissionCategory,
  QueryPermissionRow,
} from '../permission';
import { batchGetGroupByPermission } from '../../../api/query';
import { usePermissionQuery } from '../../../composables/usePermissionQuery';
import { usePermissionCategories } from '../../../composables/usePermissionCategories';

defineProps<{
  initialAppId?: number;
  initialTenantId?: number;
}>();

// 使用权限成员查询composable
const { transformMembersToTableData } = usePermissionQuery();

// 使用权限分类composable
const { refreshData: refreshPermissionData } = usePermissionCategories();

const {
  loading: queryLoading,
  execute: executeBatchQuery,
} = useLatestPromise(batchGetGroupByPermission);

// 查询结果状态
const queryResult = ref<QueryPermissionRow[]>([]);
const queryCategories = ref<PermissionCategory[]>([]);
const lastQueryParams = ref<GetPermissionMembersRequest>();
const isMultiTenant = ref(false);

// 处理查询
async function handleQuery(params: GetPermissionMembersRequest) {
  if (queryLoading.value) {
    return;
  }

  lastQueryParams.value = params;

  try {
    if (params.appId) {
      await refreshPermissionData(params.appId);
    }

    const response = await executeBatchQuery({}, params);

    if (response?.data?.code === 0 && response.data.data) {
      // 检查是否为多租户应用
      isMultiTenant.value = !!(params.tenantIds && params.tenantIds.length > 0);

      const { tableData, categories } = transformMembersToTableData(
        response.data.data,
        isMultiTenant.value,
      );

      queryResult.value = tableData;
      queryCategories.value = categories;

      if (tableData.length === 0) {
        message.info('未查询到相关权限信息');
      }
    } else {
      message.error(response?.data?.message || '查询失败');
      queryResult.value = [];
      queryCategories.value = [];
    }
  } catch (error) {
    console.error('权限查询失败:', error);
    queryResult.value = [];
    queryCategories.value = [];
  }
}
</script>
