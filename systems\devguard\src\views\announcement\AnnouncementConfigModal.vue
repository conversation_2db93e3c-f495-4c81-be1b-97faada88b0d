<template>
  <Modal
    :width="700"
    :open="show"
    :maskClosable="false"
    :footer="null"
    destroyOnClose
    centered
    :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="text-center">
        <span class="font-size-[16px] font-bold">
          <span>提交公告配置</span>
        </span>
      </div>
    </template>
    <div v-if="!isEdit">
      <div class="relative">
        <Tabs v-model:activeKey="announcementType">
          <TabPane key="new" tab="新公告">
            <div class="h-400px overflow-y-auto">
              <div v-if="mocKData.length">
                <AnnouncementItem v-for="item in mocKData" :key="item.ID" :item="item" :streamsList="streamsList" announcementType="new" @editAnnouncement="editAnnouncement" />
              </div>
              <div v-else class="h-full flex items-center justify-center c-FO-Container-Fill6">
                暂无新公告
              </div>
            </div>
          </TabPane>
          <TabPane key="history" tab="历史公告">
            <div class="h-400px overflow-y-auto">
              <div v-if="mocKData.length">
                <AnnouncementItem v-for="item in mocKData" :key="item.ID" :item="item" :streamsList="streamsList" announcementType="history" />
              </div>
              <div v-else class="h-full flex items-center justify-center c-FO-Container-Fill6">
                暂无新公告
              </div>
            </div>
          </TabPane>
        </Tabs>
        <div class="absolute right-0 top-12px">
          <Button shape="round" size="small" @click="isEdit = true">
            <div class="flex items-center gap-4px">
              <Icon :icon="addIcon" />
              <span>新增公告</span>
            </div>
          </Button>
        </div>
      </div>
    </div>
    <div v-else>
      <Button shape="round" size="small" @click="handleBack">
        <div class="flex items-center gap-4px">
          <Icon :icon="LeftIcon" />
          <span>返回</span>
        </div>
      </Button>
      <EditAnnouncement ref="editAnnouncementRef" class="h-300px" :streamsList="streamsList" />
      <div class="flex justify-center">
        <Button type="primary" @click="handleSave">
          保存
        </Button>
        <Button class="ml-2 bg-FO-Functional-Error1-Default!" type="primary" @click="handleBack">
          取消
        </Button>
      </div>
    </div>
  </Modal>
</template>

<script lang="ts" setup>
import { Button, Modal, TabPane, Tabs } from 'ant-design-vue';
import { type ModalBaseProps, useLatestPromise } from '@hg-tech/utils-vue';
import AnnouncementItem from './AnnouncementItem.vue';
import { Icon } from '@iconify/vue';
import LeftIcon from '@iconify-icons/icon-park-outline/left';
import addIcon from '@iconify-icons/icon-park-outline/plus';
import type { NullableBasicResult } from '/@/api/model/baseModel';
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import EditAnnouncement from './EditAnnouncement.vue';
import { type AnnouncementsItem, type StreamsListItem, addAnnouncementsHistoryListApi, getAnnouncementsHistoryListApi, getAnnouncementsListApi, getListSubmit } from '../../api';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig';
import { store } from '../../store/pinia';

defineProps< ModalBaseProps<{ updatedItem?: NullableBasicResult }> & {

}>();
const forgeonConfig = useForgeonConfigStore(store);
const { execute: getAnnouncementsListExecute, data: announcementsList } = useLatestPromise(getAnnouncementsListApi);
const { execute: getAnnouncementsHistoryListExecute, data: announcementsHistoryList } = useLatestPromise(getAnnouncementsHistoryListApi);
const { execute: addAnnouncementsHistoryListExecute } = useLatestPromise(addAnnouncementsHistoryListApi);

const { execute: getListSubmitExecute, data: submitList } = useLatestPromise(getListSubmit);
const currentProjectId = computed(() => forgeonConfig.currentProjectId);
const projectCode = computed(() => forgeonConfig.currentProjectInfo?.alias);
const announcementType = ref('new');
const streamsList = ref< StreamsListItem[]>([]);
const isEdit = ref(false);
const editAnnouncementRef = ref();

async function handleBack() {
  isEdit.value = false;
}
const mocKData = [
  {
    ID: 1,
    content: '公告内容',
    triggerType: 2, // 1 单次；2 周期
    startTimestamp: 1756967604414, // 单次的开始时间戳
    endTimestamp: 1756967683133, // 单次的结束时间
    cycleWeekdays: [false, true, false, true, true, true, true], // 长度为 7 的 bool 数组，表示周一到周日
    cycleStartTime: 1756967683133, // 固定格式的字符串
    cycleDuration: 22, // 暂定按照分钟
    streamIDs: [240, 260], // 适用分支的 id，是提交中心这边的分支 id
    modifier: {
      ID: 773,
      CreatedAt: '2025-04-27T11:20:05.359+08:00',
      UpdatedAt: '2025-04-27T11:26:24.565+08:00',
      uuid: 'cd7aa65c-ca3c-4a63-b17c-12dd584372ee',
      userName: 'wanghongyi',
      nickName: '王弘毅(mikyi)',
      sideMode: 'dark',
      headerImg: 'https://qmplusimg.henrongyi.top/gva_header.jpg',
      baseColor: '#fff',
      activeColor: '#1890ff',
      authorityId: 888,
      authority: {
        CreatedAt: '0001-01-01T00:00:00Z',
        UpdatedAt: '0001-01-01T00:00:00Z',
        DeletedAt: null,
        authorityId: 0,
        authorityName: '',
        parentId: 0,
        dataAuthorityId: null,
        children: null,
        menus: null,
        defaultRouter: '',
      },
      authorities: null,
      phone: '',
      email: '<EMAIL>',
      enable: 1,
      authType: 1,
      openID: '',
    },
    inEffect: true, // 当前是否生效
  },
];
async function handleSave() {
  const params = await editAnnouncementRef.value.getParams();
  await addAnnouncementsHistoryListExecute({ id: currentProjectId.value! }, params);
  handleBack();
  getData();
}
function editAnnouncement(item: AnnouncementsItem) {
  isEdit.value = true;
  nextTick(() => {
    editAnnouncementRef.value.setParams(item);
  });
}
function getData() {
  if (announcementType.value === 'new') {
    getAnnouncementsListExecute({ id: currentProjectId.value! }, {});
  } else {
    getAnnouncementsHistoryListExecute({ id: currentProjectId.value! }, {});
  }
}
onMounted(async () => {
  await getListSubmitExecute({ projectCode: projectCode.value!, id: currentProjectId.value! }, {});
  streamsList.value = submitList.value?.data?.data?.list.map((item) => item.streams).flat() || [];
});
watch(() => announcementType.value, () => {
  getData();
}, {
  immediate: true,
  deep: true,
});
</script>
