<template>
  <BasicModal
    :wrapClassName="prefixCls"
    :footer="null"
    :width="600"
    :closable="false"
    :maskClosable="false"
    :afterClose="handleAfterClose"
    :keyboard="false"
    @register="registerModal"
  >
    <template #title>
      <div class="pb-4 text-center text-2xl c-FO-Content-Components1">
        {{ getTitle }}
      </div>
    </template>
    <!-- eslint-disable-next-line vue/no-v-html -->
    <div class="px-60px py-50px text-xl" v-html="getContent" />

    <div class="text-center">
      <a-button
        v-if="!['error', 'back'].includes(modalType!)"
        shape="round"
        class="!border-none !bg-#424242 !px-18px !py-4px !c-FO-Content-Components1"
        @click="handleExit"
      >
        {{ modalType === 'close' ? '确认退出' : '确认' }}
      </a-button>
      <a-button
        shape="round"
        class="!border-none !bg-#424242 !px-18px !py-4px !c-FO-Content-Components1"
        :class="{ 'ml-8': !['error', 'back'].includes(modalType!) }"
        @click="handleClose"
      >
        {{ modalType === 'close' ? '返回考试' : modalType === 'freeze' ? '前往培训课程' : '确认' }}
      </a-button>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup name="TCP4TCommonModal">
import { useTimeoutFn } from '@vueuse/shared';
import { computed } from 'vue';
import { useP4Exam } from '../hook';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useDesign } from '/@/hooks/web/useDesign';
import { useGo } from '/@/hooks/web/usePage';
import { useTabs } from '/@/hooks/web/useTabs';
import { sendEvent } from '/@/service/tracker';

const emit = defineEmits(['register', 'exit']);
const { prefixCls } = useDesign('tcp4t-common-modal');
const { refreshPage } = useTabs();

const {
  questionIndex,
  questionTotal,
  modalType,
  modalShow,
  curPageIndex,
  examProgressInfo,
  getExamProgress,
} = useP4Exam();
const [registerModal, { closeModal }] = useModalInner();
const go = useGo();
const getTitle = computed(() => {
  switch (modalType.value) {
    case 'freeze':
      return '考试冻结';
    case 'error':
      return '输入内容';
    case 'back':
      return '您的workspace状态已复原';
    default:
      return '退出考试';
  }
});

const getContent = computed(() => {
  switch (modalType.value) {
    case 'freeze':
      return (
        `您已答错5次，考试被冻结2分钟；您可以前往学习培训课程, ${examProgressInfo.value?.unfreezeTimer}后再进行尝试。${
          curPageIndex.value === 1
            ? `<br/><br/>您已答对${questionIndex.value}道基础知识测试题，还剩${
              questionTotal.value - questionIndex.value
            }道。`
            : ''}`
      );
    case 'error':
      return '题目要求在题干最下方的输入框输入指定内容，请认真阅读题干，填写所需内容后再点击验证。';
    case 'back':
      return '您的workspace状态已复原，请在本地更新workspace，并清空本地changelist';
    default:
      return '下次进入考试时，如果之前已完成基础知识测试，则会直接进入未完成的操作测试题。';
  }
});

async function handleExit() {
  if (modalType.value === 'freeze') {
    await getExamProgress();
    if (!examProgressInfo.value?.isFrozen) {
      refreshPage();
      return;
    }
  }
  modalShow.value = false;
  emit('exit');
}

async function handleClose() {
  if (modalType.value === 'freeze') {
    sendEvent('p4_study_p4_exam_freeze_goto_training');
  }
  modalShow.value = false;
  closeModal();
}

async function handleAfterClose() {
  if (modalType.value === 'freeze') {
    go({ name: 'P4Trains', query: { fs: 0 } });
    return;
  }
  modalShow.value = false;
  useTimeoutFn(() => {
    modalType.value = 'close';
  }, 200);
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-tcp4t-common-modal';
.@{prefix-cls} {
  & .ant-modal-header {
    background-color: #424242;
    color: #fff;
  }
}

html[data-theme='dark'] .@{prefix-cls} {
  & .ant-modal-header {
    background-color: #151515;
  }
}
</style>
