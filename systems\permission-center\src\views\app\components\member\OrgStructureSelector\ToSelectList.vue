<template>
  <div class="flex flex-col overflow-hidden">
    <PermissionSolidTabs
      v-model:value="currentTab"
      class="mb-12px mr-16px flex-none"
      :tabList="validTabs"
    />
    <Breadcrumb v-if="selectedTreeSchema && treeLevels.length && !searchKw" separator=">" class="FO-Font-R12 mb-8px py-3px">
      <BreadcrumbItem v-for=" i in treeLevels " :key="i.key">
        <a class="!bg-transparent !c-FO-Content-Text2" @click="() => handleBackNode(i)">
          <RenderMaybeVNode
            v-if="selectedTreeSchema?.breadcrumbRender"
            :nodes="selectedTreeSchema.breadcrumbRender(i)"
          />
        </a>
      </BreadcrumbItem>
    </Breadcrumb>
    <div ref="cellContainerRef" class="flex-auto overflow-auto">
      <Spin :spinning="isLoading">
        <Empty v-if="!currentList.length" class="mt-40px" />
        <div
          class="relative w-full pr-16px"
          :style="{ height: `${totalSize}px` }"
        >
          <div
            v-for="virtualItem in virtualItems"
            :key="currentList[virtualItem.index]?.key"
            class="absolute left-0 top-0 w-full pb-4px"
            :style="{
              height: `${virtualItem.size}px`,
              transform: `translateY(${virtualItem.start}px)`,
            }"
          >
            <OrgStructureSelectorCell
              :data="currentList[virtualItem.index]"
              :data-index="virtualItem.index"
              class="h-full transition-300"
              :class="[
                { '!bg-FO-Brand-Tertiary-Active': isItemSelected(currentList[virtualItem.index]) },
                isItemDisabled(currentList[virtualItem.index]) ? 'cursor-not-allowed' : 'cursor-pointer hover:bg-FO-Container-Fill2',
              ]"
              @click="() => handleSelected(currentList[virtualItem.index])"
            >
              <template #prefix>
                <Checkbox
                  :checked="isItemSelected(currentList[virtualItem.index]) || (props.showCheckedWhenDisabled && isItemDisabled(currentList[virtualItem.index])) || false"
                  :disabled="isItemDisabled(currentList[virtualItem.index])"
                  :class="{
                    'disabled-checked': props.showCheckedWhenDisabled && isItemDisabled(currentList[virtualItem.index]),
                  }"
                />
              </template>
              <template v-if="selectedTreeSchema && !searchKw && currentList[virtualItem.index]?.type && selectedTreeSchema.treeTypeList?.includes(currentList[virtualItem.index].type!)" #suffix>
                <Button class="!px-8px" :class="isItemSelected(currentList[virtualItem.index]) ? 'btn-fill-secondary' : 'btn-fill-text'" @click.stop="() => handleEnterNode(currentList[virtualItem.index])">
                  <template #icon>
                    <FillArrowDown class="text-16px c-FO-Content-Icon3 -rotate-90" />
                  </template>
                </Button>
              </template>
            </OrgStructureSelectorCell>
          </div>
        </div>
      </Spin>
    </div>
  </div>
</template>

<script setup lang="ts" generic="K extends string, T extends OrgStructureCellInfo<K>">
import { computed, ref, shallowRef, watch } from 'vue';
import { RenderMaybeVNode } from '@hg-tech/oasis-common';
import { Breadcrumb, BreadcrumbItem, Button, Checkbox, Empty, Spin } from 'ant-design-vue';
import OrgStructureSelectorCell from './OrgStructureSelectorCell.vue';
import type { OrgStructureCellInfo, OrgStructurePayload, OrgStructureSchema, OrgStructureSchemaTree } from './type.ts';
import PermissionSolidTabs from '../../../../../components/PermissionSolidTabs.vue';
import { isEqual, last } from 'lodash';
import { useVirtualInfiniteScroll } from '../../../../../composables/useVirtualInfiniteScroll.ts';
import FillArrowDown from '../../../../../assets/icons/SystemStrokeArrowDown.svg?component';
import type { PagedQueryParam } from 'src/api/_common.ts';

const props = defineProps<{
  searchKw?: string;
  schemas?: OrgStructureSchema<K, T>[];
  disabledKeys?: OrgStructureCellInfo<K>['key'][];
  /** 禁用时显示为已勾选 */
  showCheckedWhenDisabled?: boolean;
}>();

const DEFAULT_TREE_LEVELS = [{ title: '鹰角' } as T];

const selectedPayloads = defineModel<OrgStructurePayload<K, T>[]>('selectedPayloads', { required: true });
const currentTab = ref<string>();
const validTabs = computed(() => {
  const supportedSchema = props.searchKw
    ? props.schemas?.filter((schema) => schema.searchFunc)
    : props.schemas?.filter((schema) => schema.initFunc != null && schema.searchFunc);

  return (supportedSchema || []).map((schema) => ({
    key: schema.type,
    label: schema.title,
  }));
});
watch(validTabs, (tabs, prvTabs) => {
  if (tabs.length > 0 && !isEqual(tabs, prvTabs)) {
    // 当 tab 变化时，重置 currentTab
    currentTab.value = tabs[0].key;
  }
}, { flush: 'sync', immediate: true });
const selectedSchema = computed(() => props.schemas?.find((schema) => schema.type === currentTab.value));
const selectedTreeSchema = computed<OrgStructureSchemaTree<K, T> | undefined>(() => {
  return (selectedSchema.value as unknown as OrgStructureSchemaTree<K, T>)?.treeTypeList?.length ? selectedSchema.value as OrgStructureSchemaTree<K, T> : undefined;
});
const treeLevels = shallowRef<T[]>(selectedTreeSchema.value?.defaultTreeLevels || DEFAULT_TREE_LEVELS);

const cellContainerRef = shallowRef<HTMLDivElement | null>(null);

const {
  currentList,
  isLoading,
  virtualItems,
  totalSize,
  reload,
} = useVirtualInfiniteScroll<T>(
  cellContainerRef,
  {
    req: () => {
      if (props.searchKw) {
        return async (pageInfo: PagedQueryParam) => selectedSchema.value?.searchFunc(pageInfo, props.searchKw);
      }
      return async (pageInfo: PagedQueryParam) => selectedSchema.value?.initFunc?.(pageInfo, last(treeLevels.value)?.key);
    },
    getKey: (i: T) => i.key,
    estimateSize: () => 58, // 54px 高度 + 4px 间距
    overscan: 30,
  },
);

watch(selectedSchema, (schema, prvSchema) => {
  if (!isEqual(schema, prvSchema)) {
    // 检测分类切换，重置树形结构
    treeLevels.value = selectedTreeSchema.value?.defaultTreeLevels || DEFAULT_TREE_LEVELS;
  }
});

watch([selectedSchema, treeLevels], () => {
  reload();
});

// 缓存选中状态，避免重复计算
const selectedKeysMap = computed(() => {
  const map = new Map<string, boolean>();
  selectedPayloads.value.forEach((p) => {
    map.set(`${p.data.key}-${p.data.type}`, true);
  });
  return map;
});

// 检查项目是否被选中
function isItemSelected(item: T): boolean {
  return selectedKeysMap.value.has(`${item.key}-${item.type}`);
}

// 检查项目是否被禁用
function isItemDisabled(item: T): boolean {
  return props.disabledKeys?.includes(item.key) || false;
}

// 切换 tab 时滚动到顶部
watch(currentTab, () => {
  cellContainerRef.value?.scrollTo(0, 0);
});

// 树形导航变化时滚动到顶部
watch(treeLevels, () => {
  cellContainerRef.value?.scrollTo(0, 0);
}, { flush: 'post' });

function handleSelected(item: T) {
  // 如果项目被禁用，则不允许选择
  if (isItemDisabled(item)) {
    return;
  }

  const selected = selectedPayloads.value.find((i) => i.data.key === item.key && i.data.type === item.type);
  if (selected) {
    // 已选中，取消选中
    selectedPayloads.value = selectedPayloads.value.filter((i) => !(i.data.key === item.key && i.data.type === item.type));
  } else {
    // 未选中，添加到已选列表
    selectedPayloads.value = [
      ...selectedPayloads.value,
      {
        key: item?.type,
        data: item,
      } as OrgStructurePayload<K, T>,
    ];
  }
}

function handleBackNode(i: T) {
  const toIdx = treeLevels.value.findIndex((j) => j.key === i.key);
  if (toIdx >= 0 && toIdx < treeLevels.value.length - 1) {
    treeLevels.value = treeLevels.value.slice(0, toIdx + 1);
  }
}
function handleEnterNode(i: T) {
  treeLevels.value = [...treeLevels.value, i];
}
</script>

<style scoped lang="less">
:deep(.ant-breadcrumb) {
  &-link a {
    height: unset;
    margin: 0 !important;
  }
  &-separator {
    margin: 0 2px;
  }
}

.disabled-checked {
  &:deep(.ant-checkbox-checked .ant-checkbox-inner) {
    background-color: var(--FO-Brand-Primary-Disabled) !important;
    border-color: var(--FO-Brand-Primary-Disabled) !important;

    &::after {
      border-color: var(--FO-Content-Components1) !important;
    }
  }
}
</style>
