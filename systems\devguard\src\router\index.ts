import { createRouter, createWebHistory } from 'vue-router';
import type { App } from 'vue';
import { withDocumentTitle } from './modules/withDocumentTitle.ts';
import { withTrack } from './modules/withTrack.ts';
import { PlatformEnterPoint, PlatformRoutePath } from '@hg-tech/oasis-common';
import { MergePermission } from '../constants/premission.ts';
import { withPermission } from './modules/withPermission.ts';

export const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      name: PlatformEnterPoint.SysDevGuard,
      path: PlatformRoutePath.SysDevGuard,
      redirect: {
        name: PlatformEnterPoint.CommitCenter,
      },
    },
    {
      name: PlatformEnterPoint.CommitCenter,
      path: PlatformRoutePath.CommitCenter,
      meta: {
        title: '提交中心',
        isProjectSelectorShow: true,
      },
      component: () => import('../views/commit-center-home/List.vue'),
    },
    {
      name: PlatformEnterPoint.CommitList,
      path: PlatformRoutePath.CommitList,
      meta: {
        title: '提交列表',
        permissionDeclare: {
          any: [MergePermission.SubmitCenterAccessRecords],
        },
        isProjectSelectorShow: false,
      },
      component: () => import('../views/commit-list/index.vue'),
    },
    {
      name: PlatformEnterPoint.Instance,
      path: PlatformRoutePath.Instance,
      meta: {
        title: '实例配置、操作、状态同步',
        permissionDeclare: {
          any: [MergePermission.SubmitCenterInstanceStatus],
          isProjectSelectorShow: false,
        },
      },
      component: () => import('../views/instance/InstanceList.vue'),
    },
    {
      name: PlatformEnterPoint.InstanceConfig,
      path: PlatformRoutePath.InstanceConfig,
      meta: {
        title: '检查实例配置',
        isProjectSelectorShow: false,
      },
      component: () => import('../views/instance/config/InstanceConfig.vue'),
    },
  ],
  strict: true,
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

export function setupRouter(app: App<Element>) {
  app.use(router);

  // with plugins
  withDocumentTitle(router);
  withPermission(router);
  withTrack(router);
}
