import type { PermissionBaseRes } from './_common';
import { apiService } from '../services/req';
import type { OrgStructureType } from './group';
import type { SysUserInfo } from './users';

export interface GetUserPermissionsRequest {
  /** 用户hgId（非必填） */
  hgId?: string;
  /** 应用id（非必填） */
  appId?: number;
  /** 租户id列表（多项目应用）（非必填） */
  tenantIds?: string[];
  /** 用户完整信息（用于提取hgAccount等） */
  userInfo?: SysUserInfo;
}

export interface PermissionItem {
  /** 权限点ID */
  id: number;
  /** 应用ID */
  appId: number;
  /** 租户ID */
  tenant: string;
  /** 权限点名称 */
  name: string;
  /** 权限点Code */
  code?: string;
  /** 权限点描述 */
  description?: string;
  /** 权限分类 */
  category?: string;
  /** 是否启用 */
  isEnabled?: boolean;
}

export interface PermissionRoleItem {
  /** 角色ID */
  id: number;
  /** 角色名称 */
  name: string;
  /** 租户信息 */
  tenant?: string;
  /** 租户ID */
  tenantId?: number;
  /** 是否为公共角色 */
  isPublic?: boolean;
}

export interface GetUserPermissionsResponse {
  code: number;
  data?: {
    resource: {
      /** 权限点ID作为key，值为角色列表 */
      [permissionId: number]: PermissionRoleItem[];
    };
    stats?: {
      /** 总权限数 */
      totalPermissions: number;
      /** 已启用权限数 */
      enabledPermissions: number;
      /** 总角色数 */
      totalRoles: number;
    };
  };
  message?: string;
}

export interface PermissionMemberGroupItem {
  /** 角色ID */
  id: number;
  /** 应用ID */
  appId: number;
  /** 租户名称 */
  tenant: string;
  /** 租户ID */
  tenantId: number;
  /** 角色名称 */
  name: string;
}

export interface PermissionMemberItem {
  /** 成员名称 */
  name: string;
  /** 资源ID */
  resourceId: string;
  /** 成员类型 */
  type: OrgStructureType;
  /** 拥有该权限的角色列表 */
  groups: PermissionMemberGroupItem[];
  /** 是否为外包成员 */
  outsourceFlag?: boolean;
}

export interface GetPermissionMembersRequest {
  /** 应用id（必填） */
  appId: number;
  /** 权限资源id（必填） */
  resourceId: number;
  /** 租户id列表（多项目应用）（非必填） */
  tenantIds?: string[];
}

// 多租户模式返回结构
export interface MultiTenantPermissionMembersData {
  tenants: {
    /** 租户ID作为key */
    [tenantId: string]: PermissionMemberItem[];
  };
}

// 非多租户模式返回结构
export interface NonMultiTenantPermissionMembersData {
  members: PermissionMemberItem[];
}

export interface GetPermissionMembersResponse {
  code: number;
  data?: MultiTenantPermissionMembersData | NonMultiTenantPermissionMembersData;
  message?: string;
}

// V2 接口专用类型定义
export interface PermissionGroupItem {
  /** 角色ID */
  id: number;
  /** 应用ID */
  appId: number;
  /** 租户名称 */
  tenant: string;
  /** 租户ID */
  tenantId: number;
  /** 角色名称 */
  name: string;
}

// V2 多租户模式返回结构
export interface MultiTenantPermissionGroupsData {
  tenants: {
    /** 租户ID作为key */
    [tenantId: string]: PermissionGroupItem[];
  };
}

// V2 非多租户模式返回结构
export interface NonMultiTenantPermissionGroupsData {
  groups: PermissionGroupItem[];
}

export interface GetPermissionGroupsResponse {
  code: number;
  data?: MultiTenantPermissionGroupsData | NonMultiTenantPermissionGroupsData;
  message?: string;
}

/**
 * 获取应用下用户拥有的权限点
 */
export const batchGetGroupByUser = apiService.POST<
  Record<string, never>,
  GetUserPermissionsRequest,
  PermissionBaseRes<GetUserPermissionsResponse['data']>
>('/api/auth/v1/permissions/batchGetGroupByUser');

/**
 * 获取应用下拥有权限点的成员和角色
 */
export const batchGetGroupByPermission = apiService.POST<
  Record<string, never>,
  GetPermissionMembersRequest,
  PermissionBaseRes<GetPermissionMembersResponse['data']>
>('/api/auth/v1/permissions/batchGetGroupByPermission');

/**
 * 获取应用下拥有权限点的所有角色
 */
export const batchGetGroupByPermissionV2 = apiService.POST<
  Record<string, never>,
  GetPermissionMembersRequest,
  PermissionBaseRes<GetPermissionGroupsResponse['data']>
>('/api/auth/v1/permissions/batchGetGroupByPermissionV2');
