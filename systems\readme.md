# 子系统

可独立部署、运行的子系统，也可被 project 引入作为子应用，业务主流程应当覆盖**端到端测试**。

## 允许 Workspace 依赖

- libs
- modules

## 如何发布一个子系统

- package.json 包含了 `build`/ `build:rnd` / `build:pre` 指令
- 项目**已经配置了类型检查**(`vue-tsc --noEmit --skipLibCheck`等)
- 父系统中已经配置了子系统的入口（MicroApp.url）（地址通常维护在`.env`中，请确保**各个环境**均已配置）
- 在 [PaaS 平台](https://paas.hypergryph.net/#/serviceCatalog/APP/APP_1821) 对应环境中配置访问入口
  - 前缀匹配为 `/`，后端服务端口为 80
- 在 `libs/configs/src/entry/projects` 中添加了对应的配置文件已合并到 `main` 分支，并更新发布[tech-center-fe(1821)](https://devops.bk.hypergryph.net/console/pipeline/tech-center-efficiency/p-a57dc95348b14280ac69bc170ad5964b/history)
  - 配置的 host 域名需要和上一步中的申请的域名一致
- 在 [hg-tech-fe-mono:publish](https://devops.bk.hypergryph.net/console/pipeline/techcenter-efficiency/p-e98dc07d8c9f41e28c2784a355f2592b/history) 的发布信息 `publish_project` 字段已添加新的子系统名称
