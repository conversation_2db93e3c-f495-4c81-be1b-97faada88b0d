import type { ThemeConfig } from 'ant-design-vue/es/config-provider/context';
import { ColorPresetToken, ForgeonTheme } from '../tokens';
import { merge } from 'lodash-es';
import {
  type ForgeonThemeMapToAntdComponentToken,
  type ForgeonThemeMapToAntdToken,
  toAntdThemedComponentColors,
  toAntdThemedToken,
} from './helper';

const commonToken = {
  colorFillAlter: 'ContainerFill1',
  colorBgLayout: 'ContainerBackground',
  colorBgContainer: 'ContainerFill1',
  colorBgContainerDisabled: 'ContainerFill2',
  colorText: 'ContentText1',
  colorTextSecondary: 'ContentText2',
  colorTextTertiary: 'ContentText3',
  colorTextQuaternary: 'ContentText4',
  colorPrimary: 'BrandPrimaryDefault',
  colorPrimaryActive: 'BrandPrimaryActive',
  colorPrimaryBg: 'ContainerBackground',
  colorPrimaryHover: 'BrandPrimaryHover',
  colorError: 'FunctionalError1Default',
  colorErrorActive: 'FunctionalError1Active',
  colorErrorBg: 'FunctionalError2Default',
  colorErrorBgHover: 'FunctionalError2Hover',
  colorErrorHover: 'FunctionalError1Hover',
  colorInfo: 'FunctionalInfo1Default',
  colorInfoActive: 'FunctionalInfo1Active',
  colorInfoBg: 'FunctionalInfo2Default',
  colorInfoBgHover: 'FunctionalInfo2Hover',
  colorInfoHover: 'FunctionalInfo1Hover',
  colorSuccess: 'FunctionalSuccess1Default',
  colorSuccessActive: 'FunctionalSuccess1Active',
  colorSuccessBg: 'FunctionalSuccess2Default',
  colorSuccessBgHover: 'FunctionalSuccess2Hover',
  colorWarning: 'FunctionalWarning1Default',
  colorWarningActive: 'FunctionalWarning1Active',
  colorWarningBg: 'FunctionalWarning2Default',
  colorWarningBgHover: 'FunctionalWarning2Hover',
  colorWarningHover: 'FunctionalWarning1Hover',
  colorBorder: 'ContainerStroke1',
  colorBorderSecondary: 'ContainerStroke1',
  colorBgElevated: 'ContainerFill1',
  colorBgTextHover: 'ContainerFill2',
  colorSplit: 'ContainerStroke1',
  colorLink: 'BrandPrimaryDefault',
  colorLinkHover: 'BrandPrimaryHover',
  colorLinkActive: 'BrandPrimaryActive',
  controlItemBgHover: 'ContainerFill2',
  controlItemBgActive: 'BrandTertiaryActive',
} satisfies ForgeonThemeMapToAntdToken;

// 仅包含颜色相关配置的映射
const commonComponentColors = {
  Menu: {
    colorItemBgHover: 'ContainerFill3',
    colorItemBgSelected: 'BrandTertiaryActive',
    colorItemText: 'ContentText1',
    colorSubItemBg: 'ContainerFill0',
    colorItemTextSelected: 'BrandPrimaryDefault',
    /* @ts-expect-error antdv 类型缺失 */
    menuSubMenuBg: 'ContainerFill2',
  },
  Popover: {
    colorBgElevated: 'ContainerFill1',
  },
  Dropdown: {
    colorBgElevated: 'ContainerFill1',
    colorTextDisabled: 'BrandPrimaryDefault',
  },
  TreeSelect: {
    colorBgContainer: 'ContainerFill1',
    colorBorder: 'ContainerStroke3',
  },
  Checkbox: {
    colorBorder: 'ContainerStroke3',
  },
  Form: {
    colorTextDescription: 'ContentText4',
  },
  Upload: {
    colorFillAlter: 'ContainerFill2',
    colorBorder: 'ContainerStroke2',
  },
  Card: {
    colorBgContainer: 'ContainerFill1',
    colorBorderSecondary: 'ContainerStroke1',
  },
  Input: {
    colorBgContainer: 'ContainerFill3',
    colorBorder: 'ContainerFill3',
  },
  InputNumber: {
    colorBgContainer: 'ContainerFill3',
    colorBorder: 'ContainerFill3',
  },
  DatePicker: {
    colorBgContainer: 'ContainerFill3',
    colorBorder: 'ContainerFill3',
    colorBgElevated: 'ContainerFill1',
  },
  Select: {
    colorBgContainer: 'ContainerFill3',
    colorBorder: 'ContainerFill3',
    colorTextDisabled: 'ContentText3',
  },
  Switch: {
    colorTextQuaternary: 'ContainerFill5',
  },
  Drawer: {
    colorBgElevated: 'ContainerFill1',
  },
  Modal: {
    colorBgElevated: 'ContainerFill1',
  },
} satisfies ForgeonThemeMapToAntdComponentToken;

// 特殊覆盖配置（包括 'none' 值和其他非主题颜色值）
const commonComponentOverrides = {
  Input: {
    colorErrorOutline: 'none',
    colorErrorBorderHover: 'none',
    controlOutline: 'none',
  },
  InputNumber: {
    colorErrorOutline: 'none',
    colorErrorBorderHover: 'none',
  },
  DatePicker: {
    colorErrorOutline: 'none',
    colorErrorBorderHover: 'none',
    controlOutline: 'none',
  },
  Select: {
    controlOutline: 'none',
    margin: 4,
  },
  Cascader: {
    controlOutline: 'none',
  },
  Button: {
    controlOutline: 'none',
    colorErrorOutline: 'none',
    colorWarningOutline: 'none',
  },
  Menu: {
    radiusSubMenuItem: 8,
  },
  Dropdown: {
    paddingXXS: 8,
    borderRadiusLG: 12,
  },
} satisfies {
  [P in keyof NonNullable<ThemeConfig['components']>]: {
    [T in keyof NonNullable<NonNullable<ThemeConfig['components']>[P]>]: any
  }
};

const AntdOverrideLight: ThemeConfig = {
  token: {
    wireframe: false,
    ...toAntdThemedToken(commonToken, ForgeonTheme.Light),
  },
  components: merge(
    toAntdThemedComponentColors(commonComponentColors, ForgeonTheme.Light),
    commonComponentOverrides,
    {} as ThemeConfig['components'],
  ),
};

const AntdOverrideDark: ThemeConfig = {
  token: {
    wireframe: false,
    ...toAntdThemedToken(commonToken, ForgeonTheme.Dark),
  },
  components: merge(
    toAntdThemedComponentColors(commonComponentColors, ForgeonTheme.Dark),
    commonComponentOverrides,
    {
      Menu: {
        itemMarginInline: 4,
        colorSubItemBg: 'transparent',
        colorItemBg: 'transparent',
        radiusItem: 8,
        boxShadowTertiary: '0px 4px 12px rgba(0, 0, 0, 0.08)',
      },
      Dropdown: {
        controlPaddingHorizontal: 8,
      },
      DatePicker: {
        colorBgElevated: ColorPresetToken.DarkGray3,
        colorBorder: ColorPresetToken.DarkGray6,
      },
      Radio: {
        colorBorder: ColorPresetToken.DarkGray6,
      },
      Message: {
        colorBgContainer: ColorPresetToken.DarkGray3,
      },
    } as ThemeConfig['components'],
  ),
};

export {
  AntdOverrideDark,
  AntdOverrideLight,
};
