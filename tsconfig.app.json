{"compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "target": "esnext", "lib": ["ESNext", "DOM", "DOM.Iterable"], "moduleDetection": "force", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "allowImportingTsExtensions": true, "allowJs": true, "strict": true, "noEmit": true, "isolatedModules": false, "skipLibCheck": true}, "exclude": ["**/node_modules/**", "**/dist/**", "**/build/**", "**/nginx/**", "**/es/**"]}