<template>
  <div class="h-full flex flex-col rounded-xl bg-FO-Container-Fill1 p-24px">
    <!-- 查询表单区域 -->
    <div class="flex-none">
      <UserQueryForm
        :initialAppId="initialAppId"
        :initialTenantId="initialTenantId"
        @query="handleQuery"
      />
    </div>

    <!-- 结果展示区域 -->
    <div class="flex-auto">
      <UserResultTable
        :data="queryResult"
        :loading="queryLoading"
        :stats="queryStats"
        :queryParams="lastQueryParams"
        :categories="queryCategories"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import { useLatestPromise } from '@hg-tech/utils-vue';
import UserQueryForm from './UserQueryForm.vue';
import UserResultTable from './UserResultTable.vue';
import type { GetUserPermissionsRequest } from '../../../api/query';
import type {
  PermissionCategory,
  PermissionQueryStats,
  QueryPermissionRow,
} from '../permission';
import { batchGetGroupByUser } from '../../../api/query';
import { useUserPermissionQuery } from '../../../composables/useUserPermissionQuery';
import { usePermissionCategories } from '../../../composables/usePermissionCategories';

defineProps<{
  initialAppId?: number;
  initialTenantId?: number;
}>();

// 使用权限查询composable
const { transformPermissionsToTableData } = useUserPermissionQuery();

// 使用权限分类composable
const { categoriesWithUncategorized, refreshData: refreshPermissionData } = usePermissionCategories();

const {
  loading: queryLoading,
  execute: executeBatchQuery,
} = useLatestPromise(batchGetGroupByUser);

// 查询结果状态
const queryResult = ref<QueryPermissionRow[]>([]);
const queryCategories = ref<PermissionCategory[]>([]);
const queryStats = ref<PermissionQueryStats | null>(null);
const lastQueryParams = ref<GetUserPermissionsRequest>();

// 处理查询
async function handleQuery(params: GetUserPermissionsRequest) {
  if (queryLoading.value) {
    return;
  }

  lastQueryParams.value = params;

  try {
    if (params.appId) {
      await refreshPermissionData(params.appId);
    }

    const response = await executeBatchQuery({}, params);

    if (response?.data?.code === 0 && response.data.data) {
      const { tableData, categories, stats } = transformPermissionsToTableData(
        response.data.data,
        categoriesWithUncategorized.value,
        false,
      );
      queryResult.value = tableData;
      queryCategories.value = categories;
      queryStats.value = stats;

      if (tableData.length === 0) {
        message.info('未查询到相关权限信息');
      }
    } else {
      message.error(response?.data?.message || '查询失败');
      queryResult.value = [];
      queryCategories.value = [];
      queryStats.value = null;
    }
  } catch (error) {
    console.error('权限查询失败:', error);
    queryResult.value = [];
    queryCategories.value = [];
    queryStats.value = null;
  }
}
</script>
