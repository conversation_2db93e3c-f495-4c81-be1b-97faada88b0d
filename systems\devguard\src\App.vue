<template>
  <ForgeonThemeProvider class="h-full" :theme="forgeonConfig?.theme">
    <ConfigProvider :locale="zhCN" :theme="antdTheme" :getContainer="getContainer">
      <Layout />
    </ConfigProvider>
  </ForgeonThemeProvider>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { ConfigProvider } from 'ant-design-vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import Layout from './views/Layout.vue';
import { AntdOverrideDark, AntdOverrideLight } from '@hg-tech/forgeon-style';
import { ForgeonThemeProvider, useMicroAppInject, usePlatformConfigCtx } from '@hg-tech/oasis-common';

const { data: forgeonConfig } = useMicroAppInject(usePlatformConfigCtx);
const antdTheme = computed(() => (forgeonConfig.value?.theme === 'dark' ? AntdOverrideDark : AntdOverrideLight));

function getContainer() {
  return document.getElementById('sub-app');
}
</script>
