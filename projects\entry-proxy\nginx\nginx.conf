# 该文件为自动生成，请勿手动修改
worker_processes auto;

events {
    worker_connections 1024;
}

http {
    include       mime.types;
    default_type  text/plain;
    charset utf-8;
    
    # 日志格式
    log_format proxy_log 
        [$time_local] 
        [$status][$http_host][$http_x_fe_env_flag][$http_x_be_env_flag]$request \t
        [$upstream_cache_status]$upstream_addr($upstream_response_time) \t
        [$http_upgrade]$proxy_host$uri;

    # 指定 dns 服务器
    resolver kube-dns.kube-system.svc.cluster.local valid=30s;
    resolver_timeout 5s;
    
    # 提升最大域名长度
    server_names_hash_bucket_size 128;
    
    # 增加转发缓存
    proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=oss_proxy_cache:10m max_size=1g inactive=60m use_temp_path=off;
   
    server {
       listen 80 default_server;
       server_name _;
       
       # 健康检查
       location /checkHealth {
         access_log off;
         return 200;
       }  
       
       location ^~/ {
         return 404 "未找到服务【$host】, 请在 https://gitlab.hypergryph.net/tech/platform/hypergryph-tech-fe-mono/-/tree/main/projects/entry-proxy#%E5%A6%82%E4%BD%95%E6%B7%BB%E5%8A%A0%E4%B8%80%E4%B8%AA%E6%96%B0%E7%9A%84%E9%A1%B9%E7%9B%AE 中添加项目";
       }
    }

    include /etc/nginx/conf.d/*.nginx;
}
