import type { UserConfig } from 'unocss';
import { kebabCase, mapKeys } from 'lodash-es';
import { ForgeonThemeCssVar } from '@hg-tech/forgeon-style';

export const unocssTheme = {
  colors: mapKeys(ForgeonThemeCssVar, (_, key) => {
    // FunctionalSuccess1Default -> FO-Functional-Success1-Default
    // ContentText0 -> FO-Content-Text0
    return `FO-${kebabCase(key)}`
      .replace(/-\w/g, (match) => match.toUpperCase())
      .replace(/-(\d+)/g, '$1');
  }) as Record<string, string>,
  breakpoints: {
    'xs': '320px',
    'sm': '640px',
    'md': '768px',
    'lg': '960px',
    'xl': '1280px',
    '2xl': '1536px',
    '3xl': '1920px',
    '4xl': '2560px',
  },
  borderRadius: {
    none: '0px',
    sm: '4px',
    md: '8px',
    lg: '12px',
    half: '50%',
    full: '9999px',
  },
  /**
   * @see https://www.figma.com/design/tDf0zHfSRgs7ZtYdsUG9um/ForgeOn%E7%BB%84%E4%BB%B6%E8%A7%84%E8%8C%83-2.0%EF%BC%88%E8%BF%81%E7%A7%BB%E4%B8%AD%EF%BC%89?node-id=4766-14392&t=W7K3w23pE7eZSgvX-4
   * @deprecated 升级 unocss 版本至 v66.4.2 后，将被修改为 shadow
   */
  boxShadow: {
    1: '0 2px 8px 0 rgba(0, 0, 0, 0.04), 0 1px 12px 0 rgba(0, 0, 0, 0.06), 0 1px 3px -2px rgba(0, 0, 0, 0.08)',
    2: '0 4px 16px 0 rgba(0, 0, 0, 0.04), 0 4px 20px 1px rgba(0, 0, 0, 0.06), 0 2px 6px -4px rgba(0, 0, 0, 0.10)',
    3: '0 6px 24px 2px rgba(0, 0, 0, 0.04), 0 8px 32px 2px rgba(0, 0, 0, 0.06), 0 4px 12px -8px rgba(0, 0, 0, 0.12)',
    4: '0 6px 24px 2px rgba(0, 0, 0, 0.06), 0 8px 32px 2px rgba(0, 0, 0, 0.08), 0 4px 12px -8px rgba(0, 0, 0, 0.24)',
  },
} satisfies UserConfig['theme'];
