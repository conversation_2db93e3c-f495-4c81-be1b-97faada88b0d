<template>
  <div v-if="linkStore.isOasis" class="h-screen w-screen flex flex-col items-center justify-center">
    <ThemeSwitcher
      class="pos-absolute right-0 top-50% z-100 font-size-[20px] c-FO-Content-Components1"
      placement="left"
      :theme="getDarkMode"
      :onToggle="setDarkMode"
    />
    <div class="flex text-4xl font-medium">
      已触发唤起
      <img alt="" :src="oasisLogo" class="mx-1 h-[40px] w-[40px]">
      Oasis下载
    </div>
    <div class="my-6 text-4xl font-medium">
      请点击浏览器上方弹窗
    </div>
    <div class="flex items-center">
      没有看到弹窗？请确认已安装
      <a-button type="link" class="!mx-0 !px-0" @click="handleOpenLatest">
        最新版Oasis
      </a-button>
      且启动过，可点击 再次触发 重新唤起
    </div>
    <div class="mt-8 flex items-center">
      <div
        class="cursor-pointer rounded-full bg-gray-300 px-4 py-2 text-black hover:bg-gray-400"
        @click="openApp"
      >
        再次触发
      </div>
      <div
        type="primary"
        class="ml-8 cursor-pointer rounded-full bg-gray-300 px-4 py-2 text-black hover:bg-gray-400"
        @click="goToPage"
      >
        通过浏览器下载
      </div>
    </div>
    <NoPermissionDownloadModal @register="registerModal" />
  </div>
</template>

<script lang="ts" setup name="LinkToApp">
import { omit, pickBy } from 'lodash-es';
import { nextTick, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import oasisLogo from '/@/assets/images/oasis-logo.png';
import { useModal } from '/@/components/Modal';
import { useTrack } from '/@/hooks/system/useTrack';
import { useMessage } from '/@/hooks/web/useMessage';
import { OASIS_DOWNLOAD_URL } from '/@/settings/siteSetting';
import type { oasisQueryType } from '/@/store/modules/link';
import { useLinkStoreWithOut } from '/@/store/modules/link';
import { openWindow, setObjToUrlParams } from '/@/utils';
import { isString } from '/@/utils/is';
import NoPermissionDownloadModal from '/@/views/sys/exception/NoPermissionDownloadModal.vue';
import { getAllProjectList } from '../../../hooks/useProjects.ts';
import { ThemeSwitcher } from '@hg-tech/oasis-common';
import { useRootSetting } from '../../../hooks/setting/useRootSetting.ts';

const props = defineProps({
  app: {
    type: String,
    default: '',
  },
});

const { getDarkMode, setDarkMode } = useRootSetting();
const { currentRoute, push } = useRouter();
const ignoreQueryList = ['app', 'fs', 'p'];
const linkStore = useLinkStoreWithOut();
const { createMessage } = useMessage();
const [registerModal, { openModal }] = useModal();

async function openApp() {
  if (linkStore.getAppPrefix === 'oasisdownload') {
    const projectList = await getAllProjectList();
    const foundProject = projectList.find((i) => i.ID === Number(linkStore.getOasisQuery?.project));

    if (!foundProject) {
      await nextTick();
      openModal(true, {});

      return false;
    }

    const { setTrack } = useTrack();

    setTrack('5repgarrwt');
  }

  openWindow(`${linkStore.getAppPrefix}://${linkStore.getQueryList.join('&')}`, { target: '_self' });

  return true;
}

function goToPage() {
  const isGameStore = linkStore.getOasisQuery?.type === '1';
  const pageName = isGameStore ? 'GamePackage' : 'ToolkitDetail';
  const pagePath = isGameStore
    ? '/test/gamePackage'
    : `/toolkit/detail/${linkStore.getOasisQuery?.project}`;
  const query = {
    p: isGameStore ? linkStore.getOasisQuery?.project : undefined,
    b: isGameStore ? linkStore.getOasisQuery?.branch : undefined,
    tab: isGameStore ? undefined : 'Version',
    v: linkStore.getOasisQuery?.version,
    downloadNow: '1',
    fs: '0',
  };

  try {
    push({
      name: pageName,
      query,
      params: {
        id: isGameStore ? undefined : linkStore.getOasisQuery?.project,
      },
    });
  } catch {
    // 登录才能访问的页面，跳转到登录页
    push({
      name: 'Login',
      query: {
        redirect: setObjToUrlParams(pagePath, pickBy(query, isString)),
      },
    });
    createMessage.warning('请登录，以验证下载身份');
  }
}

function handleOpenLatest() {
  openWindow(OASIS_DOWNLOAD_URL);
}

onMounted(async () => {
  const { query } = currentRoute.value;
  const appPrefix = props.app || query?.app;

  if (appPrefix) {
    const isOasis = appPrefix === 'oasisdownload';

    linkStore.setIsOasis(isOasis);

    if (isOasis) {
      linkStore.setOasisQuery(omit(query, ignoreQueryList) as oasisQueryType);
    }

    linkStore.setAppPrefix(props.app || (query.app as string) || '');

    if (linkStore.getAppPrefix) {
      const queryList: string[] = [];

      for (const key in query) {
        if (!ignoreQueryList.includes(key)) {
          queryList.push(`${key}=${query[key]}`);
        }
      }

      linkStore.setQueryList(queryList);

      const res = await openApp();

      if (res) {
        push({ query: { fs: query?.fs } });
      }
    }
  }
});

defineExpose({
  openApp,
});
</script>
