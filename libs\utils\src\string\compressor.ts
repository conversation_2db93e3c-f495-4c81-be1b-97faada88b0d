import { compressToEncodedURIComponent, decompressFromEncodedURIComponent } from 'lz-string';

interface SafeEncodeOptions {
  /**
   * 添加前缀
   * @default ''
   */
  prefix?: string;
  /**
   * 是否避免二次编码
   * @default true
   */
  avoidDoubleEncode?: boolean;
  /**
   * 编码失败时的回退值
   * @default ''
   */
  fallback?: string;
}

interface SafeDecodeOptions {
  /**
   * 添加前缀
   * @default ''
   */
  prefix?: string;
  /**
   * 是否避免抛出未SafeEncode的错误
   * @default true
   */
  avoidNoEncodedError?: boolean;
  /**
   * 是否避免抛出非指定Prefix的错误
   * @default true
   */
  avoidDifferentPrefixError?: boolean;
  /**
   * 编码失败时的回退值
   * @default ''
   */
  fallback?: string;
}

/**
 * 编码前缀分隔符
 * @description 用于分隔编码前缀和编码内容
 */
const ENCODE_PREFIX_SEPARATOR = '::';

/**
 * 安全编码string
 * @param input - 要编码的URL路径
 * @param options - 编码选项
 * @param options.prefix - 是否添加前缀
 * @param options.debug - 是否启用调试模式
 * @param options.avoidDoubleEncode - 是否避免二次编码，默认 true
 * @param options.fallback - 编码失败时的回退值，默认 ''
 * @return {string} - 编码后的URL路径
 * @description 如果输入不是字符串或为空字符串，则返回指定fallback，不存在fallback则返回空字符串
 * @description 如果avoidDoubleEncode为true且输入已经是指定prefix的安全编码，则返回原始输入，会有warning提示
 */
function safeEncode(input: string, options: SafeEncodeOptions = {}): string {
  if (typeof input !== 'string' || !input) {
    console.warn(`[safeEncode] input must be a no-empty string`);
    return options.fallback ?? '';
  }
  const { prefix, avoidDoubleEncode = true } = options;
  if (avoidDoubleEncode && isSafeEncoded(input, prefix)) {
    console.warn(`[safeEncode] Avoid double encoding`);
    return input; // 避免同prefix二次编码
  }
  const prefixed = prefix ? `${prefix}::${input}` : input;
  const encoded = compressToEncodedURIComponent(prefixed);
  return encoded;
}
/**
 * 安全解码URL
 * @param maybeEncodedPath - 可能是编码的URL路径
 * @param options - 解码选项
 * @param options.prefix - 是否添加前缀
 * @param options.avoidNoEncodedError - 是否避免处理未SafeEncode的错误，默认 true
 * @param options.avoidDifferentPrefixError - 是否避免处理异常Prefix的错误，默认 true
 * @param options.fallback - 编码失败时的回退值，默认 ''
 * @return {string} - 解码后的URL路径
 * @description 如果输入不是字符串或解码失败，则返回指定fallback，不存在fallback则返回空字符串
 */
function safeDecode(maybeEncodedPath: string, options: SafeDecodeOptions = {}): string {
  if (typeof maybeEncodedPath !== 'string' || !maybeEncodedPath) {
    console.warn(`[safeDecode] type of maybeEncodedPath is no-empty string`);
    return options.fallback ?? '';
  }

  const { prefix = '', avoidNoEncodedError = true, avoidDifferentPrefixError = true } = options;
  const decompressed = decompressFromEncodedURIComponent(maybeEncodedPath);
  if (!decompressed) {
    if (avoidNoEncodedError) {
      console.warn(`[safeDecode] No safe encoded input: ${maybeEncodedPath}`);
      return options.fallback ?? ''; // 解压失败，返回fallback或空字符串
    } else {
      throw new Error(`[safeDecode] No safe encoded input: ${maybeEncodedPath}`);
    }
  }

  if (decompressed.includes(ENCODE_PREFIX_SEPARATOR)) {
    const [maybePrefix, ...rest] = decompressed.split(ENCODE_PREFIX_SEPARATOR);
    const reconstructed = rest.join(ENCODE_PREFIX_SEPARATOR); // 防止 URL 中也有 ::
    if (prefix && maybePrefix !== prefix) {
      if (avoidDifferentPrefixError) {
        console.warn(`[safeDecode] Prefix mismatch: input string encoded with "${maybePrefix}", but decode prefix is "${prefix}"`);
        return options.fallback ?? ''; // 解压内容不匹配当前 prefix，返回fallback或空字符串
      } else {
        throw new Error(`[safeDecode] Prefix mismatch: input string encoded with "${maybePrefix}", but decode prefix is "${prefix}"`);
      }
    }
    return prefix !== '' ? reconstructed : decompressed; // 返回解压后的字符串
  }

  return decompressed;
}

/**
 * 判断字符串是否为safeEncode编码内容
 * @param input - 输入字符串
 * @param prefix - 前缀
 * @return {boolean} - 是否为safeEncode编码内容
 * @description 如果输入不是字符串或解码失败，则返回 false
 * @description 如果指定了前缀，则检查解码后的字符串是否以该前缀开头
 * @description 如果没有指定前缀，则只要解码成功即可
 */
function isSafeEncoded(input: string, prefix?: string): boolean {
  if (typeof input !== 'string' || !input) {
    console.warn(`[isSafeEncoded] input must be a no-empty string`);
    return false;
  }

  try {
    const decompressed = decompressFromEncodedURIComponent(input);
    if (!decompressed) {
      console.warn(`[isSafeEncoded] No safe encoded input: ${input}`);
      return false;
    }
    if (decompressed.includes(ENCODE_PREFIX_SEPARATOR)) {
      const [maybeSalt] = decompressed.split(ENCODE_PREFIX_SEPARATOR);
      if (!maybeSalt) {
        console.warn(`[isSafeEncoded] No safe encoded input: ${input}`);
        return false;
      }
      if (prefix !== '' && maybeSalt !== prefix) {
        return false; // 解压内容不匹配当前 prefix
      }
    }
    return true;
  } catch {
    return false;
  }
}

/**
 * 获取编码字符串的前缀
 * @param input - 编码字符串
 * @return {string | undefined} - 编码字符串的前缀
 * @description 如果输入不是字符串或解码失败，则返回undefined
 */
function getEncodePrefix(input: string): string | undefined {
  if (typeof input !== 'string' || !input) {
    console.warn(`[getEncodePrefix] input must be a no-empty string`);
    return undefined;
  }

  const decompressed = decompressFromEncodedURIComponent(input);
  if (!decompressed) {
    return undefined;
  }
  // 如果解压后的字符串不包含 ENCODE_PREFIX_SEPARATOR，则返回空字符串
  if (!decompressed.includes(ENCODE_PREFIX_SEPARATOR)) {
    return '';
  }
  // 如果解压后的字符串包含 ENCODE_PREFIX_SEPARATOR，则获取前缀
  const [maybeSalt] = decompressed.split(ENCODE_PREFIX_SEPARATOR);
  if (!maybeSalt) {
    return undefined;
  }
  // 如果前缀以 ENCODE_PREFIX_SEPARATOR 结尾，则去掉 ENCODE_PREFIX_SEPARATOR
  const prefix = maybeSalt.endsWith(ENCODE_PREFIX_SEPARATOR) ? maybeSalt.slice(0, -2) : maybeSalt;
  if (!prefix) {
    return '';
  }
  return prefix;
}

/**
 * 尝试解压缩直到原始字符串
 * @param input - 输入字符串
 * @param prefixes - 前缀数组
 * @return {string} - 解压缩后的字符串
 */
function tryDecompressUntilRawString(input: string, prefixes?: string[]): string {
  if (typeof input !== 'string') {
    return '';
  }
  function recursiveDecompress(input: string): string {
    if (!prefixes || prefixes.length === 0) {
      const decoded = safeDecode(input, { fallback: input });
      if (decoded === input) {
        return input; // 解码后内容未变化，停止递归
      }
      return recursiveDecompress(decoded);
    }
    // 如果指定了前缀数组，检查是否有匹配的前缀
    const prefix = prefixes.find((p) => isSafeEncoded(input, p));
    // 没有匹配的前缀，返回原始输入
    if (!prefix) {
      return input;
    }
    const decoded = safeDecode(input, { prefix, fallback: input });
    if (decoded === input) {
      return input; // 解码后内容未变化，停止递归
    }
    return recursiveDecompress(decoded);
  }
  return recursiveDecompress(input);
}

export {
  getEncodePrefix,
  isSafeEncoded,
  safeDecode,
  SafeDecodeOptions,
  safeEncode,
  SafeEncodeOptions,
  tryDecompressUntilRawString,
};
