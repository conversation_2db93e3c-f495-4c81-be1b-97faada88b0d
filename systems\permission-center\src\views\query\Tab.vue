<template>
  <PageLayout>
    <template #headerTitle>
      <Breadcrumb class="FO-Font-B18" separator=">">
        <template #itemRender="{ route: { path, breadcrumbName } }">
          <router-link v-if="path" :to="path" class="!bg-transparent !text-FO-Content-Text2">
            {{ breadcrumbName }}
          </router-link>
          <span v-else class="!text-FO-Content-Text1">
            {{ breadcrumbName }}
          </span>
        </template>
        <BreadcrumbItem v-for="navRoute in routeNav" :key="navRoute.breadcrumbName">
          <router-link v-if="navRoute.path" :to="navRoute.path" class="!bg-transparent !text-FO-Content-Text2">
            {{ navRoute.breadcrumbName }}
          </router-link>
          <span v-else class="!text-FO-Content-Text1">
            {{ navRoute.breadcrumbName }}
          </span>
        </BreadcrumbItem>
      </Breadcrumb>
    </template>
    <template #headerActions />

    <div class="h-full flex flex-col gap-12px p-20px">
      <!-- Tab 切换区域 -->
      <div class="rounded-12px bg-FO-Container-Fill1 p-16px px-16px">
        <PermissionSolidTabs
          v-model:value="currentTab"
          :tabList="[
            { key: QueryTab.ByUser, label: '按用户' },
            { key: QueryTab.ByPermission, label: '按权限' },
          ]"
        />
      </div>

      <!-- 内容区域 -->
      <div class="flex-auto">
        <UserQueryTab
          v-if="currentTab === QueryTab.ByUser"
          :initialAppId="initialAppId"
          :initialTenantId="initialTenantId"
        />

        <PermissionQueryTab
          v-else
          :initialAppId="initialAppId"
          :initialTenantId="initialTenantId"
        />
      </div>
    </div>
  </PageLayout>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Breadcrumb, BreadcrumbItem } from 'ant-design-vue';
import { useRouteQuery } from '@vueuse/router';
import { useRouter } from 'vue-router';
import type { Route } from 'ant-design-vue/es/breadcrumb/Breadcrumb';
import { ForgeonTitleMap, PlatformEnterPoint } from '@hg-tech/oasis-common';
import PageLayout from '../../components/PageLayout.vue';
import PermissionSolidTabs from '../../components/PermissionSolidTabs.vue';
import UserQueryTab from './components/UserQueryTab.vue';
import PermissionQueryTab from './components/PermissionQueryTab.vue';

enum QueryTab {
  ByUser = 'byUser',
  ByPermission = 'byPermission',
}

// 当前Tab状态
const currentTab = useRouteQuery('tab', QueryTab.ByUser);

const router = useRouter();
const initialAppId = useRouteQuery('appId', undefined, { transform: Number });
const initialTenantId = useRouteQuery('tenantId', undefined, { transform: Number });

// 面包屑导航
const routeNav = computed<Route[]>(() => {
  return [
    {
      path: router.resolve({ name: PlatformEnterPoint.PermissionCenterDashboard }).href,
      breadcrumbName: ForgeonTitleMap[PlatformEnterPoint.PermissionCenterDashboard],
    },
    {
      path: '',
      breadcrumbName: ForgeonTitleMap[PlatformEnterPoint.PermissionCenterQuery],
    },
  ];
});
</script>
