{"compilerOptions": {"composite": false, "target": "es2020", "jsx": "preserve", "jsxImportSource": "vue", "lib": ["ES2020", "DOM", "DOM.Iterable"], "useDefineForClassFields": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "baseUrl": ".", "module": "ES2022", "moduleResolution": "bundler", "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "types": ["vite/client", "node"], "allowImportingTsExtensions": true, "strict": true, "strictNullChecks": true, "noImplicitAny": true, "importHelpers": true, "noEmit": true, "noEmitHelpers": false, "sourceMap": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "disableSizeLimit": true}, "include": ["./src/**/*.ts", "./src/**/*.d.ts", "./src/**/*.tsx", "./src/**/*.vue"], "exclude": ["node_modules"]}