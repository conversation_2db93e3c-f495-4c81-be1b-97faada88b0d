<template>
  <div :class="prefixCls">
    <div class="c-FO-Content-Text1 c-FO-Content-Text2 4xl:text-2xl">
      证书编号: {{ examProgressInfo?.certNumber }}
    </div>
    <div class="mx-auto px-30px text-4xl font-bold 4xl:text-6xl">
      <div class="ml-72px">
        干员
        <span class="border-b-2 px-4">{{ formatNickName(userStore.getUserInfo) }}</span>
        经技术中心P4技能认证考试(TCP4T)合格，
      </div>
      <div class="mt-32px flex items-end">
        <div>
          准予申请P4仓库提交权限
        </div>
        <div v-if="hasP4Button" class="ml-4 flex cursor-pointer items-center pr-12px text-16px font-bold 4xl:h-60px !b-rd-20px !border-none !bg-#0B8600 !pl-18px 4xl:text-26px !c-FO-Content-Components1 4xl:b-rd-30px! 4xl:text-36px!" @click="goP4Onboarding">
          <div>前往P4接触流程以完成权限申请</div>
          <Icon icon="icon-park-outline:right" class="mt-1px font-bold !ml-0 !text-20px" />
        </div>
        <div v-else class="ml-4 flex cursor-pointer items-center pr-12px text-16px font-bold 4xl:h-60px !b-rd-20px !border-none !bg-#0B8600 !pl-18px 4xl:text-26px !c-FO-Content-Components1 4xl:b-rd-30px!" @click="goP4Onboarding">
          <div>前往P4接触流程</div>

          <Icon icon="icon-park-outline:right" class="mt-1px font-bold !ml-0 !text-20px" />
        </div>
      </div>
    </div>

    <div class="mb-2 mr-5 flex justify-end">
      <Icon icon="tcp4t-footer-logo|svg" class="!text-color !h-40px" :size="200" />
    </div>
  </div>
</template>

<script lang="ts" setup name="TCP4TCertificate">
import confetti from 'canvas-confetti';
import { onMounted, ref } from 'vue';
import { useP4Exam } from '../hook';
import Icon from '/@/components/Icon';
import { formatNickName } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { useUserStore } from '/@/store/modules/user';
import { getOrientationByPage, getUserSchedule } from '/@/api/page/system';
import { useGo } from '/@/hooks/web/usePage';
import { sendEvent } from '/@/service/tracker';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';

const { prefixCls } = useDesign('tcp4t-Certificate');
const userStore = useUserStore();
const { examProgressInfo } = useP4Exam();
const go = useGo();
const hasP4Button = ref(false);
function congrats() {
  const defaults = {
    colors: ['#5D8C7B', '#F2D091', '#F2A679', '#D9695F', '#8C4646'],
    shapes: ['square'],
    ticks: 500,
  } as confetti.Options;
  confetti({
    ...defaults,
    particleCount: 80,
    spread: 100,
    origin: { y: 0 },
  });
  setTimeout(() => {
    confetti({
      ...defaults,
      particleCount: 50,
      angle: 60,
      spread: 80,
      origin: { x: 0 },
    });
  }, 250);
  setTimeout(() => {
    confetti({
      ...defaults,
      particleCount: 50,
      angle: 120,
      spread: 80,
      origin: { x: 1 },
    });
  }, 400);
}

function goP4Onboarding() {
  sendEvent('p4_study_p4_exam_click_go_to_contact_flow');
  go({ name: PlatformEnterPoint.P4Onboarding });
}
onMounted(async () => {
  congrats();
  const { list } = await getOrientationByPage({ page: 1, pageSize: 100 });
  // 获取值ID和bizId的值
  let id: number | undefined;
  let bizId: number | undefined;
  list.forEach((item) => {
    if (item.title === '获得提交权限') {
      id = item.ID;
      bizId = item.bizId;
    }
  });

  const { orientation } = await getUserSchedule({ bizId: 1 });
  hasP4Button.value
        = orientation.bizId !== bizId || orientation.orientationId !== id || orientation.status !== 1;
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-tcp4t-Certificate';
.@{prefix-cls} {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
  height: calc(100vh - 250px);
  min-height: 300px;
  overflow: auto;
}
</style>
