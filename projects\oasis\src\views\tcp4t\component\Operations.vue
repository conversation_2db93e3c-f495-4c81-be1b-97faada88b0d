<template>
  <div ref="scrollOperationRef" :class="prefixCls" :style="`height:${contentHeight}`">
    <div v-if="curOperation">
      <MarkdownViewer :value="curOperation?.questStem" />
      <div v-if="curOperation?.needsInput" class="mt-10">
        <div class="font-bold">
          请输入题目中要求输入的内容：
        </div>
        <div>
          <ATextarea
            v-model:value.trim="operationInput"
            class="mt-2"
            placeholder="请输入答案"
            :maxlength="100"
            :status="operationInput.length === 100 ? 'error' : ''"
          />
          <div v-if="operationInput.length === 100" class="mt-2 c-FO-Functional-Error1-Default">
            已达到最大字符限制,上限100字
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="TCP4TOperation">
import { Textarea as ATextarea } from 'ant-design-vue';
import { computed, onBeforeMount } from 'vue';
import { useP4ExamOperations } from '../hook';
import { MarkdownViewer } from '/@/components/Markdown';
import { useDesign } from '/@/hooks/web/useDesign';

const { prefixCls } = useDesign('tcp4t-operation');

const {
  getOperationCount,
  getCurOperation,
  curOperation,
  operationInput,
  operationBtnType,
  scrollOperationRef,
} = useP4ExamOperations();

onBeforeMount(async () => {
  await getOperationCount();
  await getCurOperation();
});

const contentHeight = computed(() => {
  return operationBtnType.value === 'modify' ? 'calc(100vh - 400px)' : 'calc(100vh - 250px)';
});
defineExpose({
  curOperation,
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-tcp4t-operation';
.@{prefix-cls} {
  padding: 1.5em 6em;
  overflow: auto;
  min-height: 300px;
}
</style>
