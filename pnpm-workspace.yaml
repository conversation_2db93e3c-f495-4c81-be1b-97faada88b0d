packages:
  - libs/**/*
  - modules/**/*
  - systems/*
  - projects/*
catalog:
  '@napi-rs/canvas': ^0.1.72
  '@ant-design/icons': ^5.4.0
  '@ant-design/icons-vue': ^7.0.1
  '@antfu/eslint-config': ^3.11.2
  '@antv/x6': ^2.18.1
  '@antv/x6-vue-shape': ^2.1.2
  '@changesets/cli': ^2.27.7
  '@commitlint/cli': ^19.3.0
  '@commitlint/config-conventional': ^19.2.2
  '@eslint-react/eslint-plugin': ^1.18.0
  '@eslint/eslintrc': ^3.1.0
  '@hg/event-log': ^3.2.1
  '@icon-park/vue-next': ^1.4.2
  '@iconify-icons/icon-park-outline': ^1.2.11
  '@iconify-icons/ic': ^1.2.13
  '@iconify-icons/line-md': ^1.2.30
  '@iconify-icons/icon-park-solid': ^1.2.8
  '@iconify-icons/ant-design': ^1.2.7
  '@iconify/json': ^2.2.243
  '@iconify/vue': ^4.1.2
  '@lancercomet/fetcher': ^1.1.2
  '@lancercomet/style': ^1.0.1
  '@lancercomet/suntori': ^1.1.0
  '@logicflow/core': ^2.0.3
  '@logicflow/extension': ^2.0.5
  '@micro-zoe/micro-app': ^1.0.0-rc.26
  '@microsoft/fetch-event-source': ^2.0.1
  '@sentry/react': ^8.35.0
  '@sentry/types': ^8.35.0
  '@sentry/vite-plugin': ^2.22.6
  '@sentry/vue': ^8.50.0
  '@swc/core': 1.3.58
  '@tanstack/react-router': ^1.45.11
  '@tanstack/router-devtools': ^1.45.11
  '@tanstack/vue-virtual': ^3.10.8
  '@tweenjs/tween.js': ^25.0.0
  '@types/ali-oss': ^6.16.11
  '@types/bytes': ^3.1.5
  '@types/canvas-confetti': ^1.6.4
  '@types/codemirror': ^5.60.15
  '@types/color': ^3.0.6
  '@types/crypto-js': ^4.2.2
  '@types/eslint__eslintrc': ^2.1.2
  '@types/gulp': ^4.0.17
  '@types/gulp-less': ^0.0.36
  '@types/inquirer': ^9.0.7
  '@types/intro.js': ^5.1.5
  '@types/jest': ^29.0.3
  '@types/jmuxer': ^2.0.7
  '@types/js-cookie': ^3.0.6
  '@types/katex': ^0.16.7
  '@types/lodash': ^4.17.7
  '@types/lodash-es': ^4.17.12
  '@types/markdown-it': ^14.1.2
  '@types/markdown-it-emoji': ^3.0.1
  '@types/node': ^22.15.29
  '@types/node-fetch': ^2.6.2
  '@types/nprogress': ^0.2.3
  '@types/papaparse': ^5.3.14
  '@types/qrcode': ^1.5.5
  '@types/qs': ^6
  '@types/react': ^18.3.3
  '@types/react-dom': ^18.3.0
  '@types/react-window': ^1.8.8
  '@types/react-window-infinite-loader': ^1.0.9
  '@types/showdown': ^2.0.6
  '@types/sortablejs': ^1.15.8
  '@types/splitpanes': ^2.2.6
  '@types/uuid': ^9.0.8
  '@unocss/eslint-plugin': ^0.65.1
  '@unocss/preset-mini': ^65.4.3
  '@unocss/reset': ^0.59.4
  '@unocss/transformer-attributify-jsx': ^0.59.4
  '@unocss/transformer-directives': ^0.59.4
  '@unocss/transformer-variant-group': ^0.59.4
  '@vitejs/plugin-react-swc': ^3.7.0
  '@vitejs/plugin-vue': ^5.1.3
  '@vitejs/plugin-vue-jsx': ^4.0.1
  '@vue/compiler-sfc': ^3.4.38
  '@vue/devtools-api': ^6.6.1
  '@vue/runtime-core': ^3.4.38
  '@vue/shared': ^3.4.38
  '@vue/test-utils': ^2.4.6
  '@vueuse/components': ^11.0.3
  '@vueuse/core': ^11.0.3
  '@vueuse/router': ^11.0.3
  '@vueuse/integrations': ^13.5.0
  '@vueuse/shared': ^11.0.3
  '@welldone-software/why-did-you-render': ^8.0.3
  '@zxcvbn-ts/core': ^3.0.4
  vite-plugin-lazy-import: ^1.0.7
  ahooks: ^3.8.0
  ali-oss: ^6.20.0
  ant-design-vue: ^4.2.5
  antd: ^5.19.3
  autoprefixer: ^10.4.20
  axios-mock-adapter: ^1.22.0
  axios: ^1
  bytes: ^3.1.2
  canvas-confetti: ^1.9.3
  cheerio: 1.0.0-rc.12
  close-with-grace: ^2.0.0
  clsx: ^2.1.1
  codemirror: ^6.0.1
  commander: ^12.1.0
  conventional-changelog-cli: ^5.0.0
  cronstrue: ^2.50.0
  cropperjs: ^1.6.2
  cross-env: ^7.0.3
  crypto-js: ^4.2.0
  cspell: ^8.14.2
  csstype: ' ^3.1.3'
  dayjs: ^1.11.13
  defu: ^6.1.4
  dotenv: ^16.4.5
  driver.js: ^1.3.1
  echarts: ^5.5.1
  esbuild: ^0.23.0
  eslint-config-turbo: ^2.1.3
  eslint-plugin-format: ^0.1.3
  eslint-plugin-react-hooks: ^5.1.0
  eslint-plugin-react-refresh: ^0.4.16
  eslint: ^9.16.0
  eventemitter3: ^5.0.1
  exceljs: ^4.4.0
  fast-deep-equal: ^3.1.3
  fastify: ^4.28.1
  filesize: ^10.1.6
  glob: ^11.0.0
  gsap: ^3.12.5
  gulp-esbuild: ^0.14.0
  gulp-less: ^5.0.0
  gulp-typescript: 6.0.0-alpha.1
  gulp: ^5.0.0
  happy-dom: ^16.7.2
  highlight.js: ^11.10.0
  history: ^5.3.0
  html2canvas: ^1.4.1
  husky: ^9.1.1
  immer: ^10.1.1
  inquirer: ^10.1.8
  intro.js: ^7.2.0
  jest: ^29.4.1
  jmuxer: ^2.0.7
  js-cookie: ^3.0.5
  jsdom: ^25.0.1
  jsoneditor: ^10.1.0
  katex: ^0.16.21
  konva: ^9.3.20
  less: ^4.2.0
  lint-staged: ^15.2.7
  lodash-es: ^4
  lodash: ^4
  lru-cache: ^11.0.0
  lz-string: ^1.5.0
  madge: ^8.0.0
  markdown-it-emoji: ^3.0.0
  markdown-it-katex: ^2.0.3
  markdown-it: ^14.1.0
  mermaid: ^11.6.0
  miao-vuefinder: ^0.2.16
  naive-ui: ^2.41.0
  node-fetch: ^2.6.9
  npm-run-all: ^4.1.5
  nprogress: ^0.2.0
  p-queue: ^8.0.1
  papaparse: ^5.4.1
  path-to-regexp: ^7.1.0
  picocolors: ^1.0.1
  pinia: 2.2.2
  pinyin-pro: ^3.24.2
  pkg-types: ^1.2.0
  postcss-html: ^1.7.0
  postcss-less: ^6.0.0
  postcss-scss: ^4.0.9
  postcss: ^8.4.43
  print-js: ^1.6.0
  qrcode: ^1.5.4
  qs: ^6
  random-jpn-emoji: ^2.0.2
  react-dom: ^18.3.1
  react-virtualized-auto-sizer: ^1.0.24
  react-window-infinite-loader: ^1.0.9
  react-window: ^1.8.10
  react: ^18.3.1
  reflect-metadata: ^0.2.2
  resize-observer-polyfill: ^1.5.1
  rimraf: ^6.0.1
  rollup-plugin-copy: ^3.5.0
  rollup-plugin-visualizer: ^5.12.0
  sass: ^1.77.8
  showdown: ^2.1.0
  sortablejs: ^1.15.3
  spark-md5: ^3.0.2
  splitpanes: ^3.1.5
  swc-plugin-vue-jsx: 0.2.5
  terser: ^5.30.4
  three: ^0.167.1
  tippy.js: 6.3.7
  tree-crawl: ^1.2.2
  ts-jest: ^29.0.5
  ts-node: ^10.9.2
  ts-pattern: ^5.7.0
  tslib: ^2.6.2
  tsx: ^4.19.4
  turbo: ^2.5.4
  turborepo-remote-cache: ^2.2.1
  typescript: ^5
  unocss: ^0.62.4
  unplugin-swc: 1.4.3
  unplugin-vue-setup-extend-plus: ^1.0.1
  unplugin-vue-inspector: ^2.3.2
  uuid: ^9.0.1
  v-code-diff: ^1.13.1
  v-viewer: ^3.0.21
  vditor: ^3.10.5
  version-rocket: ^1.7.2
  viewerjs: ^1.11.7
  vite-bundle-analyzer: ^0.10.5
  vite-plugin-cdn-import: ^1.0.1
  vite-plugin-compression: ^0.5.1
  vite-plugin-html: ^3.2.2
  vite-plugin-mkcert: ^1.17.6
  vite-plugin-static-copy: ^1.0.6
  vite-plugin-svg-icons: ^2.0.1
  vite-plugin-vue-devtools: ^7.6.4
  vite-plugin-vue-setup-extend: ^0.4.0
  vite-plugin-legacy-swc: ^1.2.3
  vite-svg-loader: ^5.1.0
  vite: ^5.4.19
  vitest: ^2.1.9
  vue-json-pretty: ^2.4.0
  vue-request: 2.0.4
  vue-router: ^4.4.3
  vue-tippy: ^6.4.4
  vue-tsc: ^2.1.10
  vue-types: ^5.1.3
  vue: ^3
  vxe-pc-ui: ^4.7.21
  vxe-table-plugin-export-xlsx: ^4.0.7
  vxe-table: ^4.14.5
  wavesurfer.js: ^7.8.4
  xe-utils: ^3.7.8
  xlsx: ^0.18.5
  yaml: ^2.5.0
  zustand: ^4.5.4
  zx: ^8.1.5
