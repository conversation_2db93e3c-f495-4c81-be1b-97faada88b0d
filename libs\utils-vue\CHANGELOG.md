# @hg-tech/utils-vue

## 1.1.4

### Patch Changes

- Updated dependencies [27b5691]
  - @hg-tech/utils@1.1.6

## 1.1.3

### Patch Changes

- Updated dependencies [9836dfb]
  - @hg-tech/utils@1.1.5

## 1.1.2

### Patch Changes

- Updated dependencies [74a17db]
  - @hg-tech/utils@1.1.4

## 1.1.1

### Patch Changes

- 79e4836: useFetchPoolLRU 支持非 ref 入参

## 1.1.0

### Minor Changes

- 1303ea6: createUseFetchPoolLRU 中 get/forceGet 现在会自动 refresh 所有响应监听

### Patch Changes

- 4d0ee15: createUseFetchPoolLRU 新增默认值参数
- Updated dependencies [bbc5f5e]
  - @hg-tech/utils@1.1.3

## 1.0.1

### Patch Changes

- dbd982b: 修复 useModalShow 未导出问题
- Updated dependencies [9225188]
  - @hg-tech/utils@1.1.2
