import type { ForgeonTheme } from '@hg-tech/forgeon-style';
import { createMicroAppCtx } from '../../composables/index';
import { MicroAppEventBusEvent } from './types';

export interface ProjectListItem {
  /** 项目id */
  id?: number;
  /** 项目名称 */
  name?: string;
  /** 项目代号 */
  alias?: string;
}
/**
 * 主应用公用配置
 */
export const usePlatformConfigCtx = createMicroAppCtx<{
  /**
   * 全局主题
   */
  theme: ForgeonTheme;
  /**
   * 侧边展开状态
   */
  isMenuExpanded: boolean;
  /**
   * 项目列表(基础信息)
   */
  projectList: {
    id: number;
    name: string;
  }[];
  /**
   * 当前项目id
   */
  currentProjectId: number | undefined;
  /**
   * 侧边展开方法
   */
  changeMenuExpendStatus: (expend: boolean) => void;
  /**
   * 设置网页title
   */
  setDocumentTitle: (title: string) => void;
  /**
   * 设置当前项目id
   */
  setCurrentProjectId: (id: number) => void;
  currentProjectInfo: ProjectListItem | undefined;
}>(MicroAppEventBusEvent.PlatformConfig);
