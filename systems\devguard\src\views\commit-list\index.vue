<template>
  <div class="h-full w-full flex flex-auto flex-col gap-20px overflow-hidden p-20px">
    <PageHeader v-if="submitStreamID" pageTitle="提交记录：" class="b-rd-8px bg-FO-Container-Fill1!" :submitStreamID="submitStreamID" @back="goBack()" />
    <div class="relative flex flex-auto flex-col overflow-hidden b-rd-8px bg-FO-Container-Fill1">
      <div class="flex flex-none gap-15px p-20px">
        <div
          v-for="item in processTag" :key="item.name" class="FO-Font-R14 flex cursor-pointer items-center b-2px b-transparent b-rd-16px px-8px"
          :class="{ 'b-FO-Brand-Primary-Default!': item.name === nowTabName }" @click="tabChange(item.name)"
        >
          {{ item.label }} {{ item.name === processTagKey.InProcess ? countRecords?.data?.data?.processing : (item.name === processTagKey.PendingApproval ? countRecords?.data?.data?.qaChecking : '') }}
        </div>
        <Input v-model:value="search" placeholder="搜索cl或提交人" class="w-300px b-rd-20px" allowClear @change="changeSearch">
          <template #prefix>
            <SearchOutlined />
          </template>
        </Input>
        <Button shape="round" :class="{ 'b-FO-Brand-Primary-Default!': search }" @click="clicksearch">
          搜索
        </Button>
      </div>
      <div class="flex flex-auto flex-col overflow-auto px-20px">
        <Spin :spinning="spinning">
          <List :data-source="list" class="commit-list" :split="false" :locale="{ emptyText: '当前暂无提交记录' }">
            <template #renderItem="{ item }">
              <div class="mb-10px b-rd-8px bg-FO-Container-Fill2">
                <ListItem class="p-12px!">
                  <div class="flex flex-1 flex-col gap-12px">
                    <div class="flex items-center">
                      <div class="FO-Font-B16">
                        {{ item.submitState === SubmitStateType.SubmittingSucceeded ? `正式cl:${item.submitCL}` : `本地cl:${item.shelveCL}` }}
                      </div>
                      <Tooltip placement="bottom" @openChange="(value) => getSubmitInfo(item.ID, value)">
                        <template #title>
                          <div>提交人:{{ promptText?.submitterName }}</div>
                          <div>日期:{{ promptText?.date }}</div>
                          <div>描述:{{ promptText?.description }}</div>
                        </template>
                        <IIcon class="ml-5px" />
                      </Tooltip>
                      <div v-if="item.submitState === SubmitStateType.SubmittingSucceeded" class="ml-12px c-FO-Content-Text2">
                        本地cl：{{ item.shelveCL }}
                      </div>
                      <div class="ml-12px c-FO-Content-Text2">
                        提交人：{{ item.submitter.nickName }}
                      </div>
                      <div v-if="item.createTime" class="ml-12px c-FO-Content-Text2">
                        创建时间：{{ dayjs(item?.createTime).format('MM-DD HH:mm:ss') }}
                      </div>
                    </div>
                    <div>
                      <Process :item="item" />
                    </div>
                  </div>
                  <div class="flex gap-10px">
                    <Button v-if="item.checkState === 2 " size="small" class="px-10px!" @click="approval(item.ID)">
                      执行审批
                    </Button>
                    <div v-if="item.checkState === 2" class="h-20px w-1px bg-FO-Container-Stroke2" />
                    <Button v-if="[2, 3, 4].includes(item.reviewState)" size="small" class="px-10px!" @click="JumpReviewPage(`${item.swarmBaseURL}/reviews/${item.reviewCL}`)">
                      <div class="flex items-center gap-2px">
                        <span>查看审查</span>
                        <Icon :icon="RightUpIcon" />
                      </div>
                    </Button>
                    <Button size="small" class="px-10px!" @click="jumpsubmitPage(`${item.swarmBaseURL}/changes/${item.submitState === SubmitStateType.SubmittingSucceeded ? item.submitCL : item.shelveCL}`)">
                      <div class="flex items-center gap-2px">
                        <span>查看提交</span>
                        <Icon :icon="RightUpIcon" />
                      </div>
                    </Button>
                  </div>
                </ListItem>
              </div>
            </template>
          </List>
        </Spin>
      </div>
      <div class="flex flex-none justify-end b-rd-8px bg-FO-Container-Fill1 p-20px">
        <Pagination v-model:current="pagination.currentPage" v-model:pageSize="pagination.pageSize" showSizeChanger :total="pagination.total" size="small" @change="getRecordList" />
      </div>
    </div>
    <ApprovalModalHolder />
  </div>
</template>

<script setup lang="ts">
import RightUpIcon from '@iconify-icons/icon-park-outline/arrow-right-up';

import { Icon } from '@iconify/vue';
import PageHeader from '../PageHeader.vue';
import { Button, Input, List, ListItem, message, Pagination, Spin, Tooltip } from 'ant-design-vue';
import { SearchOutlined } from '@ant-design/icons-vue';
import IIcon from '../../assets/icons/iIcon.svg?component';
import ApprovalModal from './ApprovalModal.vue';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import Process from './Process.vue';
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { type CommitListItem, type StreamsListItem, type SubmitInfo, applyQaCheckApi, getCountRecordsApi, getQaCheckInfoApi, getRecordListByPage, getStreamsInfo, getSubmitInfoApi } from '../../api';
import { useRouter } from 'vue-router';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig';
import { store } from '../../store/pinia';
import { processTag, processTagKey, SubmitStateType } from './type.data';
import { traceClickEvent } from '../../../src/services/track';
import { TrackEventName } from '../../../src/constants/event';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import dayjs from 'dayjs';

const router = useRouter();
const routeParams = router.currentRoute.value.params;
const submitStreamID = routeParams.submitStreamID ? Number(routeParams.submitStreamID) : null;

const { execute: recordListExecute, data: recordList, loading: recordListLoading } = useLatestPromise(getRecordListByPage);
const { execute: submitInfoExecute, data: submitInfo } = useLatestPromise(getSubmitInfoApi);
const { execute: qaCheckInfoExecute, data: qaCheckInfo } = useLatestPromise(getQaCheckInfoApi);
const { execute: applyQaCheckExecute, data: applyQaCheckRes } = useLatestPromise(applyQaCheckApi);
const { execute: getCountRecordsExecute, data: countRecords } = useLatestPromise(getCountRecordsApi);
const [ApprovalModalHolder, showApprovalModal] = useModalShow(ApprovalModal);
const { execute, data: streamsInfoData } = useLatestPromise(getStreamsInfo);
const nowTabName = ref<processTagKey>(processTagKey.All);
const search = ref('');
const spinning = computed(() => recordListLoading.value);
const pagination = reactive({
  pageSize: 30,
  total: 0,
  currentPage: 1,
});
const streamsInfo = ref<StreamsListItem>();
const forgeonConfig = useForgeonConfigStore(store);

const list = ref<CommitListItem[]>([]);
const promptText = ref<SubmitInfo>();
async function getSubmitInfo(recordID: number, value: boolean) {
  if (value) {
    await submitInfoExecute({ id: forgeonConfig.currentProjectId!, recordID }, {});
    promptText.value = submitInfo.value?.data?.data;
  }
}

async function approval(ID: number) {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_SUBMIT_RECORD_APPROVAL_MODAL_TRIGGER);
  await qaCheckInfoExecute({ id: forgeonConfig.currentProjectId!, recordID: ID }, {});
  const result = await showApprovalModal({
    checkQaList: qaCheckInfo.value?.data?.data?.checkQaList,
    recordID: ID,
    shelveCL: qaCheckInfo.value?.data?.data?.shelveCL,
    submitter: qaCheckInfo.value?.data?.data?.submitter,
    checkTime: qaCheckInfo.value?.data?.data?.checkTime,
    description: qaCheckInfo.value?.data?.data?.description,
    canApply: qaCheckInfo.value?.data?.data?.canApply,
    workItemURL: qaCheckInfo.value?.data?.data?.workItemURL,
    workItemTitle: qaCheckInfo.value?.data?.data?.workItemTitle,
    sentReq: async (ApproveCode: number) => {
      await applyQaCheckExecute({ id: forgeonConfig.currentProjectId! }, { clRecordID: ID, ApproveCode });
      return applyQaCheckRes.value;
    },
  });
  if (result?.updatedItem) {
    message.success('审批完成');
  }

  traceClickEvent(TrackEventName.SUBMIT_CENTER_SUBMIT_RECORD_APPROVAL_COMPLETE);
  getRecordList();
}

function jumpPage(url: string) {
  window.open(url);
}

function JumpReviewPage(url: string) {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_SUBMIT_RECORD_REVIEW_CLICK);
  jumpPage(url);
}
function jumpsubmitPage(url: string) {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_DETAIL_CLICK);
  jumpPage(url);
}
function tabChange(name: processTagKey) {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_SUBMIT_RECORD_FILTER_CLICK);
  nowTabName.value = name;
  pagination.currentPage = 1;
  pagination.pageSize = 30;
  getRecordList();
}

function goBack() {
  router.push({
    name: PlatformEnterPoint.CommitCenter,
  });
}
function changeSearch() {
  if (!search.value) {
    clicksearch();
  }
}

function clicksearch() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_SUBMIT_RECORD_SEARCH_TRIGGER, {
    search_keyword: search.value,
  });
  pagination.currentPage = 1;
  pagination.pageSize = 30;
  getRecordList();
}

onMounted(async () => {
  if (!submitStreamID) {
    return;
  }
  await execute({ id: forgeonConfig.currentProjectId!, stream_id: submitStreamID }, {});
  streamsInfo.value = streamsInfoData.value?.data?.data?.stream;
  getRecordList();
  getCountRecordsExecute({
    id: forgeonConfig.currentProjectId!,
    streamID: submitStreamID,
  }, {});
});

async function getRecordList() {
  if (!submitStreamID) {
    return;
  }
  await recordListExecute({
    id: forgeonConfig.currentProjectId!,
    streamID: submitStreamID,
    submitState: nowTabName.value,
    keyword: search.value,
    page: pagination.currentPage,
    pageSize: pagination.pageSize,
  }, {});
  list.value = recordList.value?.data?.data?.list || [];
  pagination.total = recordList.value?.data?.data?.total || 0;
}
watch(() => forgeonConfig.currentProjectId, () => {
  goBack();
});
</script>

<style lang="less" scoped>
.commit-list {
  :deep(.ant-list-empty-text) {
    padding-top: 300px;
    color: var(--FO-Content-Text1);
  }
}
</style>
