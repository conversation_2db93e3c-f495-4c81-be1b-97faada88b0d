<template>
  <div :class="prefixCls" :style="{ height: isModalPage ? undefined : '100vh' }">
    <div
      ref="chartRef"
      :class="`${prefixCls}__chart`"
      :style="
        isModalPage ? { height: '500px', width: '100%' } : { height: '220px', width: '680px' }
      "
    />
  </div>
</template>

<script lang="ts" setup name="GamePackageDoctorTrend">
import type { LineSeriesOption } from 'echarts';
import { map, zip } from 'lodash-es';
import { type Ref, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import type {
  GamePackageDoctorDetail,
  GamePackageDoctorDetailSummaryItem,
} from '/@/api/page/model/testModel';
import { getGamePackagesDoctorTrend } from '/@/api/page/test';
import { useDesign } from '/@/hooks/web/useDesign';
import { useECharts } from '/@/hooks/web/useECharts';
import { useMessage } from '/@/hooks/web/useMessage';
import { formatTISOToDate } from '/@/utils/dateUtil';
import { isArray } from '/@/utils/is';
import { warpTextByNum } from '/@/utils/lib/echarts';
import { propTypes } from '/@/utils/propTypes';

const props = defineProps({
  isModalPage: propTypes.bool.def(false),
  trendIDs: propTypes.array.def([]),
  pkgID: propTypes.number.def(),
  projectID: propTypes.number.def(),
});
const { prefixCls } = useDesign('game-package-doctor-trend');
const { currentRoute } = useRouter();
const { createMessage } = useMessage();

const pkgID = props.isModalPage ? props.pkgID : Number(currentRoute.value.params.id);
const projectID = props.isModalPage ? props.projectID : Number(currentRoute.value.query.p);
const queryIds = currentRoute.value.query.ids;
const pid = Number(currentRoute.value.query.pid);
const ids = props.isModalPage
  ? props.trendIDs
  : ((isArray(queryIds) ? queryIds : [queryIds]) as string[]);
const doctorList = ref<GamePackageDoctorDetail[]>([]);
const summaryList = ref<GamePackageDoctorDetailSummaryItem[]>([]);

async function getTrendData() {
  if (!pkgID) {
    createMessage.warning('游戏包ID不存在');
    return;
  }
  if (!ids || !ids?.length) {
    createMessage.warning('包体检测ID不存在');
    return;
  }
  if (!projectID && !pid) {
    createMessage.warning('项目ID不存在');
    return;
  }

  const { list } = await getGamePackagesDoctorTrend(pid || projectID, pkgID, {
    idList: ids?.map((e) => Number(e)),
  });
  if (list?.length > 0) {
    const sortList = list.sort((a, b) => (a.ID || 0) - (b.ID || 0));
    doctorList.value = sortList;
    summaryList.value = map(sortList, 'summary.list');
  } else {
    doctorList.value = [];
    summaryList.value = [];
  }
}

const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
onMounted(async () => {
  await getTrendData();

  const series: LineSeriesOption[] = [];
  const zipData = zip(...(summaryList.value as any));
  zipData.forEach((e: GamePackageDoctorDetailSummaryItem[]) => {
    series.push({
      name: e[0]?.name ?? '',
      type: 'line',
      stack: 'Total',
      data: map(e, (item) => {
        return Number.parseFloat(((item?.total_raw ?? 0) / (1024 * 1024)).toFixed(2));
      }),
    });
  });
  setOptions({
    tooltip: {
      trigger: 'axis',
    },
    grid: {
      bottom: '12%',
      left: 100,
      right: 100,
    },
    legend: {
      data: map(zipData, (e: GamePackageDoctorDetailSummaryItem[]) => e[0]?.name ?? ''),
    },
    animation: props.isModalPage,
    yAxis: {
      name: '大小(MB)',
      type: 'value',
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisLabel: {
        interval: 0,
        formatter: (text) => warpTextByNum(text, 28 - props.trendIDs!.length * 2),
      },
      data: doctorList.value.map((e) => e.version || formatTISOToDate(e.UpdatedAt, true, true)),
    },
    series,
  });
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-game-package-doctor-trend';
.@{prefix-cls} {
  background-color: @FO-Container-Fill1;
}
</style>
