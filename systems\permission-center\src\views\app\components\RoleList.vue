<template>
  <div class="h-full flex b-rd-[12px] bg-FO-Container-Fill1 py-[16px]">
    <div class="h-full w-232px flex flex-none flex-col gap-16px">
      <div class="px-16px">
        <TenantSelector v-if="appInfo?.isMultiTenant" v-model:tenantId="tenantId" :tenantList="tenantList" />
      </div>
      <div class="flex items-center justify-between px-16px">
        <div class="FO-Font-B16">
          角色列表
        </div>
        <Tooltip title="新增角色">
          <div class="flex cursor-pointer rounded-full bg-FO-Container-Fill3 p-6px" @click="handleAddRole">
            <AddIcon class="FO-Font-R12" />
          </div>
        </Tooltip>
      </div>
      <GroupSelector
        v-model:activeGroup="activeGroupId" :allGroups="allGroups?.data?.data"
        :loading="loadingAppGroups || sortLoading" :appInfo="appInfo" @editGroup="handleEditRole"
        @deleteGroup="handleDeleteRole" @copyGroup="handleCopyRole" @setPublicGroup="handleSetPublicGroup"
        @sortGroup="handleSortGroup" @search="handleSearch"
      />
    </div>
    <div class="w-1px flex-none bg-FO-Container-Stroke1" />
    <div v-if="activeGroupId" class="h-full min-h-0 flex flex-1 flex-col overflow-hidden">
      <PermissionLineTabs v-model:value="activeTab" :tabList="tabList" class="mb-24px flex-none px-16px" />
      <div class="min-h-0 flex-1 overflow-hidden">
        <RolePermissionConfig
          v-if="activeTab === TabKey.Permission" :activeGroupId="activeGroupId" :tenantId="tenantId"
          :appId="appInfo?.id"
        />
        <RoleMemberConfig
          v-else-if="activeTab === TabKey.Member" :appId="appInfo?.id" :activeGroupId="activeGroupId"
          :tenantId="tenantId"
        />
        <Empty v-else class="m-auto" />
      </div>
    </div>
    <div v-else class="h-full flex-1 overflow-hidden">
      <div class="h-full flex items-center justify-center">
        <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" class="c-FO-Content-Text4">
          <template #description>
            无角色数据，请
            <Button type="link" class="px-0" @click="handleAddRole">
              添加角色
            </Button>
          </template>
        </Empty>
      </div>
    </div>

    <!-- 角色表单弹窗 -->
    <RoleFormModalHolder />
  </div>
</template>

<script setup lang="tsx">
import { Button, Checkbox, Empty, message, Modal, Tooltip } from 'ant-design-vue';
import { useRouteQuery } from '@vueuse/router';
import { useRoute, useRouter } from 'vue-router';
import { computed, ref, watch } from 'vue';
import AddIcon from '../../../assets/icons/SystemStrokeAdd.svg?component';
import PermissionLineTabs from '../../../components/PermissionLineTabs.vue';
import GroupSelector from './GroupSelector.vue';
import RoleFormModal from './RoleFormModal.vue';
import { type PermissionAppGroupDetail, type PermissionAppGroupListItem, cancelPublicGroup, copyPermissionGroup, createPermissionGroup, deletePermissionGroup, getPermissionAppGroups, setPublicGroup, updatePermissionGroup, updatePermissionGroupOrder } from '../../../api/group.ts';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import { useDebounceFn } from '@vueuse/shared';
import type { PermissionAppInfo, PermissionTenantInfo } from '../../../api/app.ts';
import RolePermissionConfig from './RolePermissionConfig.vue';
import RoleMemberConfig from './member/RoleMemberConfig.vue';
import TenantSelector from './TenantSelector.vue';
import WarningErrorIcon from '../../../assets/icons/SystemFillWarning.svg?component';

const props = defineProps<{
  appInfo?: PermissionAppInfo;
  tenantList?: PermissionTenantInfo[];
  initialRoleId?: number;
}>();

enum TabKey {
  Permission = 'permission',
  Member = 'member',
}

const router = useRouter();
const route = useRoute();
const tenantId = defineModel<number>('tenantId');
const activeGroupId = ref<PermissionAppGroupDetail['id']>();
const searchKeyword = ref<string>();
const { data: allGroups, execute: fetchAppGroups, loading: loadingAppGroups, reset: resetAppGroups } = useLatestPromise(getPermissionAppGroups);
const sortLoading = ref(false);
const isDeleteChecked = ref(false);

// 使用useModalShow管理角色表单弹窗
const [RoleFormModalHolder, showRoleFormModal] = useModalShow(RoleFormModal);

// 防抖搜索函数
const debouncedFetchGroups = useDebounceFn(() => {
  if (!props.appInfo?.id) {
    // 如果没有appId，重置loading状态并清空数据
    resetAppGroups();
    return;
  }

  // 多租户应用必须有tenantId才能获取数据
  if (props.appInfo.isMultiTenant && !tenantId.value) {
    tenantId.value = props.tenantList?.[0]?.id;
    resetAppGroups();
    return;
  }

  const params: { appId: number; tenantId?: number; search?: string } = {
    appId: props.appInfo.id,
  };

  if (props.appInfo.isMultiTenant && tenantId.value) {
    params.tenantId = tenantId.value;
  }

  if (searchKeyword.value) {
    params.search = searchKeyword.value;
  }

  fetchAppGroups(params, {});
}, 300);

watch([() => props.appInfo, () => tenantId.value], ([appInfo]) => {
  // 重置选中的角色，确保项目切换时清空角色选择状态
  activeGroupId.value = undefined;
  // 重置搜索关键词
  searchKeyword.value = undefined;

  if (!appInfo?.id) {
    // 未获取到应用信息
    return;
  }

  // 使用防抖函数获取数据
  debouncedFetchGroups();
}, { immediate: true });

// 监听搜索关键词变化
watch(searchKeyword, () => {
  debouncedFetchGroups();
});

// 自动角色定位逻辑
watch([allGroups, () => props.initialRoleId], ([groups, roleId]) => {
  if (groups?.data?.data && roleId && !activeGroupId.value) {
    const targetRole = groups.data.data.find((g) => g.id === roleId);
    if (targetRole) {
      activeGroupId.value = targetRole.id;
      // 清空 URL 中的 roleId，避免重复触发
      router.replace({
        query: {
          ...route.query,
          roleId: undefined,
        },
      });
    }
  }
}, { immediate: true });

// 检测 createRole 参数，自动打开新增角色弹窗
watch([() => route.query.createRole, () => props.appInfo], ([createRole, appInfo]) => {
  if (!!createRole && appInfo?.id) {
    // 移除 URL 中的 createRole 参数
    router.replace({
      query: {
        ...route.query,
        createRole: undefined,
      },
    });

    // 触发新增角色弹窗
    handleAddRole();
  }
}, { immediate: true });

const activeTab = useRouteQuery('tab', TabKey.Permission, { mode: 'replace' });
const tabList = computed(() => [
  { key: TabKey.Permission, label: '权限配置' },
  { key: TabKey.Member, label: '成员配置' },
]);

// 处理搜索事件
function handleSearch(keyword: string | undefined) {
  searchKeyword.value = keyword;
}

async function handleAddRole() {
  await showRoleFormModal({
    title: '新增角色',
    appInfo: props.appInfo,
    tenantId: tenantId.value,
    allGroups: allGroups.value?.data?.data || [],
    tenantList: props.tenantList,

    async sentReq(formValue) {
      try {
        if (!props.appInfo?.id) {
          return undefined;
        }

        const params = {
          appId: props.appInfo.id,
          tenantId: tenantId.value,
        };
        let createResult;
        if (formValue.fromGroupId) {
          createResult = await copyPermissionGroup(params, { name: formValue.name, fromGroupId: formValue.fromGroupId });
        } else {
          createResult = await createPermissionGroup(params, { name: formValue.name });
        }

        if (createResult.data?.code === 0) {
          const newGroup = createResult.data?.data;

          message.success('提交成功');
          await refreshGroupList();

          // 自动切换到新增的角色
          if (newGroup?.id) {
            activeGroupId.value = newGroup.id;
          }

          return newGroup;
        } else {
          return undefined;
        }
      } catch (error) {
        console.error('操作失败:', error);
        return undefined;
      }
    },
  });
}

async function handleEditRole(group: PermissionAppGroupListItem) {
  await showRoleFormModal({
    title: '修改名称',
    appInfo: props.appInfo,
    tenantId: tenantId.value,
    allGroups: allGroups.value?.data?.data || [],
    tenantList: props.tenantList,
    editGroup: group,

    async sentReq(formValue) {
      try {
        if (!props.appInfo?.id || !group.id) {
          return undefined;
        }

        const params = {
          appId: props.appInfo.id,
          groupId: group.id,
          tenantId: tenantId.value,
        };

        const result = await updatePermissionGroup(params, { name: formValue.name });
        if (result.data?.code === 0) {
          message.success('提交成功');
          await refreshGroupList();
          return result.data?.data;
        } else {
          return undefined;
        }
      } catch (error) {
        console.error('修改名称失败:', error);
        return undefined;
      }
    },
  });
}

function handleDeleteRole(group: PermissionAppGroupListItem) {
  Modal.confirm({
    icon: null,
    width: 496,
    okText: '删除',
    okButtonProps: {
      type: 'primary',
      danger: true,
    },
    cancelButtonProps: {
      // @ts-expect-error cancelButtonProps支持class但没有类型定义
      class: 'btn-fill-default',
    },
    centered: true,
    closable: true,
    title: () => {
      return (
        <div class="flex items-center">
          <WarningErrorIcon class="c-FO-Functional-Error1-Default" />
          <div class="FO-Font-B16 ml-8px"> 删除角色确认 </div>
        </div>
      );
    },
    content() {
      return (
        <div class="mt-12px pb-8px c-FO-Content-Text2">
          删除角色【{ group.name }】，此操作不可恢复，请谨慎操作。
        </div>
      );
    },
    async onOk() {
      try {
        if (!props.appInfo?.id || !group.id) {
          return;
        }

        const params = {
          appId: props.appInfo.id,
          groupId: group.id,
          tenantId: tenantId.value,
        };

        const res = await deletePermissionGroup(params, {});
        if (res.data?.code === 0) {
          message.success('提交成功');

          // 刷新列表
          await refreshGroupList();

          // 如果删除的是当前选中的角色，重新选择第一个角色
          if (activeGroupId.value === group.id) {
            const groups = allGroups.value?.data?.data;
            if (groups && groups.length > 0) {
              activeGroupId.value = groups[0].id;
            } else {
              activeGroupId.value = undefined;
            }
          }
        }
      } catch (error) {
        console.error('删除角色失败:', error);
      }
    },
  });
}

async function handleCopyRole(group: PermissionAppGroupListItem) {
  await showRoleFormModal({
    title: '复制角色',
    isCopy: true,
    appInfo: props.appInfo,
    tenantId: tenantId.value,
    allGroups: allGroups.value?.data?.data || [],
    tenantList: props.tenantList,
    editGroup: { ...group, name: `${group.name} 复制` },

    async sentReq(formValue) {
      try {
        if (!props.appInfo?.id || !group.id) {
          return undefined;
        }

        const params = {
          appId: props.appInfo.id,
          tenantId: tenantId.value,
        };

        const result = await copyPermissionGroup(params, { name: formValue.name, fromGroupId: group.id });

        if (result.data?.code === 0) {
          const newGroup = result.data?.data;

          message.success('提交成功');
          await refreshGroupList();

          // 自动切换到新复制的角色
          if (newGroup?.id) {
            activeGroupId.value = newGroup.id;
          }

          return newGroup;
        } else {
          return undefined;
        }
      } catch (error) {
        console.error('复制角色失败:', error);
        return undefined;
      }
    },
  });
}

function handleSetPublicGroup(group: PermissionAppGroupListItem) {
  if (group.isPublic) {
    isDeleteChecked.value = false;
  }

  Modal.confirm({
    icon: null,
    width: 496,
    okText: group.isPublic ? '解除' : '确认',
    okButtonProps: {
      type: 'primary',
    },
    centered: true,
    closable: true,
    title: () => {
      return (
        <div class="flex items-center">
          <WarningErrorIcon class="c-FO-Functional-Warning1-Default" />
          <div class="FO-Font-B16 ml-8px"> { group.isPublic ? '解除公共角色' : '设为公共角色' } </div>
        </div>
      );
    },
    content() {
      return (
        <div class="mt-12px pb-8px">
          <div class="mb-12px c-FO-Content-Text2">
            {group.isPublic
              ? `解除后，角色【${group.name}】将恢复为项目的自定义角色，支持项目管理员编辑或删除`
              : `设置为公共角色后，角色【${group.name}】将自动添加到所有项目，仅应用管理员可编辑公共角色的权限`}
          </div>
          {group.isPublic
            ? (
              <Checkbox v-model:checked={isDeleteChecked.value}>
                同步删除其他项目中没有配置成员的角色
              </Checkbox>
            )
            : null}
        </div>
      );
    },
    async onOk() {
      try {
        if (!props.appInfo?.id || !group.id) {
          return;
        }

        const params = {
          appId: props.appInfo.id,
          groupId: group.id,
          tenantId: tenantId.value,
        };

        if (group.isPublic) {
          // 取消公共角色
          await cancelPublicGroup(params, { isDelete: isDeleteChecked.value });
        } else {
          // 设置公共角色
          await setPublicGroup(params, {});
        }

        message.success('操作成功');

        // 刷新列表
        await refreshGroupList();
      } catch (error) {
        console.error('设置公共角色失败:', error);
      }
    },
  });
}

// 处理排序
async function handleSortGroup(oldIndex: number, newIndex: number) {
  if (!props.appInfo?.id || !allGroups.value?.data?.data?.length) {
    return;
  }

  sortLoading.value = true;

  try {
    // 获取新的排序
    const groups = allGroups.value.data.data || [];
    const newOrder = [...groups];
    const [movedItem] = newOrder.splice(oldIndex, 1);
    newOrder.splice(newIndex, 0, movedItem);

    const res = await updatePermissionGroupOrder(
      {
        appId: props.appInfo.id,
        tenantId: tenantId.value,
      },
      {
        order: newOrder.map((group) => group.id).filter((id): id is number => id !== undefined),
      },
    );

    if (res.data?.code === 0) {
      await refreshGroupList();
      message.success('操作成功');
    }
  } catch (error) {
    console.error('排序失败:', error);
  } finally {
    sortLoading.value = false;
  }
}

async function refreshGroupList() {
  if (!props.appInfo?.id) {
    return;
  }

  const params: { appId: number; tenantId?: number; search?: string } = {
    appId: props.appInfo.id,
  };

  if (props.appInfo.isMultiTenant && tenantId.value) {
    params.tenantId = tenantId.value;
  }

  if (searchKeyword.value) {
    params.search = searchKeyword.value;
  }

  await fetchAppGroups(params, {});
}
</script>
