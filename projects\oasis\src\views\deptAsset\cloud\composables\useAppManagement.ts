import { message } from 'ant-design-vue';
import { ref } from 'vue';
import { useCloudDeviceStore, useCloudDeviceWebsocketStore } from '../stores';
import type { UploadFileStatus } from 'ant-design-vue/es/upload/interface';
import axios from 'axios';
import {
  getOssUploadMultiPartPreSignedUrl,
  getOssUploadPreSignedUrl,
  mergeOssMultiPart,
} from '../../../../api/page/mtl/device';
import { buildUUID } from '/@/utils/uuid';
import { sendEvent } from '../../../../service/tracker/index.ts';
import { type MtlInstallRecordListItem, MtlInstallRecordSource } from '/@/api/page/mtl/model/deviceModel.ts';

export function useAppManagement() {
  const cloudDeviceStore = useCloudDeviceStore();
  const websocketStore = useCloudDeviceWebsocketStore();
  const uploadLoading = ref(false);
  const upLoadFilePath = ref('');
  const pushPath = ref('');
  const pushLoading = ref(false);
  const cancelTokenSource = ref<ReturnType<typeof axios.CancelToken.source>>();

  // 打开应用
  const openApp = (pkg: string) => {
    const params = cloudDeviceStore.isAndroid
      ? {
        type: 'debug',
        detail: 'openApp',
        pkg,
      }
      : {
        type: 'launch',
        pkg,
      };
    websocketStore.websocketSend(params);
  };

  // 杀死应用
  const killApp = (pkg: string) => {
    const params = cloudDeviceStore.isAndroid
      ? {
        type: 'debug',
        detail: 'killApp',
        pkg,
      }
      : {
        type: 'kill',
        pkg,
      };
    websocketStore.websocketSend(params);
    sendEvent('cloud_device_app_exit', {
      cloud_device_package_name: pkg,
      ...cloudDeviceStore.deviceTrackInfo,
    });
    message.success('停止成功');
  };

  // 卸载应用
  const uninstallApp = (
    pkg: string,
  ) => {
    message.success('开始卸载，请稍后...');
    websocketStore.websocketSend({
      type: 'uninstallApp',
      detail: pkg,
    });
  };

  /**
   * 安装应用
   * @param params 安装参数
   * @param params.val 安装信息
   * @param params.curUuid 当前安装的uuid(仅未安装过需要传入)
   * @param params.area 安装范围(upload只上传，install上传并安装)
   */
  const install = (params: { val: Record<string, any>; curUuid?: string; area?: 'upload' | 'install' }) => {
    const { val, curUuid, area } = params;
    websocketStore.websocketSend({
      type: 'debug',
      detail: 'install',
      uuid: curUuid,
      area,
      ...val,
    });
    if (curUuid) {
      websocketStore.curUploadingUUID = curUuid;
    }
  };

  /**
   * 触发包体中心安装
   * @param params 安装参数
   * @param params.val 安装信息
   * @param params.curUuid 当前安装的uuid(仅未安装过需要传入)
   * @param params.isPkgTest 是否为包体测试
   * @param params.area 安装范围(upload只上传，install上传并安装)
   */
  const selectPackage = (
    params: { val: Record<string, any>; curUuid?: string; isPkgTest?: boolean; area?: 'upload' | 'install' },
  ) => {
    const { val, curUuid, isPkgTest, area } = params;
    websocketStore.websocketSend({
      type: 'debug',
      detail: isPkgTest ? 'packageTest' : 'techCenterInstall',
      area,
      uuid: curUuid,
      ...val,
    });
    if (!isPkgTest) {
      if (curUuid) {
        websocketStore.downloadPercent = 0;
        websocketStore.curUploadingUUID = curUuid;
      }
    }
    websocketStore.curUploadingName = val.name;
  };

  // 超出文件限制提示
  const limitOut = () => {
    message.error('只能添加一个文件，请先移除旧文件');
  };

  function file2Blob(blob: File, callback: (res: Blob) => void) {
    const reader = new FileReader();
    reader.onload = (e) => {
      const fileContent = e.target?.result as ArrayBuffer;
      const newBlob = new Blob([fileContent]);
      callback(newBlob);
    };
    reader.readAsArrayBuffer(blob);
  }

  function file2BlobPromise(file: File): Promise<Blob> {
    return new Promise((resolve, reject) => {
      file2Blob(file, (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Error converting file to Blob'));
        }
      });
    });
  }

  // 分片上传相关常量
  const CHUNK_SIZE = 5 * 1024 * 1024; // 5MB
  const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
  const MAX_CONCURRENT_UPLOADS = 6; // 最大并发上传数

  // 将文件分割成指定大小的片段
  function sliceFile(file: File, chunkSize: number): Blob[] {
    const chunks: Blob[] = [];
    let start = 0;

    while (start < file.size) {
      const end = Math.min(start + chunkSize, file.size);
      chunks.push(file.slice(start, end));
      start = end;
    }

    return chunks;
  }

  // 上传单个分片
  async function uploadChunk(
    chunk: Blob,
    url: string,
    chunkIndex: number,
    cancelToken?: any,
  ): Promise<{ success: boolean; etag?: string; error?: string; status?: UploadFileStatus }> {
    try {
      const response = await axios.put(url, chunk, {
        headers: {
          'Content-Type': null,
        },
        timeout: 20 * 60 * 1000, // 10分钟超时
        cancelToken,
      });

      if (response.status === 200) {
        // 从响应头中获取ETag
        const etag = response.headers.etag || response.headers.ETag;
        return { success: true, etag };
      }
      return { success: false, error: `分片${chunkIndex + 1}上传失败` };
    } catch (error: any) {
      if (axios.isCancel(error)) {
        return { success: false, error: '上传已取消', status: 'removed' as UploadFileStatus };
      }
      return { success: false, error: `分片${chunkIndex + 1}上传失败， ${error.message}` };
    }
  }

  // 并发控制上传分片
  async function uploadChunksWithConcurrency(
    chunks: Blob[],
    urls: string[],
    cancelToken?: any,
    onProgress?: (completedCount: number) => void,
  ): Promise<Array<{ success: boolean; etag?: string; error?: string; partNumber: number }>> {
    const results: Array<{ success: boolean; etag?: string; error?: string; partNumber: number }> = [];
    let completedCount = 0;
    let currentIndex = 0;

    // 创建并发上传的Promise池
    const uploadPromises: Promise<void>[] = [];

    // 上传单个分片的包装函数
    const uploadSingleChunk = async () => {
      while (currentIndex < chunks.length) {
        const index = currentIndex++;
        const chunk = chunks[index];
        const url = urls[index];

        try {
          const result = await uploadChunk(chunk, url, index, cancelToken);
          results[index] = { ...result, partNumber: index + 1 };

          completedCount++;
          if (onProgress) {
            onProgress(completedCount);
          }
        } catch (error: any) {
          results[index] = {
            success: false,
            error: error.message || `分片${index + 1}上传失败`,
            partNumber: index + 1,
          };
          completedCount++;
          if (onProgress) {
            onProgress(completedCount);
          }
        }
      }
    };

    // 创建指定数量的并发上传任务
    Array.from({ length: Math.min(MAX_CONCURRENT_UPLOADS, chunks.length) }).forEach(() => {
      uploadPromises.push(uploadSingleChunk());
    });

    // 等待所有上传完成
    await Promise.all(uploadPromises);

    return results;
  }

  // 分片上传主要逻辑
  async function uploadFileWithChunks(
    file: File,
    fileName: string,
    onProgress?: (percent: number) => void,
    cancelToken?: any,
  ): Promise<{ success: boolean; url?: string; error?: string; status?: UploadFileStatus }> {
    try {
      // 分割文件
      const chunks = sliceFile(file, CHUNK_SIZE);
      const partCount = chunks.length;

      // 获取分片预签名URLs
      const { data: { data: multiPartData } } = await getOssUploadMultiPartPreSignedUrl({
        name: `packageFiles/${fileName}`,
        partCount,
      }, {});

      if (!multiPartData || !multiPartData.urls || !multiPartData.uploadId) {
        return { success: false, error: '获取分片上传URL失败' };
      }

      const { urls, uploadId } = multiPartData;

      // 使用并发控制上传分片
      const results = await uploadChunksWithConcurrency(
        chunks,
        urls,
        cancelToken,
        (completedCount) => {
          if (onProgress) {
            const percent = Math.round((completedCount / chunks.length) * 100);
            onProgress(percent);
          }
        },
      );

      // 检查是否所有分片都上传成功
      const failedChunks = results.filter((r) => !r.success);
      if (failedChunks.length > 0) {
        // 检查是否是用户取消导致的失败
        const canceledChunk = failedChunks.find((chunk) => chunk.error === '上传已取消');
        if (canceledChunk) {
          return { success: false, error: '上传已取消', status: 'removed' as UploadFileStatus };
        }
        return { success: false, error: failedChunks[0].error || '分片上传失败' };
      }

      // 合并分片
      const { data: { data: mergeResult } } = await mergeOssMultiPart({
        name: `packageFiles/${fileName}`,
        uploadId,
      }, {});

      if (mergeResult === 0) {
        return { success: false, error: '分片合并失败' };
      }

      // 从第一个分片URL中提取基础URL
      const baseUrl = urls[0].split('?')[0];
      return {
        success: true,
        url: baseUrl,
      };
    } catch (error: any) {
      if (axios.isCancel(error)) {
        return { success: false, error: '上传已取消', status: 'removed' as UploadFileStatus };
      }
      return { success: false, error: error.message || '分片上传失败' };
    }
  }

  // 上传文件
  const uploadFile = async (content: Record<string, any>) => {
    websocketStore.downloadPercent = 0;
    uploadLoading.value = true;
    cancelTokenSource.value = axios.CancelToken.source();
    const randomName = `${buildUUID()}.${content.file.name.split('.').pop()}`;

    // 记录前端上传开始时间
    const startTime = Date.now();

    try {
      const file = content.file as File;

      // 检查文件大小，决定使用普通上传还是分片上传
      if (file.size > MAX_FILE_SIZE) {
        // 大于100M，使用分片上传
        const result = await uploadFileWithChunks(
          file,
          randomName,
          (percent) => {
            if (content.onProgress) {
              content.onProgress({ percent });
            }
          },
          cancelTokenSource.value?.token,
        );

        if (result.success) {
          // 计算前端上传耗时（秒）
          const frontendUploadDuration = Math.ceil((Date.now() - startTime) / 1000);
          websocketStore.setFrontendUploadTime(frontendUploadDuration);

          return {
            success: true,
            status: 'done' as UploadFileStatus,
            url: result.url,
          };
        } else {
          // 分片上传失败时清理前端时间记录
          websocketStore.clearFrontendUploadTime();

          return {
            success: false,
            status: (result.status || 'error') as UploadFileStatus,
            response: result.error || '上传失败',
          };
        }
      } else {
        // 小于等于100M，使用普通上传
        const { data: { data: signedUrl } } = await getOssUploadPreSignedUrl({
          name: `packageFiles/${randomName}`,
        }, {});

        if (!signedUrl) {
          // 获取预签名URL失败时清理前端时间记录
          websocketStore.clearFrontendUploadTime();
          return { success: false, status: 'error' as UploadFileStatus, response: '上传失败' };
        }

        const blob = await file2BlobPromise(file);
        const response = await axios.put(
          signedUrl,
          blob,
          {
            headers: {
              // 阿里云oss通过预签名url上传文件时，需要删除Content-Type
              'Content-Type': null,
            },
            timeout: 20 * 60 * 1000,
            onUploadProgress: (progressEvent: any) => {
              if (progressEvent.total > 0 && content.onProgress) {
                const percent = Math.round((progressEvent.loaded / progressEvent.total) * 100);
                content.onProgress({ percent });
              }
            },
            cancelToken: cancelTokenSource.value?.token,
          },
        );

        if (response.status === 200) {
          // 计算前端上传耗时（秒）
          const frontendUploadDuration = Math.ceil((Date.now() - startTime) / 1000);
          websocketStore.setFrontendUploadTime(frontendUploadDuration);

          return {
            success: true,
            status: 'done' as UploadFileStatus,
            url: signedUrl.split('?')[0],
          };
        }
        // 普通上传失败时清理前端时间记录
        websocketStore.clearFrontendUploadTime();
        return { success: false, status: 'error' as UploadFileStatus, response: '上传失败' };
      }
    } catch (error: any) {
      // 上传失败或取消时清理前端时间记录
      websocketStore.clearFrontendUploadTime();

      if (axios.isCancel(error)) {
        return { success: false, status: 'removed' as UploadFileStatus, response: '上传已取消' };
      }
      return {
        success: false,
        status: 'error' as UploadFileStatus,
        response: error?.message || '上传失败',
      };
    } finally {
      uploadLoading.value = false;
      cancelTokenSource.value = undefined;
    }
  };

  // 取消上传
  const cancelUpload = () => {
    if (cancelTokenSource.value) {
      cancelTokenSource.value.cancel();
      cancelTokenSource.value = undefined;
      uploadLoading.value = false;
      return true;
    }
    return false;
  };

  const cancelInstall = async (uuid?: string) => {
    websocketStore.websocketSend({
      type: 'cancelInstall',
      uuid,
    });
  };

  // 推送文件
  const pushFile = () => {
    pushLoading.value = true;
    websocketStore.websocketSend({
      type: 'pushFile',
      file: upLoadFilePath.value,
      path: pushPath.value,
    });
  };

  // 拉取文件相关
  const pullPath = ref('');
  const pullLoading = ref(false);
  const pullResult = ref('');

  // 拉取文件
  const pullFile = () => {
    pullResult.value = '';
    pullLoading.value = true;
    websocketStore.websocketSend({
      type: 'pullFile',
      path: pullPath.value,
    });
  };

  // 扫描
  const scan = (url: string) => {
    websocketStore.websocketSend({
      type: 'scan',
      url,
    });
  };

  function handleInstall(app: MtlInstallRecordListItem) {
    const curRecentApp = websocketStore.recentApps.find((item) => item.uuid === app.uuid);
    if (curRecentApp?.isInstalling || !curRecentApp) {
      return;
    }
    websocketStore.isUserStopUpload = false;
    const downloadInfo = curRecentApp.downloadInfo ? JSON.parse(curRecentApp.downloadInfo) : {};
    curRecentApp.isInstalling = true;
    websocketStore.curInstallingApp = curRecentApp;
    websocketStore.installStatus = undefined;
    websocketStore.installPercent = 0;
    message.success('开始安装，请稍后...');
    if (curRecentApp.source === MtlInstallRecordSource.PackageCenter) {
      selectPackage({
        val: {
          uuid: curRecentApp.uuid,
          ...downloadInfo,
        },
        area: 'install',
      });
    } else {
      install({
        val: {
          uuid: curRecentApp.uuid,
          ...downloadInfo,
        },
        area: 'install',
      });
    }
  }

  function handleCancelInstall() {
    if (websocketStore.installStatus === 'installing') {
      return;
    }
    websocketStore.isUserStopUpload = true;
    cancelInstall(websocketStore.curInstallingApp?.uuid);
    message.info('安装已取消');
    websocketStore.curInstallingApp = undefined;
    websocketStore.installStatus = undefined;
    websocketStore.installPercent = 0;
    websocketStore.getRecentApps();
  }
  return {
    uploadLoading,
    upLoadFilePath,
    pushPath,
    pushLoading,
    pullPath,
    pullLoading,
    pullResult,
    openApp,
    killApp,
    uninstallApp,
    install,
    selectPackage,
    limitOut,
    uploadFile,
    cancelUpload,
    cancelInstall,
    pushFile,
    pullFile,
    scan,
    handleInstall,
    handleCancelInstall,
  };
}
