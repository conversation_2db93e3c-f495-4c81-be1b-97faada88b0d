import type { PermissionItem, PermissionRoleItem } from '../../api/query.ts';
import type { OrgStructureType } from '../../api/group.ts';

/** 成员统计信息 */
export interface MemberCountInfo {
  /** 用户数 */
  users: number;
  /** 部门数 */
  departments: number;
  /** 组数 */
  groups: number;
}

/** 权限查询结果行数据 */
export type QueryPermissionRow = {
  /** 节点ID */
  id: number | string;
  /** 唯一标识键，用于VXETable的rowField */
  uniqueKey: string;
  /** 节点名称 */
  name: string;
  /** 节点类型 */
  type: 'category' | 'permission';
} & (
  | {
    /** 分类节点 */
    type: 'category';
    /** 子节点数量 */
    childCount: number;
    /** 成员统计信息 */
    memberCountInfo?: MemberCountInfo;
    /** 分类原始数据 */
    categoryData?: PermissionCategory;
  }
  | {
    /** 权限节点 */
    type: 'permission';
    /** 父节点唯一键，用于建立树形关系（非多租户模式下可选） */
    parentUniqueKey?: string;
    /** 所属分类ID */
    categoryId: number | string;
    /** 权限点Code */
    code: string;
    /** 权限点说明 */
    description: string;
    /** 是否启用 */
    isEnabled: boolean;
    /** 所属角色列表 */
    roles: PermissionRoleItem[];
    /** 原始权限数据 */
    rawData: PermissionItem;
    /** 资源ID（用于获取成员数据） */
    resourceId?: string;
    /** 组织类型（用于判断是否需要显示成员查看图标） */
    orgType?: OrgStructureType;
    /** 外包标识（仅对 Member 类型有效） */
    outsourceFlag?: boolean;
  }
);

/** 权限分类信息 */
export interface PermissionCategory {
  /** 分类显示名称 */
  label: string;
  /** 分类值 */
  value: string;
  /** 该分类下的权限数量 */
  count: number;
}

/** 权限查询结果统计信息 */
export interface PermissionQueryStats {
  /** 总权限数 */
  totalCount: number;
  /** 已开启权限数 */
  enabledCount: number;
  /** 角色数量 */
  roleCount: number;
  /** 分类数量 */
  categoryCount: number;
}

/** 项目树节点（多租户场景） */
export interface ProjectTreeNode {
  id: string;
  name: string;
  type: 'project';
  tenantId: string;
  permissionCount: number;
  children: CategoryTreeNode[];
}

/** 分类树节点 */
export interface CategoryTreeNode {
  id: string;
  name: string;
  type: 'category';
  childCount: number;
  children: PermissionTreeNode[];
}

/** 权限树节点 */
export interface PermissionTreeNode extends Omit<QueryPermissionRow, 'id' | 'type'> {
  id: string;
  type: 'permission';
}

/** 树形表格节点联合类型 */
export type TreeTableNode = ProjectTreeNode | CategoryTreeNode | PermissionTreeNode;

/** 已配置角色信息（用于 ConfiguredRolesDrawer） */
export interface ConfiguredRoleInfo {
  roleId: number;
  roleName: string;
  projectName?: string;
  tenantId?: number;
  memberCount: number;
  members: string[];
}
