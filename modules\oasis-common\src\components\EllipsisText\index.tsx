import { useDebounceFn, useElementVisibility, useResizeObserver } from '@vueuse/core';
import { type TooltipProps, Tooltip } from 'ant-design-vue';
import { type PropType, computed, defineComponent, ref, watch } from 'vue';

interface EllipsisTextProps {
  inline?: boolean;
  // 行数
  lines?: string | number;
  // tooltip props
  tooltipProps?: Partial<TooltipProps>;
}

const EllipsisText = defineComponent<EllipsisTextProps>({
  props: {
    lines: {
      type: [String, Number] as PropType<string | number>,
    },
    tooltipProps: {
      type: Object as PropType<Partial<TooltipProps>>,
      default: () => ({}),
    },
  },
  setup(props, { slots }) {
    const renderLines = computed(() => {
      const v = Math.floor(Number(props.lines));
      return v > 0 ? v : 1;
    });
    const tooltipVisible = ref(false);
    const textRef = ref<HTMLSpanElement>();
    const textShow = useElementVisibility(textRef);

    const updateVisible = useDebounceFn(() => {
      if (!textRef.value || !textShow.value) {
        return;
      }
      tooltipVisible.value = textRef.value.scrollHeight > textRef.value.clientHeight || textRef.value.scrollWidth > textRef.value.clientWidth;
    }, 50);
    watch([textRef, textShow], updateVisible);
    useResizeObserver(textRef, updateVisible);

    return () => {
      const textElement = (
        <span
          class={[
            'inline-block',
            'max-w-full',
            'overflow-hidden',
            { truncate: renderLines.value < 2 }, // fallback
          ]}
          ref={textRef}
          style={{
            'display': '-webkit-box',
            '-webkit-box-orient': 'vertical',
            '-webkit-line-clamp': renderLines.value,
            'line-clamp': renderLines.value,
            'word-break': 'break-all',
            'white-space': 'wrap',
          }}
        >
          {slots.default?.()}
        </span>
      );

      // 只在 tooltipVisible 为 true 时渲染 Tooltip
      if (tooltipVisible.value) {
        return (
          <Tooltip {...props.tooltipProps}>
            {{
              title: () => slots.title?.() || slots.default?.(),
              default: () => textElement,
            }}
          </Tooltip>
        );
      }

      // tooltipVisible 为 false 时直接返回文本元素
      return textElement;
    };
  },
});

export {
  EllipsisText,
};
