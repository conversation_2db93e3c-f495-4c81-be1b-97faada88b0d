/* This file is automatically generated. Do not edit it manually.  */
import { ColorPresetToken } from './plain';

export enum ForgeonTheme {
  Light = 'light',
  Dark = 'dark',
}

/**
 * 使用 ForgeonTheme 获取到实际的变量值
 */
export const ForgeonThemeMap = {
  [ForgeonTheme.Light]: {
    BrandPrimaryDefault: ColorPresetToken.LightViolet6,
    BrandPrimaryHover: ColorPresetToken.LightViolet5,
    BrandPrimaryActive: ColorPresetToken.LightViolet7,
    BrandPrimaryDisabled: ColorPresetToken.LightViolet3,
    BrandSecondaryDefault: ColorPresetToken.LightViolet1,
    BrandSecondaryHover: ColorPresetToken.LightViolet2,
    BrandSecondaryActive: ColorPresetToken.LightViolet3,
    BrandSecondaryDisabled: ColorPresetToken.LightViolet1,
    BrandTertiaryActive: ColorPresetToken.LightViolet1,
    FunctionalSuccess1Default: ColorPresetToken.LightGreen6,
    FunctionalSuccess1Hover: ColorPresetToken.LightGreen5,
    FunctionalSuccess1Active: ColorPresetToken.LightGreen7,
    FunctionalSuccess1Disabled: ColorPresetToken.LightGreen3,
    FunctionalSuccess2Default: ColorPresetToken.LightGreen1,
    FunctionalSuccess2Hover: ColorPresetToken.LightGreen2,
    FunctionalSuccess2Active: ColorPresetToken.LightGreen3,
    FunctionalSuccess2Disabled: ColorPresetToken.LightGreen1,
    FunctionalWarning1Default: ColorPresetToken.LightOrange6,
    FunctionalWarning1Hover: ColorPresetToken.LightOrange5,
    FunctionalWarning1Active: ColorPresetToken.LightOrange7,
    FunctionalWarning1Disabled: ColorPresetToken.LightOrange3,
    FunctionalWarning2Default: ColorPresetToken.LightOrange1,
    FunctionalWarning2Hover: ColorPresetToken.LightOrange2,
    FunctionalWarning2Active: ColorPresetToken.LightOrange3,
    FunctionalWarning2Disabled: ColorPresetToken.LightOrange1,
    FunctionalError1Default: ColorPresetToken.LightRed6,
    FunctionalError1Hover: ColorPresetToken.LightRed5,
    FunctionalError1Active: ColorPresetToken.LightRed7,
    FunctionalError1Disabled: ColorPresetToken.LightRed3,
    FunctionalError2Default: ColorPresetToken.LightRed1,
    FunctionalError2Hover: ColorPresetToken.LightRed2,
    FunctionalError2Active: ColorPresetToken.LightRed3,
    FunctionalError2Disabled: ColorPresetToken.LightRed1,
    FunctionalInfo1Default: ColorPresetToken.LightBlue6,
    FunctionalInfo1Hover: ColorPresetToken.LightBlue5,
    FunctionalInfo1Active: ColorPresetToken.LightBlue7,
    FunctionalInfo1Disabled: ColorPresetToken.LightBlue3,
    FunctionalInfo2Default: ColorPresetToken.LightBlue1,
    FunctionalInfo2Hover: ColorPresetToken.LightBlue2,
    FunctionalInfo2Active: ColorPresetToken.LightBlue3,
    FunctionalInfo2Disabled: ColorPresetToken.LightBlue1,
    ContentText0: ColorPresetToken.BasicWhite100,
    ContentText1: ColorPresetToken.LightGray15,
    ContentText2: ColorPresetToken.LightGray11,
    ContentText3: ColorPresetToken.LightGray8,
    ContentText4: ColorPresetToken.LightGray6,
    ContentIcon0: ColorPresetToken.BasicWhite100,
    ContentIcon1: ColorPresetToken.LightGray14,
    ContentIcon2: ColorPresetToken.LightGray10,
    ContentIcon3: ColorPresetToken.LightGray7,
    ContentIcon4: ColorPresetToken.LightGray5,
    ContentComponents1: ColorPresetToken.LightGray0,
    ContentComponents2: ColorPresetToken.LightGray0,
    ContentLinkDefault: ColorPresetToken.LightBlue6,
    ContentLinkHover: ColorPresetToken.LightBlue5,
    ContentLinkActive: ColorPresetToken.LightBlue7,
    ContentLinkDisabled: ColorPresetToken.LightBlue3,
    ContainerMask0: ColorPresetToken.BasicWhite60,
    ContainerMask1: ColorPresetToken.BasicBlack40,
    ContainerBackground: ColorPresetToken.LightGray1,
    ContainerBackground2: ColorPresetToken.BasicWhite80,
    ContainerFill0: ColorPresetToken.BasicWhite0,
    ContainerFill1: ColorPresetToken.LightGray0,
    ContainerFill2: ColorPresetToken.LightGray1,
    ContainerFill3: ColorPresetToken.LightGray2,
    ContainerFill4: ColorPresetToken.LightGray3,
    ContainerFill5: ColorPresetToken.LightGray4,
    ContainerFill6: ColorPresetToken.DarkGray1,
    ContainerStroke0: ColorPresetToken.BasicWhite100,
    ContainerStroke1: ColorPresetToken.LightGray3,
    ContainerStroke2: ColorPresetToken.LightGray4,
    ContainerStroke3: ColorPresetToken.LightGray5,
    ContainerStroke4: ColorPresetToken.LightGray6,
    ContainerStroke5: ColorPresetToken.LightGray7,
    DatavisViolet1: ColorPresetToken.LightViolet6,
    DatavisViolet2: ColorPresetToken.LightViolet4,
    DatavisViolet3: ColorPresetToken.LightViolet1,
    DatavisBlue1: ColorPresetToken.LightBlue6,
    DatavisBlue2: ColorPresetToken.LightBlue4,
    DatavisBlue3: ColorPresetToken.LightBlue1,
    DatavisLightblue1: ColorPresetToken.LightLightblue7,
    DatavisLightblue2: ColorPresetToken.LightLightblue5,
    DatavisLightblue3: ColorPresetToken.LightLightblue1,
    DatavisTeal1: ColorPresetToken.LightTeal7,
    DatavisTeal2: ColorPresetToken.LightTeal5,
    DatavisTeal3: ColorPresetToken.LightTeal1,
    DatavisGreen1: ColorPresetToken.LightGreen7,
    DatavisGreen2: ColorPresetToken.LightGreen4,
    DatavisGreen3: ColorPresetToken.LightGreen1,
    DatavisLightgreen1: ColorPresetToken.LightLightgreen7,
    DatavisLightgreen2: ColorPresetToken.LightLightgreen4,
    DatavisLightgreen3: ColorPresetToken.LightLightgreen1,
    DatavisYellow1: ColorPresetToken.LightYellow8,
    DatavisYellow2: ColorPresetToken.LightYellow7,
    DatavisYellow3: ColorPresetToken.LightYellow1,
    DatavisOrange1: ColorPresetToken.LightOrange7,
    DatavisOrange2: ColorPresetToken.LightOrange5,
    DatavisOrange3: ColorPresetToken.LightOrange1,
    DatavisRed1: ColorPresetToken.LightRed6,
    DatavisRed2: ColorPresetToken.LightRed4,
    DatavisRed3: ColorPresetToken.LightRed1,
    DatavisPink1: ColorPresetToken.LightPink6,
    DatavisPink2: ColorPresetToken.LightPink4,
    DatavisPink3: ColorPresetToken.LightPink1,
    DatavisPurple1: ColorPresetToken.LightPurple6,
    DatavisPurple2: ColorPresetToken.LightPurple4,
    DatavisPurple3: ColorPresetToken.LightPurple1,
  },
  [ForgeonTheme.Dark]: {
    BrandPrimaryDefault: ColorPresetToken.DarkViolet6,
    BrandPrimaryHover: ColorPresetToken.DarkViolet5,
    BrandPrimaryActive: ColorPresetToken.DarkViolet4,
    BrandPrimaryDisabled: ColorPresetToken.DarkViolet4,
    BrandSecondaryDefault: ColorPresetToken.DarkViolet1,
    BrandSecondaryHover: ColorPresetToken.DarkViolet2,
    BrandSecondaryActive: ColorPresetToken.DarkViolet3,
    BrandSecondaryDisabled: ColorPresetToken.DarkViolet1,
    BrandTertiaryActive: ColorPresetToken.DarkViolet1,
    FunctionalSuccess1Default: ColorPresetToken.DarkGreen6,
    FunctionalSuccess1Hover: ColorPresetToken.DarkGreen5,
    FunctionalSuccess1Active: ColorPresetToken.DarkGreen4,
    FunctionalSuccess1Disabled: ColorPresetToken.DarkGreen2,
    FunctionalSuccess2Default: ColorPresetToken.DarkGreen2,
    FunctionalSuccess2Hover: ColorPresetToken.DarkGreen3,
    FunctionalSuccess2Active: ColorPresetToken.DarkGreen4,
    FunctionalSuccess2Disabled: ColorPresetToken.DarkGreen2,
    FunctionalWarning1Default: ColorPresetToken.DarkOrange6,
    FunctionalWarning1Hover: ColorPresetToken.DarkOrange5,
    FunctionalWarning1Active: ColorPresetToken.DarkOrange4,
    FunctionalWarning1Disabled: ColorPresetToken.DarkOrange2,
    FunctionalWarning2Default: ColorPresetToken.DarkOrange1,
    FunctionalWarning2Hover: ColorPresetToken.DarkOrange3,
    FunctionalWarning2Active: ColorPresetToken.DarkOrange4,
    FunctionalWarning2Disabled: ColorPresetToken.DarkOrange2,
    FunctionalError1Default: ColorPresetToken.DarkRed6,
    FunctionalError1Hover: ColorPresetToken.DarkRed5,
    FunctionalError1Active: ColorPresetToken.DarkRed4,
    FunctionalError1Disabled: ColorPresetToken.DarkRed2,
    FunctionalError2Default: ColorPresetToken.DarkRed1,
    FunctionalError2Hover: ColorPresetToken.DarkRed3,
    FunctionalError2Active: ColorPresetToken.DarkRed4,
    FunctionalError2Disabled: ColorPresetToken.DarkRed2,
    FunctionalInfo1Default: ColorPresetToken.DarkBlue6,
    FunctionalInfo1Hover: ColorPresetToken.DarkBlue5,
    FunctionalInfo1Active: ColorPresetToken.DarkBlue4,
    FunctionalInfo1Disabled: ColorPresetToken.DarkBlue2,
    FunctionalInfo2Default: ColorPresetToken.DarkBlue1,
    FunctionalInfo2Hover: ColorPresetToken.DarkBlue3,
    FunctionalInfo2Active: ColorPresetToken.DarkBlue4,
    FunctionalInfo2Disabled: ColorPresetToken.DarkBlue1,
    ContentText0: ColorPresetToken.DarkGray1,
    ContentText1: ColorPresetToken.DarkGray15,
    ContentText2: ColorPresetToken.DarkGray10,
    ContentText3: ColorPresetToken.DarkGray7,
    ContentText4: ColorPresetToken.DarkGray5,
    ContentIcon0: ColorPresetToken.DarkGray1,
    ContentIcon1: ColorPresetToken.DarkGray14,
    ContentIcon2: ColorPresetToken.DarkGray9,
    ContentIcon3: ColorPresetToken.DarkGray6,
    ContentIcon4: ColorPresetToken.DarkGray5,
    ContentComponents1: ColorPresetToken.DarkGray16,
    ContentComponents2: ColorPresetToken.DarkGray11,
    ContentLinkDefault: ColorPresetToken.DarkBlue6,
    ContentLinkHover: ColorPresetToken.DarkBlue5,
    ContentLinkActive: ColorPresetToken.DarkBlue4,
    ContentLinkDisabled: ColorPresetToken.DarkBlue2,
    ContainerMask0: ColorPresetToken.BasicWhite60,
    ContainerMask1: ColorPresetToken.BasicBlack40,
    ContainerBackground: ColorPresetToken.DarkGray0,
    ContainerBackground2: ColorPresetToken.BasicBlack50,
    ContainerFill0: ColorPresetToken.BasicBlack0,
    ContainerFill1: ColorPresetToken.DarkGray1,
    ContainerFill2: ColorPresetToken.DarkGray2,
    ContainerFill3: ColorPresetToken.DarkGray3,
    ContainerFill4: ColorPresetToken.DarkGray4,
    ContainerFill5: ColorPresetToken.DarkGray5,
    ContainerFill6: ColorPresetToken.DarkGray0,
    ContainerStroke0: ColorPresetToken.DarkGray2,
    ContainerStroke1: ColorPresetToken.DarkGray3,
    ContainerStroke2: ColorPresetToken.DarkGray4,
    ContainerStroke3: ColorPresetToken.DarkGray6,
    ContainerStroke4: ColorPresetToken.DarkGray7,
    ContainerStroke5: ColorPresetToken.DarkGray6,
    DatavisViolet1: ColorPresetToken.DarkViolet7,
    DatavisViolet2: ColorPresetToken.DarkViolet4,
    DatavisViolet3: ColorPresetToken.DarkViolet1,
    DatavisBlue1: ColorPresetToken.DarkBlue7,
    DatavisBlue2: ColorPresetToken.DarkBlue4,
    DatavisBlue3: ColorPresetToken.DarkBlue1,
    DatavisLightblue1: ColorPresetToken.DarkLightblue6,
    DatavisLightblue2: ColorPresetToken.DarkLightblue3,
    DatavisLightblue3: ColorPresetToken.DarkLightblue1,
    DatavisTeal1: ColorPresetToken.DarkTeal6,
    DatavisTeal2: ColorPresetToken.DarkTeal3,
    DatavisTeal3: ColorPresetToken.DarkTeal1,
    DatavisGreen1: ColorPresetToken.DarkGreen6,
    DatavisGreen2: ColorPresetToken.DarkGreen3,
    DatavisGreen3: ColorPresetToken.DarkGreen1,
    DatavisLightgreen1: ColorPresetToken.DarkLightgreen6,
    DatavisLightgreen2: ColorPresetToken.DarkLightgreen3,
    DatavisLightgreen3: ColorPresetToken.DarkLightgreen1,
    DatavisYellow1: ColorPresetToken.DarkYellow5,
    DatavisYellow2: ColorPresetToken.DarkYellow3,
    DatavisYellow3: ColorPresetToken.DarkYellow1,
    DatavisOrange1: ColorPresetToken.DarkOrange6,
    DatavisOrange2: ColorPresetToken.DarkOrange4,
    DatavisOrange3: ColorPresetToken.DarkOrange1,
    DatavisRed1: ColorPresetToken.DarkRed7,
    DatavisRed2: ColorPresetToken.DarkRed4,
    DatavisRed3: ColorPresetToken.DarkRed1,
    DatavisPink1: ColorPresetToken.DarkPink7,
    DatavisPink2: ColorPresetToken.DarkPink4,
    DatavisPink3: ColorPresetToken.DarkPink1,
    DatavisPurple1: ColorPresetToken.DarkPurple7,
    DatavisPurple2: ColorPresetToken.DarkPurple4,
    DatavisPurple3: ColorPresetToken.DarkPurple1,
  },
};

/**
 * 映射到对应的 CSS 变量值
 * @example ForgeonThemeCssVar.BrandPrimaryDefault == 'var(--FO-Brand-Primary-Default)'
 */
export const ForgeonThemeCssVar = {
  BrandPrimaryDefault: 'var(--FO-Brand-Primary-Default)',
  BrandPrimaryHover: 'var(--FO-Brand-Primary-Hover)',
  BrandPrimaryActive: 'var(--FO-Brand-Primary-Active)',
  BrandPrimaryDisabled: 'var(--FO-Brand-Primary-Disabled)',
  BrandSecondaryDefault: 'var(--FO-Brand-Secondary-Default)',
  BrandSecondaryHover: 'var(--FO-Brand-Secondary-Hover)',
  BrandSecondaryActive: 'var(--FO-Brand-Secondary-Active)',
  BrandSecondaryDisabled: 'var(--FO-Brand-Secondary-Disabled)',
  BrandTertiaryActive: 'var(--FO-Brand-Tertiary-Active)',
  FunctionalSuccess1Default: 'var(--FO-Functional-Success1-Default)',
  FunctionalSuccess1Hover: 'var(--FO-Functional-Success1-Hover)',
  FunctionalSuccess1Active: 'var(--FO-Functional-Success1-Active)',
  FunctionalSuccess1Disabled: 'var(--FO-Functional-Success1-Disabled)',
  FunctionalSuccess2Default: 'var(--FO-Functional-Success2-Default)',
  FunctionalSuccess2Hover: 'var(--FO-Functional-Success2-Hover)',
  FunctionalSuccess2Active: 'var(--FO-Functional-Success2-Active)',
  FunctionalSuccess2Disabled: 'var(--FO-Functional-Success2-Disabled)',
  FunctionalWarning1Default: 'var(--FO-Functional-Warning1-Default)',
  FunctionalWarning1Hover: 'var(--FO-Functional-Warning1-Hover)',
  FunctionalWarning1Active: 'var(--FO-Functional-Warning1-Active)',
  FunctionalWarning1Disabled: 'var(--FO-Functional-Warning1-Disabled)',
  FunctionalWarning2Default: 'var(--FO-Functional-Warning2-Default)',
  FunctionalWarning2Hover: 'var(--FO-Functional-Warning2-Hover)',
  FunctionalWarning2Active: 'var(--FO-Functional-Warning2-Active)',
  FunctionalWarning2Disabled: 'var(--FO-Functional-Warning2-Disabled)',
  FunctionalError1Default: 'var(--FO-Functional-Error1-Default)',
  FunctionalError1Hover: 'var(--FO-Functional-Error1-Hover)',
  FunctionalError1Active: 'var(--FO-Functional-Error1-Active)',
  FunctionalError1Disabled: 'var(--FO-Functional-Error1-Disabled)',
  FunctionalError2Default: 'var(--FO-Functional-Error2-Default)',
  FunctionalError2Hover: 'var(--FO-Functional-Error2-Hover)',
  FunctionalError2Active: 'var(--FO-Functional-Error2-Active)',
  FunctionalError2Disabled: 'var(--FO-Functional-Error2-Disabled)',
  FunctionalInfo1Default: 'var(--FO-Functional-Info1-Default)',
  FunctionalInfo1Hover: 'var(--FO-Functional-Info1-Hover)',
  FunctionalInfo1Active: 'var(--FO-Functional-Info1-Active)',
  FunctionalInfo1Disabled: 'var(--FO-Functional-Info1-Disabled)',
  FunctionalInfo2Default: 'var(--FO-Functional-Info2-Default)',
  FunctionalInfo2Hover: 'var(--FO-Functional-Info2-Hover)',
  FunctionalInfo2Active: 'var(--FO-Functional-Info2-Active)',
  FunctionalInfo2Disabled: 'var(--FO-Functional-Info2-Disabled)',
  ContentText0: 'var(--FO-Content-Text0)',
  ContentText1: 'var(--FO-Content-Text1)',
  ContentText2: 'var(--FO-Content-Text2)',
  ContentText3: 'var(--FO-Content-Text3)',
  ContentText4: 'var(--FO-Content-Text4)',
  ContentIcon0: 'var(--FO-Content-Icon0)',
  ContentIcon1: 'var(--FO-Content-Icon1)',
  ContentIcon2: 'var(--FO-Content-Icon2)',
  ContentIcon3: 'var(--FO-Content-Icon3)',
  ContentIcon4: 'var(--FO-Content-Icon4)',
  ContentComponents1: 'var(--FO-Content-Components1)',
  ContentComponents2: 'var(--FO-Content-Components2)',
  ContentLinkDefault: 'var(--FO-Content-Link-Default)',
  ContentLinkHover: 'var(--FO-Content-Link-Hover)',
  ContentLinkActive: 'var(--FO-Content-Link-Active)',
  ContentLinkDisabled: 'var(--FO-Content-Link-Disabled)',
  ContainerMask0: 'var(--FO-Container-Mask0)',
  ContainerMask1: 'var(--FO-Container-Mask1)',
  ContainerBackground: 'var(--FO-Container-Background)',
  ContainerBackground2: 'var(--FO-Container-Background2)',
  ContainerFill0: 'var(--FO-Container-Fill0)',
  ContainerFill1: 'var(--FO-Container-Fill1)',
  ContainerFill2: 'var(--FO-Container-Fill2)',
  ContainerFill3: 'var(--FO-Container-Fill3)',
  ContainerFill4: 'var(--FO-Container-Fill4)',
  ContainerFill5: 'var(--FO-Container-Fill5)',
  ContainerFill6: 'var(--FO-Container-Fill6)',
  ContainerStroke0: 'var(--FO-Container-Stroke0)',
  ContainerStroke1: 'var(--FO-Container-Stroke1)',
  ContainerStroke2: 'var(--FO-Container-Stroke2)',
  ContainerStroke3: 'var(--FO-Container-Stroke3)',
  ContainerStroke4: 'var(--FO-Container-Stroke4)',
  ContainerStroke5: 'var(--FO-Container-Stroke5)',
  DatavisViolet1: 'var(--FO-Datavis-Violet1)',
  DatavisViolet2: 'var(--FO-Datavis-Violet2)',
  DatavisViolet3: 'var(--FO-Datavis-Violet3)',
  DatavisBlue1: 'var(--FO-Datavis-Blue1)',
  DatavisBlue2: 'var(--FO-Datavis-Blue2)',
  DatavisBlue3: 'var(--FO-Datavis-Blue3)',
  DatavisLightblue1: 'var(--FO-Datavis-Lightblue1)',
  DatavisLightblue2: 'var(--FO-Datavis-Lightblue2)',
  DatavisLightblue3: 'var(--FO-Datavis-Lightblue3)',
  DatavisTeal1: 'var(--FO-Datavis-Teal1)',
  DatavisTeal2: 'var(--FO-Datavis-Teal2)',
  DatavisTeal3: 'var(--FO-Datavis-Teal3)',
  DatavisGreen1: 'var(--FO-Datavis-Green1)',
  DatavisGreen2: 'var(--FO-Datavis-Green2)',
  DatavisGreen3: 'var(--FO-Datavis-Green3)',
  DatavisLightgreen1: 'var(--FO-Datavis-Lightgreen1)',
  DatavisLightgreen2: 'var(--FO-Datavis-Lightgreen2)',
  DatavisLightgreen3: 'var(--FO-Datavis-Lightgreen3)',
  DatavisYellow1: 'var(--FO-Datavis-Yellow1)',
  DatavisYellow2: 'var(--FO-Datavis-Yellow2)',
  DatavisYellow3: 'var(--FO-Datavis-Yellow3)',
  DatavisOrange1: 'var(--FO-Datavis-Orange1)',
  DatavisOrange2: 'var(--FO-Datavis-Orange2)',
  DatavisOrange3: 'var(--FO-Datavis-Orange3)',
  DatavisRed1: 'var(--FO-Datavis-Red1)',
  DatavisRed2: 'var(--FO-Datavis-Red2)',
  DatavisRed3: 'var(--FO-Datavis-Red3)',
  DatavisPink1: 'var(--FO-Datavis-Pink1)',
  DatavisPink2: 'var(--FO-Datavis-Pink2)',
  DatavisPink3: 'var(--FO-Datavis-Pink3)',
  DatavisPurple1: 'var(--FO-Datavis-Purple1)',
  DatavisPurple2: 'var(--FO-Datavis-Purple2)',
  DatavisPurple3: 'var(--FO-Datavis-Purple3)',
};

export type ForgeonThemeTokens = keyof typeof ForgeonThemeCssVar;
