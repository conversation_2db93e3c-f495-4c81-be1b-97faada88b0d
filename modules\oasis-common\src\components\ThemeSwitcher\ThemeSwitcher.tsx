import { Tooltip } from 'ant-design-vue';
import { match, P } from 'ts-pattern';
import { ForgeonTheme } from '@hg-tech/forgeon-style';
import { type PropType, defineComponent } from 'vue';
import MoonIcon from '../../assets/svg/component/theme-switch-moon.svg?component';
import SunIcon from '../../assets/svg/component/theme-switch-sun.svg?component';
import Icon from '@ant-design/icons-vue';
import type { TooltipPlacement } from 'ant-design-vue/es/tooltip';
import { useViewTransitionClick } from './useViewTransitionClick';

export const ThemeSwitcher = defineComponent({
  props: {
    theme: {
      type: String as PropType<ForgeonTheme>,
    },
    onToggle: {
      type: Function as PropType<(theme: ForgeonTheme) => void>,
      required: true,
    },
    placement: {
      type: String as PropType<TooltipPlacement>,
      default: 'top',
    },
    position: {
      type: String as PropType<'left' | 'right' | 'none'>,
      default: 'right',
    },
    /**
     * 一些额外的样式配置
     */
    btnStyle: {
      type: String as PropType<'icon' | 'circle'>,
      default: () => 'circle',
    },
  },
  setup(props) {
    const handleToggleTheme = useViewTransitionClick(() => {
      props.onToggle(props.theme === ForgeonTheme.Dark ? ForgeonTheme.Light : ForgeonTheme.Dark);
    });

    return () => (
      <Tooltip placement={props.placement} title={`切换至${props.theme === ForgeonTheme.Dark ? '亮色' : '暗色'}模式`}>
        <div
          class={[
            'flex cursor-pointer items-center justify-center',
            `${match(props.btnStyle)
              .with('circle', () => 'p-10px rd-full bg-FO-Brand-Primary-Default hover:bg-FO-Brand-Primary-Hover')
              .with(P._, () => '')
              .exhaustive()
            }`,
            'hover:translate-x-0% transition-all',
            `${match(props.position)
              .with('left', () => 'translate-x--50%')
              .with('right', () => 'translate-x-50%')
              .with(P._, () => '')
              .exhaustive()
            }`,
          ]}
          onClick={handleToggleTheme}
        >
          <Icon
            class="theme-switch-icon"
            component={props.theme === ForgeonTheme.Dark ? <SunIcon /> : <MoonIcon />}
          />
        </div>
      </Tooltip>
    );
  },
});
