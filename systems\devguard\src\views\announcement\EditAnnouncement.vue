<template>
  <div class="mt-10px">
    <Form :labelCol="labelCol" :wrapperCol="wrapperCol" :colon="false">
      <FormItem label="公告内容" v-bind="validateInfos.content">
        <Input v-model:value="modelRef.content" />
      </FormItem>
      <FormItem label="触发类型" v-bind="validateInfos.triggerType">
        <RadioGroup v-model:value="modelRef.triggerType">
          <Radio :value="triggerType.Once">
            触发单次
          </Radio>
          <Radio :value="triggerType.Periodic">
            周期触发
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem v-if="modelRef.triggerType === triggerType.Once" label="持续时间" v-bind="validateInfos.onceDuration">
        <RangePicker v-model:value="modelRef.onceDuration" showTime class="w-full" />
      </FormItem>
      <FormItem v-if="modelRef.triggerType === triggerType.Periodic" label="持续时间" v-bind="validateInfos.periodicDuration">
        <RangePicker v-model:value="modelRef.periodicDuration" showTime class="w-full" />
      </FormItem>
      <FormItem v-if="modelRef.triggerType === triggerType.Periodic" v-bind="validateInfos.cycleWeekdays" class="ml-100px w-full">
        <CheckboxGroup v-model:value="modelRef.cycleWeekdays" class="w-full flex">
          <Checkbox :value="1">
            周一
          </Checkbox>
          <Checkbox :value="2">
            周二
          </Checkbox>
          <Checkbox :value="3">
            周三
          </Checkbox>
          <Checkbox :value="4">
            周四
          </Checkbox>
          <Checkbox :value="5">
            周五
          </Checkbox>
          <Checkbox :value="6">
            周六
          </Checkbox>
          <Checkbox :value="7">
            周日
          </Checkbox>
        </CheckboxGroup>
      </FormItem>
      <FormItem label="适用分支" v-bind="validateInfos.streamIDs">
        <Select v-model:value="modelRef.streamIDs" mode="multiple" :options="streamsList" :fieldNames="{ label: 'description', value: 'id' }" />
      </FormItem>
    </Form>
  </div>
</template>

<script lang="ts" setup>
import { Checkbox, Form, FormItem, Input, Radio, RadioGroup, RangePicker, Select } from 'ant-design-vue';
import { computed, reactive } from 'vue';
import { triggerType } from './annnouncement.data';
import type { AnnouncementsItem, StreamsListItem } from '../../api';
import dayjs from 'dayjs';

defineProps<{
  streamsList: StreamsListItem[];
}>();

const CheckboxGroup = Checkbox.Group;
const labelCol = { span: 4 };
const wrapperCol = { span: 20 };
const useForm = Form.useForm;
const modelRef = reactive({
  content: '',
  triggerType: triggerType.Once,
  onceDuration: undefined as [dayjs.Dayjs, dayjs.Dayjs] | undefined,
  periodicDuration: undefined as [dayjs.Dayjs, dayjs.Dayjs] | undefined,
  cycleWeekdays: [],
  streamIDs: [] as number[],
});
const rulesRef = reactive({
  content: [
    {
      required: true,
      message: '请选择公告内容',
    },
  ],
  triggerType: [
    {
      required: true,
      message: '请选择触发类型',
    },
  ],
  onceDuration: [
    {
      required: computed(() => modelRef.triggerType === triggerType.Once),
      message: '请选择持续时间',
    },
  ],
  periodicDuration: [
    {
      required: computed(() => modelRef.triggerType === triggerType.Periodic),
      message: '请选择持续时间',
    },
  ],
  triggerWeekDay: [
    {
      required: computed(() => modelRef.triggerType === triggerType.Periodic),
      message: '请选择持续时间',
    },
  ],
  streamIDs: [
    {
      required: true,
      message: '请选择适用分支',
    },
  ],
});
const { validate, validateInfos } = useForm(modelRef, rulesRef);

async function getParams() {
  await validate();
  const params: AnnouncementsItem = {
    content: modelRef.content,
    triggerType: modelRef.triggerType,
    streamIDs: modelRef.streamIDs,
  };
  if (modelRef.triggerType === triggerType.Once) {
    params.startTimestamp = dayjs(modelRef.onceDuration?.[0]).valueOf();
    params.endTimestamp = dayjs(modelRef.onceDuration?.[1]).valueOf();
  } else {
    const weekList = [false, false, false, false, false, false, false];
    modelRef.cycleWeekdays.forEach((item) => {
      weekList[item - 1] = true;
    });
    params.startTimestamp = dayjs(modelRef.periodicDuration?.[0]).valueOf();
    params.endTimestamp = dayjs(modelRef.periodicDuration?.[1]).valueOf();
    params.cycleWeekdays = weekList;
  }
  return params;
}
function setParams(item: AnnouncementsItem) {
  modelRef.content = item.content || '';
  modelRef.triggerType = item.triggerType || triggerType.Once;
  modelRef.streamIDs = item.streamIDs || [];
  if (item.triggerType === triggerType.Once) {
    modelRef.onceDuration = [dayjs(item.startTimestamp), dayjs(item.endTimestamp)];
  } else {
    modelRef.periodicDuration = [dayjs(item.startTimestamp), dayjs(item.endTimestamp)];
    item.cycleWeekdays?.forEach((item, index) => {
      if (item) {
        modelRef.cycleWeekdays.push(index + 1);
      }
    });
  }
}

defineExpose({
  getParams,
  setParams,
});
</script>
