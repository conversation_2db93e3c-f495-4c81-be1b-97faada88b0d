<template>
  <div v-track:v="'rnkyg3ayzt'" :class="prefixCls" :style="{ maxHeight: `calc(100vh - ${headerHeightRef}px)` }">
    <div :class="`${prefixCls}__left`">
      <div class="!h-[30px]">
        <Icon icon="gameStore-roof-curtain|svg" :size="200" class="!h-inherit" />
      </div>
      <AppProjectSelect v-if="isFullScreen" theme="dark" outsideHeader class="mt-3" popupBody />
      <div
        class="mt-2 flex flex-col gap-2 overflow-auto"
        :style="{ height: `calc(100vh - ${headerHeightRef + 30 + (isFullScreen ? 64 : 0)}px)` }"
      >
        <AEmpty v-if="!packageListWithPkg?.length" :image="AEmpty.PRESENTED_IMAGE_SIMPLE" description="暂无分支数据" />
        <template v-if="packageListWithPkg?.length === 1 && packageListWithPkg[0].is_default">
          <div
            v-for="pkg in packageListWithPkg[0].game_packages" :key="pkg.ID" v-track="'io7vpmwrbw'" :class="`${prefixCls}__left-tab`"
            :active="activeTab === pkg.ID" @click="handleTabChange(pkg)"
          >
            <img :src="pkg.icon" alt="" :class="`${prefixCls}__left-tab-icon`">

            <ATypographyText
              :class="`${prefixCls}__left-tab-title`" :ellipsis="{ tooltip: true }"
              :content="pkg.briefName || pkg.name"
            />
          </div>
        </template>
        <template v-else>
          <a-collapse v-model:activeKey="activeKey" ghost expandIconPosition="end" :class="`${prefixCls}__packageListWithPkg-collapse`">
            <template #expandIcon="{ isActive }">
              <Icon icon="arrow-down|svg" size="8" class="collapse-arrow" :class="!isActive ? 'transform-rotate-z--90deg' : 'transform-rotate-z-0deg'" />
            </template>
            <a-collapse-panel v-for="WithPkg in packageListWithPkg" :key="WithPkg.ID">
              <template #header>
                <span class="font-bold">
                  {{ WithPkg.name }}
                </span>
              </template>
              <div
                v-for="pkg in WithPkg.game_packages" :key="pkg.ID" v-track="'io7vpmwrbw'" :class="`${prefixCls}__left-tab  mb-2`"
                :active="activeTab === pkg.ID" @click="handleTabChange(pkg)"
              >
                <img :src="preprocessFilePath(pkg.icon)" alt="" :class="`${prefixCls}__left-tab-icon`">

                <ATypographyText
                  :class="`${prefixCls}__left-tab-title`" :ellipsis="{ tooltip: true }"
                  :content="pkg.briefName || pkg.name"
                />
              </div>
            </a-collapse-panel>
          </a-collapse>
        </template>
      </div>
    </div>
    <div :class="`${prefixCls}__right`">
      <div :class="`${prefixCls}__right-filter`">
        <div
          class="w-full flex flex-col-reverse items-start gap-y-4 2xl:flex-row justify-start! 2xl:justify-between"
        >
          <div class="flex flex-1 flex-wrap items-center gap-[16px]">
            <div v-if="!mtl" class="flex items-center">
              <span class="mr-3 font-bold">平台:</span>
              <TagSelect
                v-model="searchParams.platform" :options="showPlatformOptions" :disabled="!!selectMode"
                :disabledFunc="disabledFunc" trackText="klrdrxm2nx" @change="getCardList"
              />
            </div>
            <div class="flex items-center">
              <span class="mr-3 font-bold">包体检测:</span>
              <TagSelect
                v-model="searchParams.doctor" :options="cloneDoctorOptions" :disabled="!!selectMode"
                :disabledFunc="disabledFunc" trackText="klrdrxm2nx" @change="getCardList"
              />
            </div>
            <div :class="`flex items-center ${prefixCls}__pack-color`">
              <span class="mr-3 font-bold">包体颜色:</span>
              <TagSelect
                v-model="searchParams.colors" :options="allColorList" :disabled="!!selectMode"
                :disabledFunc="disabledFunc" :isMultiple="true" @change="getCardList"
              >
                <template #label="{ item }">
                  <div class="h-[12px] w-[12px] border b-rd-[4px]" :style="{ background: colorList[item][isDark ? 2 : 0], borderColor: colorList[item][isDark ? 3 : 1] }" />
                </template>
              </TagSelect>
            </div>
          </div>
          <div v-if="!mtl" :class="`${prefixCls}__right-filter-btn-list`">
            <div v-if="hasAccessPackageInspectionReportPermission" :class="`${prefixCls}__right-filter-btn`" @click="handleTrendModal">
              <Icon icon="icon-park-outline:chart-line" class="mr-1" />
              包体检测趋势
            </div>
            <div v-if="hasAccessPackageInspectionReportPermission" :class="`${prefixCls}__right-filter-btn-delete`">
              <div
                v-track="'mj33sh432b'" :class="`${prefixCls}__right-filter-btn`"
                @click="handleSelectMode('compare')"
              >
                <Icon icon="icon-park-outline:switch-contrast" class="mr-1 c-FO-Functional-Warning1-Default" />
                对比{{
                  selectVersionList.length && selectMode === 'compare'
                    ? `: ${selectVersionList.length}`
                    : ''
                }}
              </div>
              <div v-if="selectMode === 'compare'" class="mx-2 flex items-center font-bold">
                <div class="mr-2 cursor-pointer select-none" @click="handleCompareVersions()">
                  确认
                </div>
                <div class="cursor-pointer select-none" @click="handleSelectMode('')">
                  取消
                </div>
              </div>
            </div>
            <div v-if="hasEditBranchPermission" :class="`${prefixCls}__right-filter-btn-delete`">
              <div v-track="'elowptffpe'" :class="`${prefixCls}__right-filter-btn`" @click="handleSelectMode('delete')">
                <Icon icon="icon-park-outline:delete" class="mr-1 c-FO-Functional-Error1-Default" />
                包体删除{{
                  selectVersionList.length && selectMode === 'delete'
                    ? `: ${selectVersionList.length}`
                    : ''
                }}
              </div>
              <div v-if="selectMode === 'delete'" class="mx-2 flex items-center font-bold">
                <div class="mr-2 cursor-pointer select-none" @click="handleDeleteVersions()">
                  确认
                </div>
                <div class="cursor-pointer select-none" @click="handleSelectMode('')">
                  取消
                </div>
              </div>
            </div>
            <div v-if="hasEditBranchPermission" :class="`${prefixCls}__right-filter-btn`" @click="goToSettingsPage">
              <Icon icon="icon-park-outline:setting-config" class="mr-1" />
              配置
            </div>
          </div>
        </div>
        <div v-if="allLabels?.length" class="mt-4 flex flex-1 flex-wrap items-center">
          <span class="mr-3 w-[33px] self-start font-bold leading-6">标签:</span>
          <ARow :gutter="[16, 16]" class="w-0 flex-1 !mx-0">
            <ACol v-for="label in allLabels" :key="label.ID" class="!flex">
              <TagSelect
                v-model="searchParams.labels[label.ID!]" :options="label.values" :disabled="!!selectMode"
                trackText="klrdrxm2nx" :isMultiple="!label.singleChoice"
                :fieldNames="{ label: 'value', value: 'ID' }" :disabledFunc="disabledFunc" @change="getCardList"
              >
                <template #itemBefore>
                  <span
                    :style="{ color: label.color }" class="mr-2 text-xs"
                    :class="{ 'ml-[6px]': !!label.singleChoice }"
                  >{{ label.name }}</span>
                </template>
              </TagSelect>
            </ACol>
          </ARow>
        </div>
      </div>
      <div :class="`${prefixCls}__right-content`">
        <div class="absolute right-[16px] top-[16px]">
          <a class="c-FO-Content-Text1 font-bold" @click="handleDeptApply">
            借用移动设备
            <RightOutlined />
          </a>
        </div>
        <template v-if="activeTab">
          <CardList v-bind="getCardListProps" ref="topCardListRef" isTop :showEmpty="false" />
          <CardList v-bind="getCardListProps" ref="cardListRef" :showEmpty="false" />
        </template>
      </div>
    </div>
    <TrendModal @register="registerTrendModal" />
  </div>
</template>

<script lang="ts" setup>
import {
  Col as ACol,
  Empty as AEmpty,
  Row as ARow,
  TypographyText as ATypographyText,
} from 'ant-design-vue';
import { cloneDeep, map, union } from 'lodash-es';
import { computed, h, nextTick, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import CardList from './CardList.vue';
import type {
  GameLabelsListItem,
  GamePackagesListItem,
  GamePackagesVersionListSearchParamsModel,
  GamePackagesVersionsListItem,
  PkgClassWithpkgListItem,
} from '/@/api/page/model/testModel';
import {
  batchDeleteGamePackagesVersion,
  getCloudGameDeployStatus,
  getGameLabelsListByPage,
  getGamePackagesFiltersListByPage,
  getGameStoreColorList,
  getPkgClassListWithPkg,
} from '/@/api/page/test';
import { RightOutlined } from '@ant-design/icons-vue';
import { AppProjectSelect } from '/@/components/Application';
import Icon from '/@/components/Icon';
import { useModal } from '/@/components/Modal';
import TagSelect from '/@/components/TagSelect';
import { useTrack } from '/@/hooks/system/useTrack';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useLayoutHeight } from '/@/layouts/default/content/useContentViewHeight';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { openWindow } from '/@/utils';
import { isNullOrUnDef } from '/@/utils/is';
import TrendModal from '/@/views/test/gamePackage/doctor/trend/TrendModal.vue';
import { doctorOptions } from '/@/views/test/gamePackage/settings/branch/branch.data';
import { platformOptions } from '/@/views/test/gamePackage/settings/settings.data';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import { colorList } from './color.data';
import { useDarkModeTheme } from '/@/hooks/setting/useDarkModeTheme';
import { PlatformEnterPoint, preprocessFilePath } from '@hg-tech/oasis-common';
import { useRouteQuery } from '@vueuse/router';
import { usePermissionCheckPoint } from '/@/service/permission/usePermission.ts';
import { sendEvent } from '/@/service/tracker/index.ts';
import { GamePackageCenterTrackOperation, GamePackageCenterTrackTrigger } from '/@/api/page/model/testModel';
import { useLatestPromise } from '@hg-tech/utils-vue';

const { prefixCls } = useDesign('game-package-list');
const userStore = useUserStoreWithOut();

const router = useRouter();
const [registerTrendModal, { openModal: openTrendModal }] = useModal();
const { createMessage, createConfirm } = useMessage();
const { headerHeightRef } = useLayoutHeight();
const { setTrack } = useTrack();
const isFullScreen = useRouteQuery('fs', undefined, { transform: (v) => v === '1' });
const urlBranch = useRouteQuery('b', undefined, { transform: (v) => (!isNullOrUnDef(v) ? Number(v) : undefined) });
const downloadNow = useRouteQuery('downloadNow', undefined, { transform: (v) => (!isNullOrUnDef(v) ? Number(v) : undefined) });
const activeTab = ref<number>();
const packageListWithPkg = ref<PkgClassWithpkgListItem[]>([]);
const platforms = ref<number[]>();
const allLabels = ref<GameLabelsListItem[]>([]);
const clonePlatformOptions = cloneDeep(platformOptions);
const cloneDoctorOptions = cloneDeep(doctorOptions);
const topCardListRef = ref();
const cardListRef = ref<InstanceType<typeof CardList>>();
const selectVersionList = ref<GamePackagesVersionsListItem[]>([]);
const selectMode = ref<string>('');
const allColorList = ref<number[]>([]);
const { isDark } = useDarkModeTheme();
const activeKey = ref<number[]>();
const oasis = useRouteQuery('oasis', 0, { transform: (v) => (!isNullOrUnDef(v) ? Number(v) : 0) });
const mtl = useRouteQuery('mtl', 0, { transform: (v) => (!isNullOrUnDef(v) ? Number(v) : 0) });
const urlPlatform = useRouteQuery('platform', 0, { transform: (v) => (!isNullOrUnDef(v) ? Number(v) : 0) });
const searchParams = ref<GamePackagesVersionListSearchParamsModel>({
  platform: undefined,
  doctor: undefined,
  labels: {},
  colors: [],
});
const [hasEditBranchPermission] = usePermissionCheckPoint({
  scope: PlatformEnterPoint.GamePackage,
  any: ['edit_branch'],
}, { skipWhenSuperAdmin: true });

const [hasAccessPackageInspectionReportPermission] = usePermissionCheckPoint({
  scope: PlatformEnterPoint.GamePackage,
  any: ['access_package_report'],
}, { skipWhenSuperAdmin: true });

// 获取分支下云游戏部署状态
const { data: cloudGameDeployInfo, execute: getCloudGameDeployInfo } = useLatestPromise(getCloudGameDeployStatus);

const getCardListProps = computed(() => ({
  pkgID: activeTab.value,
  platforms: platforms.value,
  packageList: packageListWithPkg.value.map((item) => item.game_packages).reduce((a, b) => {
    return a.concat(b);
  }),
  searchParams: searchParams.value,
  allLabels: allLabels.value,
  selectMode: selectMode.value,
  onSuccess: handleSuccess,
  onCheckChange: handleCheckChange,
  onDeployChange: () => {
    if (!userStore.getProjectId || !activeTab.value) {
      return;
    }
    getCloudGameDeployInfo(userStore.getProjectId, activeTab.value);
  },
  selectVersionList: selectVersionList.value,
  cloudGameDeployInfo: cloudGameDeployInfo.value,
}));

function getCardList() {
  sendEvent('game_package_center_operate_filter', {
    game_package_center_trigger_channel: !oasis.value ? GamePackageCenterTrackTrigger.Web : GamePackageCenterTrackTrigger.Oasis,
  });
  nextTick(async () => {
    await cardListRef.value?.getList();
    await topCardListRef.value?.getList();
  });
}

async function getLabels() {
  if (!userStore.getProjectId) {
    return;
  }

  const { list } = await getAllPaginationList((p) => getGameLabelsListByPage(userStore.getProjectId!, p));

  allLabels.value = list?.filter((e: GameLabelsListItem) => !!e.values?.length) || [];
}

function disabledFunc() {
  createMessage.warning('请先退出选择模式');
}

// 获取包体列表
async function getPackageList() {
  activeTab.value = undefined;

  if (!userStore.getProjectId) {
    return;
  }

  let { list } = await getAllPaginationList((p) => getPkgClassListWithPkg(userStore.getProjectId!, { ...p, showAll: true }));

  // 删除list的game_packages未空的项
  list = list.filter((item: PkgClassWithpkgListItem) => item.game_packages.length !== 0);

  if (list?.length > 0) {
    let findBranch: GamePackagesListItem | undefined;
    const authCacheActiveKey = userStore.getGamestoreOpenClassList;

    list.forEach((item: PkgClassWithpkgListItem) => {
      findBranch = item.game_packages.find((gamePackagesItem: GamePackagesListItem) => gamePackagesItem.ID === urlBranch.value);

      if (authCacheActiveKey) {
        const defaultKey = list.find((e: PkgClassWithpkgListItem) => e.is_default)?.ID as number;
        // 处理默认分支
        activeKey.value = (defaultKey && authCacheActiveKey.includes(defaultKey))
          ? authCacheActiveKey
          : [...new Set([...authCacheActiveKey, String(defaultKey)])];
      } else {
        packageListWithPkg.value = list;
        activeKey.value = list.map((item: PkgClassWithpkgListItem) => item.ID!);
      }
    });
    packageListWithPkg.value = list;

    activeTab.value = urlBranch.value || list[0].game_packages[0].ID;
    platforms.value = findBranch ? findBranch.platforms : list[0].game_packages[0].platforms;
  } else {
    packageListWithPkg.value = [];
    platforms.value = [];
  }
}

// 获取颜色标签备选项
async function getColorList() {
  if (!userStore.getProjectId || !activeTab.value) {
    return;
  }

  const res = await getGameStoreColorList(userStore.getProjectId, activeTab.value);

  allColorList.value = res.colors || [];
}

// 获取默认筛选条件
async function getFilterList(isEdit = false) {
  if (!userStore.getProjectId || !activeTab.value) {
    return;
  }

  if (downloadNow.value !== 1 && !isEdit) {
    const { list } = await getGamePackagesFiltersListByPage(
      userStore.getProjectId,
      activeTab.value!,
      {
        page: 1,
        pageSize: 1,
      },
    );

    searchParams.value.platform = urlPlatform.value || list?.[0]?.platform?.values?.[0];
    searchParams.value.colors = list?.[0]?.color?.values;
    searchParams.value.doctor = isNullOrUnDef(list?.[0]?.doctor?.values?.[0])
      ? undefined
      : list?.[0]?.doctor?.values?.[0] === 1;
    searchParams.value.labels = {};
    list?.[0]?.label?.filters?.forEach((e) => {
      const find = allLabels.value.find((l) => l.ID === e.label_id);

      searchParams.value.labels[e.label_id] = find?.singleChoice ? e.values[0] : e.values;
    });
  }
  await Promise.all(
    [
      getColorList(),
      getCloudGameDeployInfo(userStore.getProjectId, activeTab.value),
    ],
  );
  nextTick(async () => {
    await cardListRef.value?.getList();
    await topCardListRef.value?.getList();
  });
}

function handleTabChange(pkg: GamePackagesListItem) {
  sendEvent('game_package_center_switch_branch', {
    game_package_center_trigger_channel: !oasis.value ? GamePackageCenterTrackTrigger.Web : GamePackageCenterTrackTrigger.Oasis,
  });
  if (pkg.ID !== activeTab.value) {
    activeTab.value = pkg.ID;
    platforms.value = pkg.platforms;
    getFilterList();
  }
}

const showPlatformOptions = computed(() =>
  clonePlatformOptions.filter((e) => platforms.value?.includes(e.value)));

// 打开包体检测趋势图
function handleTrendModal() {
  openTrendModal(true, {
    pkgID: activeTab.value,
    projectID: Number(userStore.getProjectId),
    platforms: platforms.value,
    curPlatform: searchParams.value.platform,
  });
}

// 处理选择模式
function handleSelectMode(mode: string) {
  if (!mode) {
    selectVersionList.value = [];
  } else if (selectMode.value && mode !== selectMode.value) {
    createMessage.warning('请先退出当前选择模式');

    return;
  } else if (mode === selectMode.value) {
    return;
  }

  selectMode.value = mode;
}

// 删除所有选中的版本
async function handleDeleteVersions() {
  if (!selectVersionList.value.length) {
    createMessage.warning('请选择要删除的版本');

    return;
  }

  createConfirm({
    title: '确认将以下版本删除至回收站吗?',
    content: union(selectVersionList.value).map((e) => {
      return h('div', { innerText: `版本: ${e.version}`, class: 'font-bold break-all' });
    }),
    iconType: 'warning',
    okText: () => '确认',

    onOk: async () => {
      const res = await batchDeleteGamePackagesVersion(userStore.getProjectId!, activeTab.value!, {
        ids: map(selectVersionList.value, 'ID') as number[],
      }, { trigger: !oasis.value ? GamePackageCenterTrackTrigger.Web : GamePackageCenterTrackTrigger.Oasis, operation: mtl.value ? GamePackageCenterTrackOperation.CloudGame : (!oasis.value ? GamePackageCenterTrackOperation.Page : GamePackageCenterTrackOperation.Oasis) });

      if (res?.code !== 7) {
        createMessage.success('包体已删除至回收站');
        handleSelectMode('');
        setTrack('ncfapelzmw');
        handleSuccess();
      }
    },
  });
}

// 处理选择版本
function handleCheckChange(item: GamePackagesVersionsListItem) {
  if (item.checked) {
    selectVersionList.value.push(item);
  } else {
    selectVersionList.value = selectVersionList.value.filter((e) => e.ID !== item.ID);
  }
}

// 处理对比用例的跳转
function handleCompareVersions() {
  if (!selectVersionList.value.length) {
    createMessage.warning('请选择要对比的版本');

    return;
  } else if (selectVersionList.value.length < 2) {
    createMessage.warning('请至少选择两个版本进行对比');

    return;
  }
  sendEvent('game_package_center_compare_packages', {
    game_package_center_trigger_channel: !oasis.value ? GamePackageCenterTrackTrigger.Web : GamePackageCenterTrackTrigger.Oasis,

  });
  const { href } = router.resolve({
    name: PlatformEnterPoint.GamePackageDoctorCompare,
    params: {
      id: activeTab.value,
    },
    query: {
      fs: 1,
      ids: map(selectVersionList.value, 'doctorID'),
      p: userStore.getProjectId,
    },
  });

  openWindow(href);
  setTrack('5gwnhm8mff');
  handleSelectMode('');
}

function handleDeptApply() {
  sendEvent('game_package_center_redirect_borrow', {
    game_package_center_trigger_channel: !oasis.value ? GamePackageCenterTrackTrigger.Web : GamePackageCenterTrackTrigger.Oasis,

  });
  const cloudDeviceLink = router.resolve({
    name: PlatformEnterPoint.DeptAssetApplyManagement,
    query: {
      p: userStore.getProjectId,
    },
  }).href;
  if (oasis.value) {
    window.open(`oasisdownload://open-browser?url=${encodeURIComponent(preprocessFilePath(cloudDeviceLink))}`, '_blank');
  } else {
    window.open(cloudDeviceLink, '_blank');
  }
}

async function handleSuccess(isEdit = false) {
  handleSelectMode('');
  await getLabels();
  await getFilterList(isEdit);
}

async function init() {
  await getPackageList();
  await handleSuccess();
}

function goToSettingsPage() {
  sendEvent('game_package_center_enter_config', {
    game_package_center_trigger_channel: !oasis.value ? GamePackageCenterTrackTrigger.Web : GamePackageCenterTrackTrigger.Oasis,

  });
  router.push({
    name: PlatformEnterPoint.GamePackageSettings,
  });
}

onMounted(async () => {
  await init();
});

watch(() => activeKey.value, (newValue, oldValue) => {
  if (oldValue && newValue) {
    sendEvent(newValue?.length > oldValue.length ? 'game_package_center_expand_branch' : 'game_package_center_collapse_branch', {
      game_package_center_trigger_channel: !oasis.value ? GamePackageCenterTrackTrigger.Web : GamePackageCenterTrackTrigger.Oasis,

    });
  }
  userStore.setGamestoreOpenClassList(activeKey.value || []);
});
watch(() => userStore.getProjectId, () => {
  sendEvent('game_package_center_switch_project', {
    game_package_center_trigger_channel: !oasis.value ? GamePackageCenterTrackTrigger.Web : GamePackageCenterTrackTrigger.Oasis,

  });
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-game-package-list';
@tag-prefix-cls: ~'hypergryph-tag-select';
.@{prefix-cls} {
  display: flex;
  position: relative;
  overflow: auto;
  &__packageListWithPkg-collapse {
    .ant-collapse-header {
      align-items: center;
      padding: 0 !important;
    }
    .ant-collapse-header-text {
      margin-inline-end: 0 !important;
      flex: none !important;
    }
    .ant-collapse-expand-icon {
      padding-left: 5px !important;
    }
    .ant-collapse-content-box {
      padding: 16px 0 !important;
    }
  }
  &__pack-color {
    & .@{tag-prefix-cls} {
      .@{tag-prefix-cls}__item {
        padding: 0 5px !important;
      }
    }
  }

  &__color-filter {
    background-color: @report-card-background;

    padding: 5px;
  }
  &__left {
    display: flex;
    position: sticky;
    top: 0;
    flex-direction: column;
    width: 220px;
    margin-left: 20px;
    padding: 20px 16px 16px 0;

    &-tab {
      position: relative;
      padding: 8px 12px;
      cursor: pointer;
      user-select: none;

      &[disabled='true'] {
        background-color: transparent !important;
        cursor: not-allowed;
      }

      &[active='true'],
      &:hover {
        border-radius: 20px;
        background-color: @FO-Container-Fill1;
      }

      &-icon {
        position: absolute;
        top: 10px;
        left: 12px;
        width: 20px;
        height: 20px;
        border-radius: 6px;
      }

      &-title {
        width: 150px;
        margin-left: 26px;
        font-weight: bold;
      }
    }
  }

  &__right {
    flex: auto;
    margin-right: 20px;
    padding: 20px 0;
    display: flex;
    flex-direction: column;

    &-filter {
      flex: none;
      margin-bottom: 10px;
      padding: 16px;
      border-radius: 10px;
      background-color: @FO-Container-Fill1;

      &-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 2px 10px;
        border-radius: 16px;
        border-color: @forgeon-btn-normal-bg-color;
        background-color: @forgeon-btn-normal-bg-color;
        color: @forgeon-btn-normal-text-color;
        cursor: pointer;
        user-select: none;

        &-delete {
          display: inline-flex;
          align-items: center;
          margin: 0 8px;
          border-radius: 16px;
          background-color: @table-head-border;
        }
      }
    }

    &-content {
      flex: auto;
      position: relative;
      border-radius: 10px;
      background-color: @FO-Container-Fill1;
    }
  }
  .collapse-arrow {
    transition: all 0.1s;
  }
}

[data-theme='dark'] .@{prefix-cls} {
  & img {
    filter: brightness(0.8);
  }
}
</style>
