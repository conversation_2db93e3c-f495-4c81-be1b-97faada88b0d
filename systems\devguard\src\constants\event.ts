/**
 * 定义事件名称的枚举, 事件名称的格式为: 模块名_事件名
 * @see https://dataark.hypergryph.net/area/11/app/app_manager/event_manager?appId=7l6k0y2vw4rksevp6bibwuht
 */
enum TrackEventName {
  // TODO: 请替换为实际的事件名称
  SUBMIT_CENTER_SUBMIT_RECORD_FILTER_CLICK = 'submit_center_submit_record_filter_click',
  SUBMIT_CENTER_SUBMIT_RECORD_SEARCH_TRIGGER = 'submit_center_submit_record_search_trigger',
  SUBMIT_CENTER_SUBMIT_RECORD_REVIEW_CLICK = 'submit_center_submit_record_review_click',
  SUBMIT_CENTER_SUBMIT_RECORD_APPROVAL_MODAL_TRIGGER = 'submit_center_submit_record_approval_modal_trigger',
  SUBMIT_CENTER_SUBMIT_RECORD_APPROVAL_COMPLETE = 'submit_center_submit_record_approval_complete',
  SUBMIT_CENTER_INSTANCE_STATUS_TYPE_SWITCH = 'submit_center_instance_status_type_switch',
  SUBMIT_CENTER_INSTANCE_DETAIL_CLICK = 'submit_center_instance_status_submit_detail_view',
  SUBMIT_CENTER_INSTANCE_CONFIG_BULK_UPDATE_CLICK = 'submit_center_instance_config_bulk_update_click',
  SUBMIT_CENTER_INSTANCE_CONFIG_BULK_RESTART_CLICK = 'submit_center_instance_config_bulk_restart_click',
  SUBMIT_CENTER_INSTANCE_CONFIG_ALLOCATION_SAVE = 'submit_center_instance_config_allocation_save',
  SUBMIT_CENTER_INSTANCE_CONFIG_OPERATION_LOG_CLICK = 'submit_center_instance_config_operation_log_click',
  SUBMIT_CENTER_INSTANCE_CONFIG_EDIT_SAVE = 'submit_center_instance_config_edit_save',
  SUBMIT_CENTER_INSTANCE_CONFIG_SINGLE_UPDATE_CLICK = 'submit_center_instance_config_single_update_click',
  SUBMIT_CENTER_INSTANCE_CONFIG_SINGLE_RESTART_CLICK = 'submit_center_instance_config_single_restart_click',
  SUBMIT_CENTER_INSTANCE_CONFIG_SINGLE_DISABLE_CLICK = 'submit_center_instance_config_disable_click',
  SUBMIT_CENTER_INSTANCE_CONFIG_SCHEDULE_ADD = 'submit_center_instance_config_schedule_add',
  SUBMIT_CENTER_INSTANCE_CONFIG_SCHEDULE_MODIFY = 'submit_center_instance_config_schedule_modify',
  SUBMIT_CENTER_INSTANCE_CONFIG_SCHEDULE_DELETE = 'submit_center_instance_config_schedule_delete',
}

/**
 * 自定义事件参数类型
 */
interface TodoEventParams {
  search_keyword: string;
}

/**
 * 定义泛型类型来获取事件的入参类型，默认无参数，也可手动添加默认参数类型
 */
type TrackEventParams<T extends TrackEventName> =
  T extends TrackEventName.SUBMIT_CENTER_SUBMIT_RECORD_SEARCH_TRIGGER
    ? TodoEventParams
    : never;

export {
  TrackEventName,
};
export type { TrackEventParams };
